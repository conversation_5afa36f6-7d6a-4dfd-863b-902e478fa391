package com.tmb.oneapp.commonpaymentexp.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.io.Serializable;


@Getter
@AllArgsConstructor
public enum ResponseCode implements Serializable {

    //Success
    SUCCESS("0000", "success"),
    SUCCESS_V2("000000", "success"),

    //Business error (HTTP : 200)
    NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR("030005", "No eligible deposit account"),
    NO_ELIGIBLE_CREDIT_CARD_ACCOUNT_ERROR("030006", "No eligible credit card account"),
    DEPOSIT_DORMANT_ACCOUNT_ERROR("030007", "Dormant account"),
    PRODUCT_EXPIRED_ERROR("030008", "Product expired"),
    AUTHORIZE_FAILED("030009", "Authorize common authentication failed"),
    UNAVAILABLE_SERVICE_HOUR("030010", "This service is unavailable from %s. Please try again during service hours."),
    BILLER_EXPIRED("030011", "This biller is expired"),
    CREDIT_CARD_ACCOUNT_INVALID_ERROR("030014", "Credit card account invalid"),
    DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR("030012", "Insufficient funds (CASA)"),
    CREDIT_CARD_INSUFFICIENT_FUNDS_ERROR("030013", "Insufficient funds (CC)"),
    ETE_ERROR("030022", "Should Map Massage Error ETE here."),
    EXCLUDE_BILLER_ERROR("030016", "This biller is not allowed to pay on this channel"),
    DAILY_LIMIT_EXCEEDED("030015","Daily limit exceeded"),
    INCORRECT_AIA_REF("030017", "Ref1 is incorrect. Please try again."),
    INCORRECT_DLT_REF("030018", "Invalid reference no.1 or no.2 or amount."),
    INCORRECT_DLT_EXCEEDED_DUD_DATE("030019", "Ref1 or Ref2 is incorrect. Please try again."),
    INCORRECT_TOYOTA_REF("030020", "Ref1, Ref2 is incorrect. Please try again."),
    BILL_PAY_TOP_UP_DUPLICATE_REF("030021", "Duplicate references found"),
    CIRCUIT_BREAK_ERROR("030023", "Circuit break error"),
    INCORRECT_AUTO_LOAN_REF_1("030024", "Ref1 is incorrect. Please try again."),
    INCORRECT_AUTO_LOAN_REF_2("030025", "Ref2 is incorrect. Please try again."),
    ACCRUED_DEBT_ERROR("030028", "PAY MORE THAN ACCRUED DEBT"),
    BILL_PROMPT_PAY_SCAN_QR_BILLER_NOT_ALLOW_PAY_WITH_QR("030035", "It is not possible to pay this biller via QR code."),
    INVALID_WOW_POINT("030037", "The WOW Point discount is not valid for this transaction"),
    FAIL_TO_REDEEM_WOW_POINT("030038", "Should map with ete/loyalty error response"),
    ALREADY_POST_AUTHENTICATE_TRANSACTION("030039", "Transaction ID has already been processed after authentication"),
    PB_UPDATE_APP("pb_update_app", "To continue using application and transacting safely, please update your ttb touch to the latest version."),

    //Technical Error
    TRANSACTION_DUPLICATE_ERROR("130024", "Transaction cannot be processed because the ID has already been used."),
    TRANSACTION_NOT_FOUND_ERROR("130002", "Transaction not found"),
    MISSING_REQUIRED_FIELD_ERROR("130003", "Missing required field"),
    MISSING_CONFIGURATION_ERROR("130004", "Missing configuration error"),
    NOT_MATCH_PAYMENT_TYPE_ERROR("130025", "Not match payment type"),
    TOGGLE_CONFIG_NOT_FOUND_ERROR("130026", "Toggle config not found"),
    MASTER_BILLER_NOT_FOUND_ERROR("130027", "Cannot find master biller"),
    MEA_AMOUNT_NOT_MATCH_ERROR("130028", "MEA amount not match with request amount"),
    PEA_AMOUNT_NOT_MATCH_ERROR("130029", "PEA amount not match with request amount"),
    GRAND_AMOUNT_NOT_FOUND_ERROR("130030", "Grand amount not found"),
    MWA_AMOUNT_NOT_FOUND_ERROR("130031", "MWA amount not found"),
    NOT_ALLOW_COMMON_PAYMENT_ERROR("130032","Not allow common payment" ),
    AL_REF2_MISS_MATCH_ERROR("130033", "Ref2 not match with request"),
    AL_REF2_MISSING("130034", "Ref2 is missing"),
    JWS_GENERATION_FAILED("030035", "JWS generation failed"),
    JWE_ENCRYPTION_FAILED("030036", "JWE encryption failed"),
    JWE_DECRYPTION_FAILED("030041", "JWE decryption failed"),
    JWS_VERIFICATION_FAILED("030038", "JWS verification failed"),
    JWK_SET_NOT_FOUND("030042", "JWKSet not found for client"),
    JWK_KEY_NOT_FOUND("030040", "JWK with key id not found"),
    PAYLOAD_DESERIALIZATION_FAILED("030044", "Failed to deserialize decrypted payload"),
    PAYLOAD_SERIALIZATION_FAILED("030045", "Failed to serialize payload for signing"),
    JWK_NOT_CORRECT_FORMAT("030043","JWK not correct format"),
    JWK_INCORRECT_ALGORITHM("130039","Cannot decrypt because Incorrect algorithm"),
    INVALID_CALLBACK_URL_ERROR("130046", "Partner callback url not contain in Config."),

    //Please add new constant above. (for avoid conflict)
    FAILED_V2("100001", "Generic error"),
    FAILED("0001", "Unknown Error");

    private final String code;
    private final String message;
    private final String service = Constants.SERVICE_NAME;
    private final String desc = null;

    private static class Constants {
        public static final String SERVICE_NAME = "common-payment-exp";
    }
}
