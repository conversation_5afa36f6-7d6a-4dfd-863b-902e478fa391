package com.tmb.oneapp.commonpaymentexp.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ProcessorConstant {

    // Bill pay and Top up processor
    public static final String BILLER_PAYMENT_TOPUP = "BILLER_PAYMENT_TOPUP";
    public static final String BILLER_PAYMENT_ONLINE_BILLER = "BILLER_PAYMENT_ONLINE_BILLER";
    public static final String BILLER_PAYMENT_TMB_AUTO_LOAN = "BILLER_PAYMENT_TMB_AUTO_LOAN";
    public static final String BILLER_PAYMENT_CREDIT_CARD = "BILLER_PAYMENT_CREDIT_CARD";
    public static final String BILLER_PAYMENT_TMB_LOAN = "BILLER_PAYMENT_TMB_LOAN";
    public static final String BILLER_PAYMENT_OFFLINE = "BILLER_PAYMENT_OFFLINE";
    public static final String BILLER_PAYMENT_BILL_PROMPT_PAY = "BILLER_PAYMENT_BILL_PROMPT_PAY";
    public static final String BILLER_PAYMENT_TOPUP_E_WALLET = "BILLER_PAYMENT_E_WALLET";
    public static final String BILLER_PAYMENT_MEA = "BILLER_PAYMENT_MEA";
    public static final String BILLER_PAYMENT_MWA = "BILLER_PAYMENT_MWA";
    public static final String BILLER_PAYMENT_PEA = "BILLER_PAYMENT_PEA";
    public static final String BILLER_PAYMENT_FLEET_CARD = "BILLER_PAYMENT_FLEET_CARD";
    public static final String BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP = "BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP";
    public static final String BILLER_PAYMENT_TOP_UP_M_PAY = "TOPUP_M_PAY"; // Deprecated

    // Schedule processor
    public static final String PROCESSOR_SCHEDULE_UNIMPLEMENTED = "PROCESSOR_SCHEDULE_UNIMPLEMENTED";
}
