package com.tmb.oneapp.commonpaymentexp.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonPaymentExpConstant {
    // HEADER
    public static final String HEADER_CORRELATION_ID = "X-Correlation-ID";
    public static final String HEADER_CRM_ID = "X-crmId";
    public static final String HEADER_X_PARTNER_NAME = "X-Partner-Name";
    public static final String HEADER_TRANSACTION_ID = "X-Transaction-ID";
    public static final String HEADER_CRM_ID_FOR_COMMON_AUTHEN = "crm-id";
    public static final String HEADER_CLIENT_VERSION = "Client-Version";
    public static final String HEADER_IP_ADDRESS = "X-Forward-For";
    public static final String HEADER_OS_VERSION = "os-version";
    public static final String HEADER_CHANNEL = "channel";
    public static final String HEADER_APP_VERSION = "app-version";
    public static final String HEADER_DEVICE_ID = "device-id";
    public static final String HEADER_DEVICE_MODEL = "device-model";
    public static final String REQUEST_UID = "request-uid";
    public static final String HEADER_X_FORWARDED_CORRELATION_ID = "x-forwarded-correlation-id";
    public static final String HEADER_PRE_LOGIN = "PRE_LOGIN";
    public static final String HEADER_ACCEPT_LANGUAGE = "Accept-Language";
    public static final String HEADER_X_IN_APP_WEBVIEW = "X-In-App-Webview";

    //Activity log
    public static final String ACTIVITY_SUCCESS = "success";
    public static final String ACTIVITY_FAILURE = "failure";

    //Bill-pay Common-payment
    public static final String BILL_PAYMENT_VERIFY_SERVICE_NAME = "bill-payment-verify";
    public static final String BILL_ADDITIONAL_PARAM_KEY_NAME = "Msg";

    public static final String ONLINE_TRANS_REF_SEQUENCE = "online_trans_ref_sequence";
    public static final String PEA_TRANS_REF_SEQUENCE = "pea_trans_ref_sequence";
    public static final String BLANK = "";
    public static final String OCP_REQUEST_CHANNEL = "1MB";
    public static final String TTB_BANK_CODE_3DIGIT = "011";
    public static final String TOP_UP_BRANCH_ID = "0001";
    public static final String TOP_UP_CURRENCY = "THB";
    public static final String DATETIME_FORMAT = "yyyyMMddHHmmss";
    public static final String CREDIT_CARD_TOPUP_TXN_CODE = "8890";
    public static final String BANK_TMB_VALIDATE_DATEFORMAT = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String TRANSFER_REFERENCE_NUMBER_PREFIX = "financial_reference_id_sequence_";
    public static final Integer TOP_UP_REF_SEQUENCE_DIGIT = 8;
    public static final String BILL_TRAN_CODE_PREFIX = "88";
    public static final String BILLER_TRAN_CODE_FLEET_CARD_POSTFIX = "9";
    public static final String BILLER_TRAN_CODE_AUTO_LOAN_SUFFIX = "0";
    public static final String METHOD_PRODUCT_OF_TMB = "2";


    public static final String REQUEST_APP_ID = "request-app-id";
    public static final String REQUEST_APP_ID_OCP_GATEWAY_VALUE = "A0478-MB";
    public static final String REQUEST_DATE_TIME = "request-datetime";
    public static final String CONTENT_TYPE = "content-type";
    public static final String SERVICE_NAME = "service-name";
    public static final String CONTENT_TYPE_VALUE_WITH_UTF8 = "application/json;charset=UTF-8";
    public static final String APPLICATION_JSON = "application/json";


    public static final String BILL_PAYMENT_ACTIVITY_CONFIRM_STEP = "Confirm";
    public static final String TOP_UP_DEFAULT_FEE = "0.00";

    public static final String ACTIVITY_LOG_E_DONATION_VERIFY_ID = "101201010";
    public static final String ACTIVITY_LOG_BILL_PAY_VIA_CREDIT_CARD_VERIFY_ID = "100500210";
    public static final String ACTIVITY_LOG_BILL_PAY_VERIFY_ID = "100500110";

    public static final int MAX_TO_ACCT_ID = 10;

    public static final String ACTIVITY_LOG_VALIDATE_TOPUP_CREDIT_CARD = "100600310";
    public static final String ACTIVITY_LOG_TOP_UP_VERIFY_ID = "100600110";

    public static final String CATEGORY_E_DONATION_ID = "26";

    public static final String BILL_REF_1_ETE_REQUEST_AIS_ON_TOP = "00000000000";
    public static final String BILL_ID_NAME = "billId";
    public static final String BILL_PAYMENT_TOP_UP_CACHE_KEY = "bill_payment:topup:compcode:%s:crm:%s";
    public static final String TOP_UP_COMP_CODE_TRUE_MOVE_H_DATA_PACKAGE = "3069";

    public static final String Y_ALPHABET = "y";
    public static final String MB = "mb";
    public static final String ACTIVITY_LOG_BILL_PAY_VIA_CREDIT_CARD_CONFIRM_ID = "100500220";
    public static final String ACTIVITY_LOG_COMFIRM_TOPUP_CREDIT_CARD = "100600320";
    public static final String PAYMENT_METHOD_TMB_PRODUCT = "15";

    public static final String SCHEDULE_TRANSFER_TEMPLATE_VALUE = "oneapp-scheduled-transfer-complete";
    public static final String SCHEDULE_TOPUP_TEMPLATE_VALUE = "oneapp-scheduled-topup-billpayment-complete";
    public static final String LOCALE_TH = "th";

    public static final String DASH = "-";
    public static final String SR_NO = "srNo";
    public static final String FAIL_REASON = "failReason";
    public static final String FROM_ACCOUNT = "fromAccountId";
    public static final String AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE = "0000000";
    public static final String HP_EXP_SERVICE_ADD_SERVICE_REQUEST_ERROR_CODE = "*********";
    public static final String PAYMENT_STATUS = "paymentStatus";
    public static final String ERROR_DESC = "errorDesc";
    public static final String ACTIVITY_WOW_POINT_FORMAT = "%s (%s Points)";

    public static final String VERIFY_COMMON_AUTHEN_REF_ID_PREFIX_REDIS_KEY = "auth:common_authen:verify:ref_id:%s";

    // Channel and App constants
    public static final String CHANNEL = "channel";
    public static final String APP = "app";
}
