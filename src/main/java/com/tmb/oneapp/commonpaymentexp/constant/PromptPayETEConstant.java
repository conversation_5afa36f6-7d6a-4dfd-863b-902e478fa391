package com.tmb.oneapp.commonpaymentexp.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class PromptPayETEConstant {

    public static final String PROMPTPAY_REF_SEQ = "promptpay_ref_sequence";
    public static final String ALPHABET_I = "I";
    public static final String B011B = "B011B";
    public static final String TMBO = "TMBO";
    public static final String PAYMENT_QR_PROMPT_PAY = "PROMPTPAY";
    public static final String SENDER_TYPE_QR = "Q";
    public static final String TERMINAL_TYPE = "80";
    public static final String TTB_BANK_CODE = "11";
    public static final String PROXY_TYPE_E_WALLET = "04";
    public static final String BLANK = "";
    public static final String SENDER_TYPE_KEY_IN = "H";
    public static final String TMB_BANK_SHORT_NAME = "TMB";
    public static final String TRANS_ID_DATE_FORMAT = "yyyyMMddHHmmss";

}
