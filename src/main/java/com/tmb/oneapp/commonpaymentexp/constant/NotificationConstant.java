package com.tmb.oneapp.commonpaymentexp.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.text.DecimalFormat;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class NotificationConstant {
    public static final DecimalFormat COMMAS_FORMAT = new DecimalFormat("#,##0.00");
    public static final String PATTERN_DATE_WITH_TIME_TH = "d MMM yy - HH:mm น.";
    public static final String PATTERN_DATE_WITH_TIME_EN = "d MMM yy - h:mm a";
    public static final String TH_LOWER_CASE = "th";
    public static final String TH_UPPER_CASE = "TH";
    public static final String NOTIFICATION_CREDIT_CARD_METHOD_TH = "บัตรเครดิต";
    public static final String NOTIFICATION_CREDIT_CARD_METHOD_EN = "Credit Card";
    public static final String NOTIFICATION_ACCOUNT_METHOD_TH = "บัญชี";
    public static final String NOTIFICATION_ACCOUNT_METHOD_EN = "Account";

    // Template Notification
    public static final String NOTIFICATION_TEMPLATE_COMMON_PAYMENT_COMPLETE = "oneapp-common-payment-complete";
    public static final String NOTIFICATION_TEMPLATE_TOP_UP_BILL_PAYMENT_COMPLETE = "oneapp-topup-billpayment-complete";
}
