package com.tmb.oneapp.commonpaymentexp.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonAuthenticationConstant {
    public static final String COMMON_PAYMENT_TOP_UP_FEATURE_ID = "1021";
    public static final String COMMON_PAYMENT_BILL_PAY_FEATURE_ID = "1022";
    public static final String COMMON_PAYMENT_DIRECT_DEBIT_FEATURE_ID = "1023";
    public static final String COMMON_PAYMENT_MUTUAL_FUNDS_FEATURE_ID = "1024";
    public static final String COMMON_PAYMENT_VISA_FEATURE_ID = "1025";

    public static final String COMMON_AUTH_TOP_UP_FLOW_NAME = "top up";
    public static final String COMMON_AUTH_BILL_FLOW_NAME = "bill payment";
}
