package com.tmb.oneapp.commonpaymentexp.constant;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class BillerConstant {
    public static final String BILLER_GROUP_TYPE_BILL = "0";
    public static final String BILLER_GROUP_TYPE_TOP_UP = "1";
    public static final String BILLER_PAYMENT_METHOD_OFFLINE = "5";
    public static final String BILLER_METHOD_TMB_CREDIT_CARD = "2";
    public static final String PAYMENT_METHOD_TMB_PRODUCT = "15";
    public static final String BILLER_METHOD_TMB_LOAN = "3";
    public static final String BILLER_CATEGORY_CODE_CREDIT_CARD = "06";
    public static final String BILLER_CATEGORY_CODE_MOBILE = "07";
    public static final String BILLER_CATEGORY_EWALLET = "7";
    public static final String BILLER_ONLINE = "12";
    public static final String SPECIAL_OFFLINE_BILLER_AIA_0002 = "0002";
    public static final String SPECIAL_OFFLINE_BILLER_AIA_2095 = "2095";
    public static final String SPECIAL_OFFLINE_BILLER_TOYOTA_0287 = "0287";
    public static final String SPECIAL_OFFLINE_BILLER_TOYOTA_2193 = "2193";
    public static final String SPECIAL_OFFLINE_BILLER_TOYOTA_0840 = "0840";
    public static final String SPECIAL_OFFLINE_BILLER_TOYOTA_2192 = "2192";
    public static final String SPECIAL_OFFLINE_BILLER_LAND_TRANSPORT_0980 = "0980";

    // Biller CompCode
    public static final String BILL_COMP_CODE_TOP_UP_M_PAY = "2055";
    public static final String BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN = "5003";
    public static final String BILL_COMP_CODE_E_WALLET = "EW01";
    public static final String BILL_COMP_CODE_TTB_FLEET_CARD = "5004";
    public static final String BILL_COMP_CODE_MEA = "2533";
    public static final String BILL_COMP_CODE_PEA = "2700";
    public static final String BILL_COMP_CODE_MWA = "2699";
    public static final String BILL_COMP_CODE_EASY_PASS = "2151";
    public static final String BILL_COMP_CODE_FLEET_CARD = "0012";
    public static final String BILL_COMP_CODE_PWA_BARCODE = "2779";
    public static final String BILL_COMP_CODE_AIS_ON_TOP = "MP01";
    public static final String TOP_UP_COMP_CODE_TRUE_MOVE_H_DATA_PACKAGE = "3069";
    public static final String BILL_COMP_CODE_AIS_ON_TOP_PREPAID = "3070";
    public static final String BILL_COMP_CODE_AIS_ON_TOP_POSTPAID = "3071";
    public static final String BILL_COMP_CODE_HOME_LOAN = "AL02";
    public static final List<String> COMP_CODE_REQUIRED_REF_VALIDATION_LIST = List.of(
            TOP_UP_COMP_CODE_TRUE_MOVE_H_DATA_PACKAGE,
            BILL_COMP_CODE_AIS_ON_TOP_PREPAID,
            BILL_COMP_CODE_AIS_ON_TOP_POSTPAID
    );

    // Biller Call From
    public static final String BILLER_CALL_FROM_AUTO_LOAN = "autoLoan";
}
