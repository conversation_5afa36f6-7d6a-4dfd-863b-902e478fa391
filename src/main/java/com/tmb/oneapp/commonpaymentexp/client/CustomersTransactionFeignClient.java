package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.customertransaction.TriggerCacheRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "${feign.customers.transaction.service.name}", url = "${feign.customers.transaction.service.endpoint}")
public interface CustomersTransactionFeignClient {
    @PostMapping(value = "/apis/internal/customer-transaction/trigger", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<String>> clearCache(
            @RequestHeader(CommonPaymentExpConstant.HEADER_CORRELATION_ID) String correlationId,
            @RequestBody TriggerCacheRequest request);
}
