package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardFormatedResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;

@FeignClient(name = "${feign.retail.lending.biz.name}", url = "${feign.retail.lending.biz.url}")
public interface RetailLendingBizClient {

    @GetMapping(value = "/v1/retail-lending-biz/creditcard")
    ResponseEntity<TmbServiceResponse<CreditCardFormatedResponse>> getCreditCardWithSupplementary(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmID,
            @RequestHeader(name = "include-supplementary", required = false) String includeSupplementary,
            @RequestHeader(name = "use-in-cc", required = false) String useInCc,
            @RequestHeader(name = "use-to-verify", required = false) String useToVerify
    );
}
