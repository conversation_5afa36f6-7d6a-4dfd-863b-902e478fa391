package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.AccountSaving;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.billpayaccount.AccountBalanceRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;

@FeignClient(name = "${feign.customers.account.biz.service.name}", url = "${feign.customers.account.biz.service.url}")
public interface CustomerAccountBizClient {

    @GetMapping(value = "/v1/customer-account-biz/accounts-list", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<AccountSaving>> getAccountList(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId
    );

    @PostMapping(value = "/v1/customer-account-biz/account-balance", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbOneServiceResponse<List<DepositAccount>>> getAccountBalance(
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestBody List<AccountBalanceRequest> accountRequest);
}
