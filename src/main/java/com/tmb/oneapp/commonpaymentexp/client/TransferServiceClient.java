package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.TransferConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;

/**
 * Feign Client to connect Common Service
 */
@FeignClient(name = "${feign.transfer.service.name}", url = "${feign.transfer.service.url}")
public interface TransferServiceClient {

    @GetMapping(value = "/v1/transfer-service/configuration")
    ResponseEntity<TmbServiceResponse<TransferConfiguration>> getTransferModuleConfig(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID);
}
