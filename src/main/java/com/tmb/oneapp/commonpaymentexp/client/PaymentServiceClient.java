package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentRule;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AutoLoanOCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanOCPResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETERequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.creditcard.CreditCardConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpaymenttoggle.CommonPaymentToggle;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CLIENT_VERSION;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_OS_VERSION;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_TRANSACTION_ID;

@FeignClient(name = "${feign.payment.service.name}", url = "${feign.payment.service.url}")
public interface PaymentServiceClient {

    @GetMapping(value = "/v1/payment-service/common-payment/common-payment-rules/{type}", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<CommonPaymentRule>> fetchCommonPaymentRuleByType(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @PathVariable(value = "type") String type
    );

    @GetMapping(value = "/v1/payment-service/common-payment/common-payment-rules/{type}/{code}", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<CommonPaymentRule>> fetchCommonPaymentRuleByTypeAndCode(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @PathVariable(value = "type") String type,
            @PathVariable(value = "code") String code
    );

    @GetMapping("/v1/payment-service/billers/details/comp-code")
    @Operation(summary = "Get Biller Detail")
    ResponseEntity<TmbServiceResponse<MasterBillerResponse>> fetchMasterBillerByCompCode(
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId,
            @RequestParam(value = "compCode") String compCode
    );

    @GetMapping("/v1/payment-service/billers/details/comp-code")
    @Operation(summary = "Get Biller Detail")
    ResponseEntity<TmbServiceResponse<MasterBillerResponse>> fetchMasterBillerByCompCode(
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId,
            @RequestParam(value = "compCode") String compCode,
            @RequestHeader(HEADER_OS_VERSION) String osVersion,
            @RequestHeader(HEADER_CLIENT_VERSION) String clientVersion
    );

    @GetMapping(value = "/v1/payment-service/common-payment/common-payment-configs/{entry-id}", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<CommonPaymentConfig>> fetchCommonPaymentConfig(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @PathVariable(value = "entry-id") String entryId
    );

    @GetMapping(value = "/v1/payment-service/configuration/billpay", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<BillPayConfiguration>> fetchBillPayConfig(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID
    );

    @PostMapping(value = "/v1/payment-service/ete/billpay/ocp/validate", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<OCPBillPaymentResponse>> validateOcpPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestBody OCPBillRequest ocpBillRequest
    );

    @PostMapping(value = "/v1/payment-service/ete/billpay/ocp/confirm", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<OCPBillPaymentResponse>> confirmOcpPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_TRANSACTION_ID) String transactionId,
            @RequestBody OCPBillRequest ocpBillRequest
    );

    @PostMapping(value = "/v1/payment-service/ete/billpay/ocp/point/confirm", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<OCPBillPaymentResponse>> confirmOcpWowPointPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_TRANSACTION_ID) String transactionId,
            @RequestBody OCPBillRequest ocpBillRequest
    );

    @PostMapping(value = "/v1/payment-service/ete/billpay/ocp/point/home-loan/confirm", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<OCPBillPaymentResponse>> confirmOcpWowPointHomeLoanPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_TRANSACTION_ID) String transactionId,
            @RequestBody OCPBillRequest ocpBillRequest
    );

    @GetMapping(value = "/v1/payment-service/common-payment/common-payment-toggle", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<CommonPaymentToggle>> getCommonPaymentToggle(
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId
    );

    @PostMapping(value = "/v1/payment-service/ete/billpay/autoloan/validate", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<TopUpETEResponse>> validateAutoLoanPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestBody TopUpETEPaymentRequest topUpETEPaymentRequest
    );

    @PostMapping(value = "/v1/payment-service/ete/billpay/autoloan/confirm", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<TopUpETEResponse>> confirmAutoLoanPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_TRANSACTION_ID) String transactionId,
            @RequestBody TopUpETEPaymentRequest topUpETEPaymentRequest
    );

    @PostMapping(value = "/v1/payment-service/ete/billpay/ocp/point/auto-loan/confirm", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<AutoLoanOCPResponse>> confirmOcpWowPointAutoLoanPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_TRANSACTION_ID) String transactionId,
            @RequestBody AutoLoanOCPBillRequest autoLoanOCPBillRequest
    );

    @PostMapping(value = "/v1/payment-service/ete/billpay/prompt-pay/e-wallet/validate", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<EWalletETEResponse>> validateEWalletPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestBody EWalletETERequest request);

    @PostMapping(value = "/v1/payment-service/ete/billpay/prompt-pay/e-wallet/confirm", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<EWalletETEResponse>> confirmEWalletPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_TRANSACTION_ID) String transactionId,
            @RequestBody EWalletETERequest request);

    @PostMapping(value = "/v1/payment-service/ete/billpay/credit-card/confirm", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<CreditCardConfirmResponse>> confirmCreditCardPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_TRANSACTION_ID) String transactionId,
            @RequestBody CreditCardConfirmRequest request);

    @PostMapping(value = "/v1/payment-service/ete/billpay/prompt-pay/validate", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<PromptPayETEValidateResponse>> validateBillPromptPayPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestBody PromptPayETEValidateRequest request);

    @PostMapping(value = "/v1/payment-service/ete/billpay/prompt-pay/confirm", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<PromptPayETEConfirmResponse>> confirmBillPromptPayPayment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_TRANSACTION_ID) String transactionId,
            @RequestBody PromptPayETEConfirmRequest request);

    @PostMapping(value = "/v1/payment-service/ete/billpay/prompt-pay/iso20022/validate", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<PromptPayETEValidateResponse>> validateBillPromptPayISO20022Payment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestBody PromptPayETEValidateRequest request);

    @PostMapping(value = "/v1/payment-service/ete/billpay/prompt-pay/iso20022/confirm", consumes = "application/json;charset=UTF-8")
    ResponseEntity<TmbServiceResponse<PromptPayETEConfirmResponse>> confirmBillPromptPayISO20022Payment(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_TRANSACTION_ID) String transactionId,
            @RequestBody PromptPayETEConfirmRequest request);
}
