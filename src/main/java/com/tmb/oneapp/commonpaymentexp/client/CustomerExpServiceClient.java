package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID_FOR_COMMON_AUTHEN;


@FeignClient(name = "${feign.customer.exp.service.name}", url = "${feign.customer.exp.service.url}")
public interface CustomerExpServiceClient {

    @GetMapping(value = "/apis/customer/accounts/creditcard", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<CreditCardResponse>> getAccountsCreditCard(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId);

    @DeleteMapping(value = "/v1/customers-exp-service/cache/creditcard", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<Void>> deleteCreditCardCache(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(value = HEADER_ACCEPT_LANGUAGE) String acceptLanguage,
            @RequestHeader(value = HEADER_APP_VERSION) String appVersion,
            @RequestParam(value = HEADER_CRM_ID_FOR_COMMON_AUTHEN) String crmId);
}
