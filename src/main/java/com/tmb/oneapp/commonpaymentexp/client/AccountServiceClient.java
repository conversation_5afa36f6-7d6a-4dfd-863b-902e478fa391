package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.loanaccount.AccountLoanDetailRequest;
import com.tmb.oneapp.commonpaymentexp.model.loanaccount.HomeLoanFullInfoResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;

@FeignClient(name = "${feign.account.service.name}", url = "${feign.account.service.url}")
public interface AccountServiceClient {

    @PostMapping(value = "/apis/accounts/get-account-detail")
    ResponseEntity<TmbServiceResponse<HomeLoanFullInfoResponse>> getAccountLoanDetail(
            @RequestHeader(HEADER_CORRELATION_ID) String correlationId,
            @RequestBody AccountLoanDetailRequest requestBody);

}