package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.request.notification.NotificationRequest;
import com.tmb.common.model.response.notification.NotificationResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;

@FeignClient(name = "feign.notification.service.name", url = "${feign.notification.service.endpoint}")
public interface NotificationServiceClient {

	@PostMapping(value = "/apis/notification/e-noti/sendmessage")
	ResponseEntity<TmbServiceResponse<NotificationResponse>> sendMessage(
			@RequestHeader(value = HEADER_CORRELATION_ID, required = true) final String xCorrelationId,
			NotificationRequest request);
}
