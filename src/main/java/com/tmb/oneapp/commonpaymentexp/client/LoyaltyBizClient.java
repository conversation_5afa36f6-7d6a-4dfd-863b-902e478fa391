package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * Feign Client to connect Loyalty Biz
 */
@FeignClient(name = "${feign.loyalty.biz.name}", url = "${feign.loyalty.biz.url}")
public interface LoyaltyBizClient {

    @PostMapping(value = "/v1/loyalty-biz/redeem-points", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<String>> redeemPoint(
            @RequestHeader HttpHeaders headers,
            @RequestBody WowPointRedeemConfirmRequest request);
}
