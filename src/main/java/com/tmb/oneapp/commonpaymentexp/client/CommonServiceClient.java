package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.CommonData;
import com.tmb.common.model.TmbServiceResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;

/**
 * Feign Client to connect Common Service
 */
@FeignClient(name = "${feign.common.service.name}", url = "${feign.common.service.url}")
public interface CommonServiceClient {

    @GetMapping(value = "/v1/common-service/configuration", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<List<CommonData>>> fetchCommonConfig(
            @RequestParam("search") String searchType,
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID);
}
