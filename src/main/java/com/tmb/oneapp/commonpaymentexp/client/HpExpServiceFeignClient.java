package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.CacheResponse;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2AddServiceRequest;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2AddServiceRequestResponse;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2UpdatePaymentFlagRequest;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2UpdatePaymentFlagResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;

@FeignClient(name = "${feign.hp.exp.service.name}", url = "${feign.hp.exp.service.url}")
public interface HpExpServiceFeignClient {
    @GetMapping(value = "/v1/hp-exp-service/cache/aldx-fee", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<CacheResponse>> getAldxFeeCache(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationId,
            @RequestHeader(value = HEADER_ACCEPT_LANGUAGE, required = false) String acceptLanguage,
            @RequestHeader(value = HEADER_APP_VERSION, required = false) String appVersion,
            @RequestParam(value = "crmId") String crmId,
            @RequestParam(value = "objectiveId") String objectiveId,
            @RequestParam(value = "hpAccountNo") String hpAccountNo
    );

    @PostMapping(value = "/apis/hpservice/internal/service-request/close-account/k2/add-service-request", produces = MediaType.APPLICATION_JSON_VALUE)
    TmbOneServiceResponse<K2AddServiceRequestResponse> addServiceRequest(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID, @RequestBody K2AddServiceRequest request);

    @PostMapping(value = "/apis/hpservice/internal/service-request/v2.0/close-account/k2/update-payment-flag", produces = MediaType.APPLICATION_JSON_VALUE)
    TmbOneServiceResponse<K2UpdatePaymentFlagResponse> updatePaymentFlag(
            @RequestHeader HttpHeaders headers,
            @RequestBody K2UpdatePaymentFlagRequest request);
}
