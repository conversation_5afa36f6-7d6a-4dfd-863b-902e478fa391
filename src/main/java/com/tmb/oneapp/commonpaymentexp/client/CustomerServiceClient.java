package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.customer.AccumulateUsageRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerDetailResponse;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerSearchRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.PinFreeCountData;
import com.tmb.oneapp.commonpaymentexp.model.notification.ENotificationSettingResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;

@FeignClient(name = "${feign.customers.service.name}", url = "${feign.customers.service.url}")
public interface CustomerServiceClient {

    @PostMapping(value = {"/apis/customers/ecprofile/realtime"}, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<List<CustomerDetailResponse>>> fetchCustomerSearchRealtime(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestBody CustomerSearchRequest request
    );

    @GetMapping(value = {"/apis/customers/crmprofile"}, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<CustomerCrmProfile>> fetchCustomerCrmProfile(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId
    );

    @PatchMapping(value = {"/apis/customers/accumulate"}, produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<String>> updateUsageAccumulation(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestBody AccumulateUsageRequest accumulateUsageRequest
    );

    @PostMapping(value = "/apis/customers/pin-free-count", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<String>> updatePinFreeCount(
            @RequestHeader(value = HEADER_CRM_ID) String crmId,
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationId,
            @RequestBody PinFreeCountData pinFreeCountData);

    @GetMapping(value = "/apis/customers/settings/notification/email", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<ENotificationSettingResponse>> getENotificationSetting(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId);

    @GetMapping(value = "/apis/customers/kyc/cache", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<TmbServiceResponse<CustomerKYCResponse>> fetchCustomerKYC(
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationID,
            @RequestHeader(value = HEADER_CRM_ID) String crmId);
}
