package com.tmb.oneapp.commonpaymentexp.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.commonpaymentexp.model.GetCardResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID_FOR_COMMON_AUTHEN;

@FeignClient(name = "${feign.credit.card.service.name}", url = "${feign.credit.card.service.url}")
public interface CreditCardServiceClient {

    @GetMapping(value = "/apis/creditcard/creditcard-details/{ACCOUNT_ID}")
    ResponseEntity<GetCardResponse> getCreditCardDetailsByAccountId(
            @PathVariable("ACCOUNT_ID") String accountId,
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationId);

    @GetMapping(value = "/apis/creditcard/card-id-details/{CARD_ID}")
    ResponseEntity<GetCardResponse> getCreditCardDetailsByCardId(
            @PathVariable("CARD_ID") String cardId,
            @RequestHeader(value = HEADER_CORRELATION_ID) String correlationId);

    @DeleteMapping(value = "/v1/creditcard-service/cache/creditcard-accounts-summary-res")
    ResponseEntity<TmbOneServiceResponse<Void>> deleteCreditCardAccountSummaryResCache(
            @RequestHeader HttpHeaders headers,
            @RequestParam(value = HEADER_CRM_ID_FOR_COMMON_AUTHEN) String crmId);
}
