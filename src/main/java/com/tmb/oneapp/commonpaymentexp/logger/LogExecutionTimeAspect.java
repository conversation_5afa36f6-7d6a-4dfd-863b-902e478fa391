package com.tmb.oneapp.commonpaymentexp.logger;

import com.tmb.common.logger.TMBLogger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

/**
 * class responsible for logging execution time of the method
 * which is annotated by @LogExecutionTime
 */
@Aspect
@Component
public class LogExecutionTimeAspect {
    private static final TMBLogger<LogExecutionTimeAspect> logger = new TMBLogger<>(LogExecutionTimeAspect.class);

    public LogExecutionTimeAspect() {
        logger.info("LogExecutionTimeAspect initialized");
    }

    @Around("@annotation(com.tmb.oneapp.commonpaymentexp.logger.LogExecutionTime)")
    public Object logExecutionTime(ProceedingJoinPoint jp) throws Throwable {

        Instant startTime = Instant.now();
        Instant endTime;
        long executionTime;
        String methodName = jp.getSignature().getName();
        logger.info("===>>> START process {} <<<===", methodName);
        try {
            final Object result = jp.proceed();
            endTime = Instant.now();

            executionTime = Duration.between(startTime, endTime).toMillis();
            logger.info("Method {} executed in {} ms.", methodName, executionTime);
            return result;
        } catch (final Exception ex) {
            endTime = Instant.now();
            executionTime = Duration.between(startTime, endTime).toMillis();
            logger.error("Exception executed in {} ms. in method " + methodName + " : " + ex, executionTime);
            throw ex;
        } finally {
            logger.info("===>>> END process {} <<<===", methodName);

        }
    }
}
