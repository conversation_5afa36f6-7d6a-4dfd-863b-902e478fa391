package com.tmb.oneapp.commonpaymentexp.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

@Component
@ConfigurationProperties(prefix = "jwk")
@Validated
@Getter
@Setter
public class JwkConfigProperties {

    @NotNull
    @Valid
    private Sets sets;

    @Getter
    @Setter
    public static class Sets {
        /**
         * A map where the key is a client identifier (e.g., "shopee", "lazada")
         * and the value is the JWK Set in JSON string format.
         * Corresponds to properties like: jwk.sets.client.shopee='{"keys":[...]}'
         */
        @NotEmpty
        private Map<String, String> client;
    }
}
