package com.tmb.oneapp.commonpaymentexp.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.HandlerTypePredicate;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Override
    public void configurePathMatch(PathMatchConfigurer configure) {
        configure.addPathPrefix("/v1/security/common-payment-exp", HandlerTypePredicate.forBasePackage("com.tmb.oneapp.commonpaymentexp.controller.v1.security"));
        configure.addPathPrefix("/v1/common-payment-exp", HandlerTypePredicate.forBasePackage("com.tmb.oneapp.commonpaymentexp.controller.v1"));
    }
}
