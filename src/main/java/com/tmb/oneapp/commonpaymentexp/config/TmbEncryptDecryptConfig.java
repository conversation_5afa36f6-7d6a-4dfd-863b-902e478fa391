package com.tmb.oneapp.commonpaymentexp.config;

import com.tmb.common.crypto.TmbEncryptDecryptUtils;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

@Configuration
@DependsOn("tmbEncryptDecryptUtils")
public class TmbEncryptDecryptConfig {
    @Value("${private.key.location}")
    private String privateKey;
    @Value("${public.key.location}")
    private String publicKey;

    @PostConstruct
    public void changeToNewKey() {
        TmbEncryptDecryptUtils.setNewKeyLocation(privateKey, publicKey);
    }
}
