package com.tmb.oneapp.commonpaymentexp.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.servers.Server;
import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.ArrayList;


/**
 * Enabling Swagger for Api
 */
@Configuration
@OpenAPIDefinition
public class SwaggerConfig implements WebMvcConfigurer {

    @Value("${spring.application.name}")
    private String appName;

    @Value("${swagger.host:apis-portal.oneapp.tmbbank.local}")
    private String swaggerHost;

    @Value("${spring.application.description}")
    private String appDesc;

    @Bean
    public OpenAPI customOpenAPI(@Value("${swagger.host:apis-portal-dev3.oneapp.tmbbank.local}") String swaggerHost) {
        return new OpenAPI()
                .servers(getServerInfo(swaggerHost))
                .info(apiInfo());
    }

    @Bean
    public GroupedOpenApi publicApi() {
        return GroupedOpenApi.builder()
                .group("common-payment-exp")
                .packagesToScan("com.tmb.oneapp.commonpaymentexp")
                .pathsToMatch("/**")
                .build();
    }

    private Info apiInfo() {
        return new Info()
                .title(appName)
                .description(appDesc)
                .contact(apiContact());
    }

    private Contact apiContact() {
        return new Contact()
                .name("TMB Team");
    }

    private ArrayList<Server> getServerInfo(String swaggerHost) {
        String[] host = swaggerHost.split(",");
        ArrayList<Server> servers = new ArrayList<>();
        for (String url : host) {
            servers.add(new Server().url(url));
        }
        return servers;
    }

}
