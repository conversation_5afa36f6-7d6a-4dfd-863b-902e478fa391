package com.tmb.oneapp.commonpaymentexp.config;

import com.nimbusds.jose.jwk.JWKSet;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.text.ParseException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
public class JwkSetCacheConfig {

    private static final TMBLogger<JwkSetCacheConfig> logger = new TMBLogger<>(JwkSetCacheConfig.class);

    @Bean("jwkSetMap")
    public Map<String, JWKSet> jwkSetMap(JwkConfigProperties jwkConfigProperties) {
        Map<String, JWKSet> jwkSetMap = new ConcurrentHashMap<>();
        if (jwkConfigProperties.getSets() == null || jwkConfigProperties.getSets().getClient() == null) {
            logger.warn("No JWK sets configured in properties. JwkSetProvider will be empty.");
            return jwkSetMap;
        }

        jwkConfigProperties.getSets().getClient().forEach((partnerName, json) -> {
            try {
                JWKSet jwkSet = JWKSet.parse(json);
                jwkSetMap.put(partnerName, jwkSet);
                logger.info("JWKSet for partner '{}' loaded successfully with {} keys.", partnerName, jwkSet.size());
            } catch (ParseException e) {
                logger.debug("[partnerName = {}, partnerValue = {}]", partnerName, json);
                logger.error("Failed to parse JWKSet JSON for partner '{}'", partnerName, e);
                TMBCommonException cause = CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWK_NOT_CORRECT_FORMAT, "Cannot parse JWKSet for partner: " + partnerName);
                throw new IllegalStateException(cause.getMessage(), cause);
            }
        });
        return jwkSetMap;
    }
}