package com.tmb.oneapp.commonpaymentexp.config;

import feign.Client;
import okhttp3.OkHttpClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeignClientConfig {

    @Bean
    public OkHttpClient okHttpClient(@Value("${spring.profiles.active:}") String activeProfile) {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();

        if ("local".equals(activeProfile)) {
            builder.hostnameVerifier((hostname, session) -> true);
        }

        return builder.build();
    }

    @Bean
    public Client feignClient(OkHttpClient client) {
        return new feign.okhttp.OkHttpClient(client);
    }
}
