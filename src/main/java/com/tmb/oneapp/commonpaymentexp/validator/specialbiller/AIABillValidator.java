package com.tmb.oneapp.commonpaymentexp.validator.specialbiller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_AIA_0002;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_AIA_2095;

@Component
public class AIABillValidator {
    private static final TMBLogger<AIABillValidator> logger = new TMBLogger<>(AIABillValidator.class);
    private static final Map<String, List<String>> COMP_CODE_TO_PREFIX = Map.of(
            SPECIAL_OFFLINE_BILLER_AIA_0002, List.of("1", "2", "8", "9", "T", "U", "P", "M"),
            SPECIAL_OFFLINE_BILLER_AIA_2095, List.of("4"));

    public void checkDigit(String billerCompCode, String ref1, MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        logger.info("AIABillValidator comp code : {}, ref1", billerCompCode, ref1);
        if (!verify(COMP_CODE_TO_PREFIX.get(billerCompCode), ref1)) {
            String errorMessage = "Please verify Ref1 : " + masterBillerResponse.getRef1().getLabelEn();
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.INCORRECT_AIA_REF.getCode(), errorMessage);
        }
    }

    boolean verify(List<String> prefix, String ref1) {
        long specialRef1 = 9002000000L;
        List<Integer> constrainValue = Arrays.asList(3, 7, 1, 3, 7, 1, 3, 7);
        int checkDigit;
        int ref1Mod;
        int ref1CheckSum;
        int resultCheckDigit;

        if (StringUtils.isBlank(ref1) || ref1.length() != 10) {
            logger.info("AIABillValidator error case ref1 is blank or length's ref not equal 10");
            return false;
        }

        String ref1FirstChar = String.valueOf(ref1.toUpperCase().charAt(0));
        if (!prefix.contains(String.valueOf(ref1.charAt(0)))) {
            logger.info("AIABillValidator error case prefix not contains");
            return false;
        }

        if (ref1FirstChar.equals("9") && Long.parseLong(ref1) <= specialRef1) {
            logger.info("AIABillValidator pass case ref1 <= 9002000000L");
            return true;
        }

        try {
            checkDigit = Integer.parseInt(String.valueOf(ref1.charAt(9)));
            ref1CheckSum = Integer.parseInt(String.valueOf(ref1.charAt(1))) * constrainValue.get(0);
            ref1CheckSum += Integer.parseInt(String.valueOf(ref1.charAt(2))) * constrainValue.get(1);
            ref1CheckSum += Integer.parseInt(String.valueOf(ref1.charAt(3))) * constrainValue.get(2);
            ref1CheckSum += Integer.parseInt(String.valueOf(ref1.charAt(4))) * constrainValue.get(3);
            ref1CheckSum += Integer.parseInt(String.valueOf(ref1.charAt(5))) * constrainValue.get(4);
            ref1CheckSum += Integer.parseInt(String.valueOf(ref1.charAt(6))) * constrainValue.get(5);
            ref1CheckSum += Integer.parseInt(String.valueOf(ref1.charAt(7))) * constrainValue.get(6);
            ref1CheckSum += Integer.parseInt(String.valueOf(ref1.charAt(8))) * constrainValue.get(7);
        } catch (NumberFormatException ex) {
            logger.info("AIABillValidator error NumberFormatException : {}", ex);
            return false;
        }

        ref1Mod = ref1CheckSum % 10;
        if (ref1Mod == 0) {
            logger.info("AIABillValidator ref1Mod:{}, checkDigit:{}", ref1Mod, checkDigit);
            return ref1Mod == checkDigit;
        }
        resultCheckDigit = 10 - ref1Mod;

        logger.info("AIABillValidator resultCheckDigit:{}, checkDigit:{}", resultCheckDigit, checkDigit);
        return resultCheckDigit == checkDigit;
    }
}
