package com.tmb.oneapp.commonpaymentexp.validator.specialbiller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.springframework.stereotype.Component;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.chrono.ThaiBuddhistChronology;
import java.util.Collections;
import java.util.Date;


@Component
public class LandTransportBillValidator extends BillValidator {
    private static final TMBLogger<LandTransportBillValidator> logger = new TMBLogger<>(LandTransportBillValidator.class);

    public void checkDigit(String ref1, String ref2, String amount) throws TMBCommonException {
        if (!verify(ref1, ref2, amount)) {
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.INCORRECT_DLT_REF);
        }
    }

    boolean verify(String ref1, String ref2, String amount) throws TMBCommonException {
        if (ref1.length() != 9 || ref2.length() != 9) {
            logger.info("LandTransportBillValidator error case: length of ref2 and amount incorrect");
            return false;
        }

        if (isExceedDueDate(ref2, new Date())) {
            logger.info("LandTransportBillValidator error case: isExceedDueDate");
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.INCORRECT_DLT_EXCEEDED_DUD_DATE);
        }

        try {
            int ref1Sum = sum(ref1, 9, 3);
            int ref2Sum = sum(ref2.substring(0, 7), 7, 5);

            String amountDigits = amount.replace(".", "");
            amountDigits = String.join("", Collections.nCopies(9 - amountDigits.length(), "0")) + amountDigits;
            int amountSum = sum(amountDigits, 5, 3);

            int total = ref1Sum + ref2Sum + amountSum;
            String checkDigitResult = mod(total, 100);

            logger.info("LandTransportBillValidator checkDigitResult: {}, ref2: {} ", checkDigitResult, ref2);
            return checkDigitResult.equalsIgnoreCase(ref2.substring(ref2.length() - 2));
        } catch (NumberFormatException e) {
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.INCORRECT_DLT_REF);
        }
    }

    public boolean isExceedDueDate(String ref2, Date currentDate) {
        try {
            String buddhaDate = ThaiBuddhistChronology.INSTANCE
                    .dateNow()
                    .toString()
                    .substring(16, 18);

            String year = buddhaDate + ref2.substring(5, 7);
            String yearBE = String.valueOf(Integer.parseInt(year) - 543);
            String dueDateString = ref2.substring(1, 5) + yearBE;

            DateFormat df = new SimpleDateFormat("ddMMyyyy");
            df.setLenient(false);

            Date dueDate = df.parse(dueDateString);
            return currentDate.after(dueDate);
        } catch (Exception e) {
            logger.info("LandTransportBillValidator error: {}", e);
            return true;
        }
    }
}
