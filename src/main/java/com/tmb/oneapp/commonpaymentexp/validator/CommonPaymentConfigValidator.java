package com.tmb.oneapp.commonpaymentexp.validator;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;


@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class CommonPaymentConfigValidator {
    private static final TMBLogger<CommonPaymentConfigValidator> logger = new TMBLogger<>(CommonPaymentConfigValidator.class);

    public static void validateNotExpired(CommonPaymentConfig commonPaymentConfig) throws TMBCommonException {
        if (commonPaymentConfig.isExpired()) {
            logger.error("Common payment config is expired. Please verify config");
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.PRODUCT_EXPIRED_ERROR);
        }
    }
}
