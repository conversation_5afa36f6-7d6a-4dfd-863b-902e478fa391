package com.tmb.oneapp.commonpaymentexp.validator.specialbiller;

public abstract class BillValidator {

    protected int sum(String s, int c1, int c2) {
        int sum = 0;
        for (int i = 0; i < s.length(); i++) {
            int valueInt = Integer.parseInt(String.valueOf(s.charAt(i)));
            if (i % 2 == 0) {
                sum += valueInt * c1;
            } else {
                sum += valueInt * c2;
            }
        }
        return sum;
    }

    protected String mod(int total, int modulo){
        int modResult = total % modulo;
        String checkDigitResult = String.valueOf(modResult);
        if (checkDigitResult.length() == 1) {
            checkDigitResult = "0" + checkDigitResult;
        }

        return checkDigitResult;
    }

}
