package com.tmb.oneapp.commonpaymentexp.validator.specialbiller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Component
public class ToyotaBillValidator extends Bill<PERSON>alidator {
    private static final TMBLogger<ToyotaBillValidator> logger = new TMBLogger<>(ToyotaBillValidator.class);

    public void checkDigit(String ref1, String ref2) throws TMBCommonException {
        if (ref2.length() > 18 || ref1.length() > 18 || ref1.isEmpty()) {
            logger.info("ToyotaBillValidator validate error case :: ref2.length() > 18 || ref1.length() > 18 || ref1.length() == 0");
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.INCORRECT_TOYOTA_REF);
        }

        if (!verify(ref1, ref2)) {
            logger.info("ToyotaBillValidator validate error case :: verify digits failed");
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.INCORRECT_TOYOTA_REF);
        }
    }

    private boolean verify(String ref1, String ref2) throws TMBCommonException {
        try {
            ref1 = String.join("", Collections.nCopies(18 - ref1.length(), "0")) + ref1;
            int ref1Sum = super.sum(ref1, 4, 8);

            ref2 = String.join("", Collections.nCopies(18 - ref2.length(), "0")) + ref2;
            int ref2Sum = super.sum(ref2.substring(0, 16), 8, 4);

            int total = ref1Sum + ref2Sum;
            String checkDigitResult = mod(total, 100);

            logger.info("ToyotaBillValidator checkDigitResult:{}, ref2:{}", checkDigitResult, ref2);
            return checkDigitResult.equalsIgnoreCase(ref2.substring(ref2.length() - 2));
        } catch (NumberFormatException e) {
            logger.info("ToyotaBillValidator error NumberFormatException : {}", e);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.INCORRECT_TOYOTA_REF);
        }
    }
}
