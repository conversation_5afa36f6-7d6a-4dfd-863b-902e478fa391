package com.tmb.oneapp.commonpaymentexp.validator;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CommonData;
import com.tmb.oneapp.commonpaymentexp.client.CommonServiceClient;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthForceFR;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.client.CommonAuthenticationService;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_PRE_LOGIN;


@Component
@RequiredArgsConstructor
public class DailyLimitPinFreeValidator {
    private static final TMBLogger<DailyLimitPinFreeValidator> logger = new TMBLogger<>(DailyLimitPinFreeValidator.class);
    private final CommonServiceClient commonServiceClient;
    private final CommonAuthenticationService commonAuthenticationService;

    @Value("${fr.payment.accumulate.usage.limit:200000}")
    private BigDecimal totalPaymentAccumulateUsageLimit;

    public CommonAuthenResult validateIsRequireCommonAuthForTopUp(HttpHeaders headers, BigDecimal reqAmount, boolean isOwn, CustomerCrmProfile crmProfile) throws TMBCommonException {
        boolean shouldValidatePaymentAccumulateUsageLimit = true;
        return this.validateIsRequireCommonAuth(headers, reqAmount, isOwn, crmProfile, crmProfile.getPinFreeBpLimit(), shouldValidatePaymentAccumulateUsageLimit);
    }

    public CommonAuthenResult validateIsRequireCommonAuthForBill(HttpHeaders headers, BigDecimal reqAmount, boolean isOwn, CustomerCrmProfile crmProfile) throws TMBCommonException {
        return this.validateIsRequireCommonAuth(headers, reqAmount, isOwn, crmProfile, crmProfile.getPinFreeBpLimit(), false);
    }

    private CommonAuthenResult validateIsRequireCommonAuth(HttpHeaders headers, BigDecimal reqAmount, boolean isOwn, CustomerCrmProfile crmProfile, double pinFreeTranLimit, boolean shouldValidatePaymentAccumulateUsageLimit) throws TMBCommonException {

        if (isPreLogin(headers)) {
            return createCommonAuthResult(true, null, false, null);
        }
        if (isOwn) {
            return createCommonAuthResult(false, null, false, null);
        }

        if (isPinFreeDisabled(crmProfile) || isAmountOverPinFreeLimit(reqAmount, pinFreeTranLimit) || hasExceededPinFreeTransactionCount(crmProfile, headers)) {
            return createCommonAuthResult(true, null, false, null);
        }

        String crmId = headers.getFirst(HEADER_CRM_ID);
        String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        CommonAuthForceFR forceFRResult = commonAuthenticationService.getCommonAuthForceFR(correlationId, crmId);

        if (isForcedDipChip(forceFRResult)) {
            return createCommonAuthResult(true, null, true, true);
        }
        if (isForcedFR(forceFRResult)) {
            return createCommonAuthResult(true, true, true, false);
        }

        boolean requiresAuth = false;
        if (shouldValidatePaymentAccumulateUsageLimit) {
            BigDecimal totalUsage = crmProfile.getPaymentAccuUsgAmt().add(reqAmount);
            requiresAuth = totalUsage.compareTo(totalPaymentAccumulateUsageLimit) >= 0;
        }

        return createCommonAuthResult(requiresAuth, false, true, false);
    }

    private boolean isPreLogin(HttpHeaders headers) {
        return Boolean.parseBoolean(headers.getFirst(HEADER_PRE_LOGIN));
    }

    private boolean isPinFreeDisabled(CustomerCrmProfile crmProfile) {
        return "N".equalsIgnoreCase(crmProfile.getPinFreeSeetingFlag());
    }

    private boolean isAmountOverPinFreeLimit(BigDecimal reqAmount, double pinFreeTranLimit) {
        return reqAmount.compareTo(BigDecimal.valueOf(pinFreeTranLimit)) > 0;
    }

    private boolean hasExceededPinFreeTransactionCount(CustomerCrmProfile crmProfile, HttpHeaders headers) throws TMBCommonException {
        Integer pinFreeTxnCount = crmProfile.getPinFreeTxnCount();
        String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        CommonData commonConfig = FeignClientUtils.executeRequest(() -> commonServiceClient.fetchCommonConfig("common_module", correlationId)).get(0);
        Integer pinFreeMaxTrans = Integer.parseInt(commonConfig.getPinFreeMaxTrans());
        return pinFreeTxnCount >= pinFreeMaxTrans;
    }

    private boolean isForcedDipChip(CommonAuthForceFR commonAuthForceFRResult) {
        return Optional.ofNullable(commonAuthForceFRResult).map(CommonAuthForceFR::getIsForceDipchip).orElse(false);
    }

    private boolean isForcedFR(CommonAuthForceFR commonAuthForceFRResult) {
        return Optional.ofNullable(commonAuthForceFRResult).map(CommonAuthForceFR::getIsForce).orElse(false);
    }

    private CommonAuthenResult createCommonAuthResult(boolean requireAuth, Boolean isForceFr, boolean isPinFree, Boolean isForceDipChip) {
        CommonAuthenResult response = new CommonAuthenResult();
        response.setRequireCommonAuthen(requireAuth);
        response.setIsForceFr(isForceFr);
        response.setPinFree(isPinFree);
        response.setIsForceDipChip(isForceDipChip);
        return response;
    }
}
