package com.tmb.oneapp.commonpaymentexp.validator;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CommonTime;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.ConversionRateDetail;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.client.CommonAuthenticationService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.ServiceHoursUtils;
import com.tmb.oneapp.commonpaymentexp.validator.specialbiller.AIABillValidator;
import com.tmb.oneapp.commonpaymentexp.validator.specialbiller.LandTransportBillValidator;
import com.tmb.oneapp.commonpaymentexp.validator.specialbiller.ToyotaBillValidator;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_AIA_0002;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_AIA_2095;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_LAND_TRANSPORT_0980;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_TOYOTA_0287;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_TOYOTA_0840;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_TOYOTA_2192;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_TOYOTA_2193;

/**
 * Service class containing common validation methods used across different payment processors
 */
@Service
@RequiredArgsConstructor
public class BaseBillPayValidator {
    private static final TMBLogger<BaseBillPayValidator> logger = new TMBLogger<>(BaseBillPayValidator.class);

    private final AIABillValidator aiaBillValidator;
    private final ToyotaBillValidator toyotaBillValidation;
    private final LandTransportBillValidator landTransportBillValidation;
    private final DailyLimitService dailyLimitService;
    private final CommonAuthenticationService commonAuthenticationService;

    /**
     * Validator for special biller offline cause have specific validate depend on the biller
     *
     * @param compCode             compCode from cache.paymentInfo.compCode
     * @param reference1           reference1
     * @param reference2           reference2
     * @param amount               amount
     * @param masterBillerResponse masterBiller
     * @throws TMBCommonException throw when validate not pass
     */
    public void validateSpecialBillerOffline(String compCode, String reference1, String reference2, BigDecimal amount, MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        switch (compCode) {
            case SPECIAL_OFFLINE_BILLER_AIA_0002, SPECIAL_OFFLINE_BILLER_AIA_2095:
                aiaBillValidator.checkDigit(compCode, reference1, masterBillerResponse);
                logger.info("validateSpecialBillerOffline AiaBillValidation : Success");
                break;
            case SPECIAL_OFFLINE_BILLER_TOYOTA_0287, SPECIAL_OFFLINE_BILLER_TOYOTA_2193,
                 SPECIAL_OFFLINE_BILLER_TOYOTA_0840, SPECIAL_OFFLINE_BILLER_TOYOTA_2192:
                toyotaBillValidation.checkDigit(reference1, reference2);
                logger.info("validateSpecialBillerOffline ToyotaBillValidator : Success");
                break;
            case SPECIAL_OFFLINE_BILLER_LAND_TRANSPORT_0980:
                landTransportBillValidation.checkDigit(reference1, reference2, String.valueOf(amount));
                logger.info("validateSpecialBillerOffline LandTransportBillValidator : Success");
                break;
            default:
                break;
        }
    }

    /**
     * Validate service hours from master biller configuration
     *
     * @param masterBillerResponse Master biller configuration
     * @throws TMBCommonException if transaction is outside service hours
     */
    public void validateServiceHours(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        CommonTime serviceHours = new CommonTime();
        serviceHours.setStart(masterBillerResponse.getBillerInfo().getStartTime());
        serviceHours.setEnd(masterBillerResponse.getBillerInfo().getEndTime());

        boolean isDuringServiceHours = ServiceHoursUtils.isDuringServiceHours(new Date(), serviceHours);
        boolean isNonServiceHours = !isDuringServiceHours;

        if (isNonServiceHours) {
            logger.error("Transaction attempted outside service hours. [serviceHour.start = {}, serviceHour.end = {}]",
                    serviceHours.getStart(), serviceHours.getEnd());
            String messageError = String.format(ResponseCode.UNAVAILABLE_SERVICE_HOUR.getMessage(),
                    serviceHours.getStart() + "-" + serviceHours.getEnd());
            throw CommonServiceUtils.getBusinessTmbCommonException(
                    ResponseCode.UNAVAILABLE_SERVICE_HOUR.getCode(),
                    messageError
            );
        }
    }

    public void validateServiceHours(MasterBillerResponseInCache masterBillerResponseInCache) throws TMBCommonException {
        MasterBillerResponse masterBiller = new MasterBillerResponse()
                .setBillerInfo(
                        new BillerInfoResponse()
                                .setStartTime(masterBillerResponseInCache.getBillerInfo().getStartTime())
                                .setEndTime(masterBillerResponseInCache.getBillerInfo().getEndTime())
                );

        validateServiceHours(masterBiller);
    }

    /**
     * Validate biller expiration date
     *
     * @param masterBillerResponse Master biller configuration
     * @throws TMBCommonException if biller is expired
     */
    public void validateBillerExpiration(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        Date billerExpireDate = CommonServiceUtils.convertStringToDate(
                masterBillerResponse.getBillerInfo().getExpiredDate());
        Date currentDate = new Date();

        if (currentDate.after(billerExpireDate)) {
            logger.error("Error the biller already expired. Please verify biller expire in biller config. [biller-id = {}, billerExpireDate = {}]",
                    masterBillerResponse.getBillerInfo().getBillerId(),
                    billerExpireDate);
            throw CommonServiceUtils.getBusinessTmbCommonException(
                    ResponseCode.BILLER_EXPIRED
            );
        }
    }

    /**
     * Validate daily limit for non-credit card transactions
     *
     * @param request            Request containing payment details
     * @param masterBiller       Master biller configuration
     * @param customerCrmProfile Customer profile
     * @throws TMBCommonException if daily limit is exceeded
     */
    public void validateDailyLimit(ValidationCommonPaymentRequest request,
                                   MasterBillerResponse masterBiller,
                                   CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        //todo refactor this method by remove logic isNotPayWithCreditCard
        boolean isNotPayWithCreditCard = !request.getCreditCard().isPayWithCreditCardFlag();
        if (isNotPayWithCreditCard) {
            BigDecimal amount = request.getDeposit().getAmount();
            dailyLimitService.validateDailyLimitExceeded(masterBiller.getBillerInfo().getBillerGroupType(), customerCrmProfile, amount);
        }
    }

    /**
     * Validate insufficient funds for both credit card and deposit accounts
     *
     * @param request            Request containing payment details
     * @param creditCardDetail   Credit card account details
     * @param fromDepositAccount Deposit account details
     * @param fee                Transaction fee
     * @throws TMBCommonException if insufficient funds
     */
    public void validateInsufficientFund(ValidationCommonPaymentRequest request,
                                         CreditCardSupplementary creditCardDetail,
                                         DepositAccount fromDepositAccount,
                                         BigDecimal fee) throws TMBCommonException {
        boolean isPayWithCreditCardFlag = request.getCreditCard().isPayWithCreditCardFlag();
        if (isPayWithCreditCardFlag) {
            this.validateCreditCardFunds(request, creditCardDetail, fee);
        } else {
            this.validateDepositFunds(request, fromDepositAccount, fee);
        }
    }

    /**
     * Validate insufficient funds for deposit accounts
     *
     * @param request            Request containing payment details
     * @param fromDepositAccount Deposit account details
     * @param fee                Transaction fee
     * @throws TMBCommonException if insufficient funds
     */
    public void validateInsufficientFund(ValidationCommonPaymentRequest request,
                                         DepositAccount fromDepositAccount,
                                         BigDecimal fee) throws TMBCommonException {
        this.validateDepositFunds(request, fromDepositAccount, fee);
    }

    /**
     * Validate that comp code is not in exclude list
     *
     * @param configData Bill pay configuration
     * @param compCode   Company code to validate
     * @throws TMBCommonException if comp code is excluded
     */
    public void validateCompCodeExclusion(BillPayConfiguration configData, String compCode) throws TMBCommonException {
        List<String> excludeBillersConfig = Optional.ofNullable(configData.getBillerExcludeList())
                .orElse(Collections.emptyList());

        boolean isExcludeBiller = excludeBillersConfig.contains(compCode);
        if (isExcludeBiller) {
            logger.error("Error CompCode is exclude from biller-config. Please verify biller config or comp-code. [compCode = {}]", compCode);
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.EXCLUDE_BILLER_ERROR);
        }
    }

    private void validateCreditCardFunds(ValidationCommonPaymentRequest request,
                                         CreditCardSupplementary creditCardDetail,
                                         BigDecimal fee) throws TMBCommonException {
        BigDecimal amountWithFee = request.getCreditCard().getAmount().add(fee);
        boolean isCreditCardInsufficientFund = amountWithFee.compareTo(
                creditCardDetail.getCardBalances().getAvailableCreditAllowance()) > 0;

        if (isCreditCardInsufficientFund) {
            logger.error("Error Insufficient fund (Credit-card). Please verify available balance of the credit-card. [card_no = {}]",
                    creditCardDetail.getCardNo());
            throw CommonServiceUtils.getBusinessTmbCommonException(
                    ResponseCode.CREDIT_CARD_INSUFFICIENT_FUNDS_ERROR
            );
        }
    }

    private void validateDepositFunds(ValidationCommonPaymentRequest request,
                                      DepositAccount fromDepositAccount,
                                      BigDecimal fee) throws TMBCommonException {
        BigDecimal amountWithFee = request.getDeposit().getAmount().add(fee);
        boolean isDepositInsufficientFund = amountWithFee.compareTo(fromDepositAccount.getAvailableBalance()) > 0;

        if (isDepositInsufficientFund) {
            logger.error("Error Insufficient fund (CASA). Please verify available balance of the account. [deposit.account_number = {}]",
                    fromDepositAccount.getAccountNumber());
            throw CommonServiceUtils.getBusinessTmbCommonException(
                    ResponseCode.DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR
            );
        }
    }

    /**
     * Validate WOW Point details.
     *
     * @param request Request containing payment details
     * @param cache   Cache containing payment rule and other information
     * @throws TMBCommonException if WOW Point is invalid
     */
    public void validateWowPoint(ValidationCommonPaymentRequest request, CommonPaymentDraftCache cache) throws TMBCommonException {
        CommonPaymentRuleInCache commonPaymentRule = cache.getCommonPaymentRule();
        BigDecimal depositAmount = request.getDeposit().getAmount();
        BigDecimal wowPointAmount = request.getWowPoint().getWowPointAmount();
        BigDecimal discountAmount = request.getWowPoint().getDiscountAmount();
        BigDecimal txnAmount = request.getWowPoint().getTxnAmount();
        ConversionRateDetail conversionRate = commonPaymentRule.getWowPoint().getConversionRate();
        logger.debug("validateWowPoint transaction-id={} discount amount={} wow point amount={}", request.getTransactionId(), discountAmount, request.getWowPoint().getWowPointAmount());

        BigDecimal amountAfterDiscount = depositAmount.subtract(discountAmount);
        boolean isValidMinPaymentAmount = amountAfterDiscount.compareTo(commonPaymentRule.getWowPoint().getMinPaymentAmount()) >= 0;
        if (!isValidMinPaymentAmount) {
            logger.error("The WOW Point discount is not valid with {} < Minimum payment amount {}", amountAfterDiscount, commonPaymentRule.getWowPoint().getMinPaymentAmount());
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.INVALID_WOW_POINT);
        }

        boolean isValidMinMaxWowPoint = wowPointAmount.compareTo(commonPaymentRule.getWowPoint().getMin()) >= 0 &&
                wowPointAmount.compareTo(commonPaymentRule.getWowPoint().getMax()) <= 0;
        if (!isValidMinMaxWowPoint) {
            logger.error("The WOW Point discount is not valid with min({})/ max({}) point", commonPaymentRule.getWowPoint().getMin(), commonPaymentRule.getWowPoint().getMax());
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.INVALID_WOW_POINT);
        }

        BigDecimal convertPointToAmount = wowPointAmount.multiply(conversionRate.getNum()).divide(conversionRate.getDen(), RoundingMode.DOWN);
        boolean isValidWowPointUsage = request.getWowPoint().getDiscountAmount().compareTo(convertPointToAmount) == 0;
        logger.debug("validateWowPoint calculate result : {} wow point={} to THB={} with {}", isValidWowPointUsage, wowPointAmount, convertPointToAmount, conversionRate);
        if (!isValidWowPointUsage) {
            logger.error("The WOW Point discount is not valid with point conversion rate");
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.INVALID_WOW_POINT);
        }

        if (txnAmount.compareTo(amountAfterDiscount) != 0) {
            logger.error("The WOW Point discount is not valid with txn amount={} mismatch amount after discount={}", txnAmount, amountAfterDiscount);
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.INVALID_WOW_POINT);
        }
    }

    public void validatePostAuthenticateTransaction(String correlationId, String transactionId) throws TMBCommonException {
        String postAuthenticateTransactionIdKey = String.format(CommonPaymentExpConstant.VERIFY_COMMON_AUTHEN_REF_ID_PREFIX_REDIS_KEY, transactionId);

        String cacheData = commonAuthenticationService.getCacheDataForValidatePostAuthentication(correlationId, postAuthenticateTransactionIdKey);

        boolean isTransactionAlreadyPostAuthenticate = cacheData != null;
        if (isTransactionAlreadyPostAuthenticate) {
            logger.error("validatePostAuthenticateTransaction transaction-id={} already process post authentication", transactionId);
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ALREADY_POST_AUTHENTICATE_TRANSACTION);
        }

    }
}