package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.fleetcard;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreditLimit {
    private Double total;
    private Double available;
    private Double availableCashAdvance;
    private Double cash_advanceFee;
    private Double lastPaymentAmount;
}
