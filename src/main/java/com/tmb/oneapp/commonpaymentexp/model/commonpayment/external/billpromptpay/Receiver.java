
package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@Accessors(chain = true)
@ToString
@JsonPropertyOrder({ "proxyType", "proxyValue", "accountId", "accountType", "accountName", "accountDisplayName",
		"accountLength", "taxId", "bankCode" })
public class Receiver {

	@JsonProperty("proxyType")
	private String proxyType;
	@JsonProperty("proxyValue")
	private String proxyValue;
	@JsonProperty("accountId")
	private String accountId;
	@JsonProperty("accountType")
	private String accountType;
	@JsonProperty("accountName")
	private String accountName;
	@JsonProperty("accountDisplayName")
	private String accountDisplayName;
	@JsonProperty("billerDisplayName")
	private String billerDisplayName;
	@JsonProperty("accountLength")
	private Integer accountLength;
	@JsonProperty("taxId")
	private String taxId;
	@JsonProperty("bankCode")
	private String bankCode;
	private String id;
	private String itmxFlag;
	private String creditFlag;
	private String category;
	private String requiredReference2Flag;
	private String scanOnly;
	private String customerTypeFlag;
	private String compCode;
	@JsonProperty("shareFlag")
	private String shareFlag;
	@JsonProperty("merchantType")
	private String merchantType;
}
