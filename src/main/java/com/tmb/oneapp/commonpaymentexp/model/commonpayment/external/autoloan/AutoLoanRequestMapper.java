package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan;

import com.tmb.common.cache.Transaction;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.BLANK;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.DATETIME_FORMAT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ONLINE_TRANS_REF_SEQUENCE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TOP_UP_BRANCH_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TOP_UP_CURRENCY;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TTB_BANK_CODE_3DIGIT;

public class AutoLoanRequestMapper {
    public static final AutoLoanRequestMapper INSTANCE = new AutoLoanRequestMapper();

    private AutoLoanRequestMapper() {
    }

    /**
     * Sets up a payment request for auto loan verification
     *
     * @param request ValidationCommonPaymentRequest containing payment details
     * @param cache CommonPaymentDraftCache containing draft payment information
     * @param idNo Customer identification number
     * @return TopUpETEPaymentRequest prepared for auto loan verification
     */
    public TopUpETEPaymentRequest setupAutoLoanVerifyPaymentRequest(ValidationCommonPaymentRequest request, CommonPaymentDraftCache cache, String idNo) {
        boolean isPayWithCreditCard = request.getCreditCard().isPayWithCreditCardFlag();
        String paymentId = this.getSequencePaymentId();
        TopUpETEPaymentRequest topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setPaymentId(paymentId);
        topUpETEPaymentRequest.setBankReferenceId(paymentId);
        topUpETEPaymentRequest.setTransactionId(paymentId);
        topUpETEPaymentRequest.setCurrency(TOP_UP_CURRENCY);
        topUpETEPaymentRequest.setBankId(TTB_BANK_CODE_3DIGIT);
        topUpETEPaymentRequest.setBranchId(TOP_UP_BRANCH_ID);

        BigDecimal amount = isPayWithCreditCard ? request.getCreditCard().getAmount() : request.getDeposit().getAmount();
        topUpETEPaymentRequest.setAmount(amount);
        topUpETEPaymentRequest.setCompCode(cache.getPaymentInformation().getCompCode());
        topUpETEPaymentRequest.setReference1(cache.getPaymentInformation().getProductDetail().getProductRef1());
        topUpETEPaymentRequest.setReference2(cache.getPaymentInformation().getProductDetail().getProductRef2());
        topUpETEPaymentRequest.setReference3(idNo);
        topUpETEPaymentRequest.setReference4(BLANK);

        return topUpETEPaymentRequest;
    }

    public String getSequencePaymentId() {
        String transactionId = getTransactionId(ONLINE_TRANS_REF_SEQUENCE, 5);
        transactionId = transactionId.substring(transactionId.length() - 5);

        SimpleDateFormat dateFormat = new SimpleDateFormat(DATETIME_FORMAT);
        String requestDate = dateFormat.format(new Date(System.currentTimeMillis()));
        return requestDate + transactionId;
    }

    /**
     * Gets a transaction ID from the cache
     *
     * @param key Sequence key
     * @param digits Number of digits
     * @return Generated transaction ID
     */
    public String getTransactionId(String key, int digits) {
        return Transaction.getTransactionId(key, digits);
    }

    /**
     * Converts amount to decimal string format
     *
     * @param amount Amount string to convert
     * @return Properly formatted decimal string
     */
    private String convertAmountToDecimal(String amount) {
        if (amount == null || amount.isEmpty()) {
            return "0.00";
        }

        BigDecimal decimalAmount = new BigDecimal(amount);
        return decimalAmount.setScale(2, RoundingMode.HALF_UP).toString();
    }
}
