package com.tmb.oneapp.commonpaymentexp.model.commonauthentication;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonAuthenVerifyRefResponse {
    private Boolean result;
    private Boolean frRequire;
    private String frStatusCode;
    private String destination;
    private String bankCode;
    private String billerCode;
    private String productCode;
    private String amount;
    private String dailyAmount;
    private String featureId;
}
