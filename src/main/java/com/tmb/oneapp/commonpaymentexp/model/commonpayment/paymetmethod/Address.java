package com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class Address {
    private String addressType;
    private String customerNameEn;
    private String customerNameTh;
    private String contactAddress;
}
