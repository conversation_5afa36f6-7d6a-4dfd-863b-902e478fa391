package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.autoloan;

import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidateDataAfterCallExternal;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class AutoLoanValidateDataAfterCallExternal implements ValidateDataAfterCallExternal {
    private boolean isRequireCommonAuthen;
    private boolean isPayByOwner;
    private CommonAuthenticationValidationCommonPaymentResponse commonAuthentication;
    private BigDecimal feeAfterCalculated;
    private BigDecimal totalAmount;
    private CommonAuthenResult commonAuthenResult;
}
