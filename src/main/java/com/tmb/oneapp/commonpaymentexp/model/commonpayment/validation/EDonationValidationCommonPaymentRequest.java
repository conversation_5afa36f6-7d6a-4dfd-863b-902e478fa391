package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EDonationValidationCommonPaymentRequest {
    @JsonProperty("allow_share_to_rd_flag")
    private boolean allowShareToRdFlag;
}
