package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.fleetcard;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Transaction {
    private String cicsTranCode;
    private String tranCode;
    private String dueDate;
    private String statementDate;
    PaymentAmount paymentAmount;
    CustomerAccount customerAccount;
    CreditLimit creditLimit;
    CreditLine creditLine;
    BonusPoint bonusPoint;
}