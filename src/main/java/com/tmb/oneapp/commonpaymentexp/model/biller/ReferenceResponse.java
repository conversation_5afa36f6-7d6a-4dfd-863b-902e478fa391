package com.tmb.oneapp.commonpaymentexp.model.biller;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReferenceResponse {
    private String labelTh;
    private String labelEn;
    private Integer maxLength;
    private Boolean isRequireAddFavorite;
    private Boolean isRequirePay;
    private Boolean isMobile;
    private String regEx;
    private String formatType;
    private Boolean isNumeric;
    private String keyBoardLayout;
}