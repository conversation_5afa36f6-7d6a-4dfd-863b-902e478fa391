package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityAutoLoanBillPayConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialAutoLoanBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityAutoLoanBillPay;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class AutoLoanLogsConfirm implements LogsForConfirm {
    private ActivityAutoLoanBillPayConfirmationEvent activityAutoLoanBillPayConfirmationEvent;
    private FinancialAutoLoanBillPayActivityLog financialAutoLoanBillPayActivityLog;
    private TransactionActivityAutoLoanBillPay transactionActivityAutoLoanBillPay;
    private ActivityCustomSlipCompleteEvent activityCustomSlipCompleteEvent;
}
