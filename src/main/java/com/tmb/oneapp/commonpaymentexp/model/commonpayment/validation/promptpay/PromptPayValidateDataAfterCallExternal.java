package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.promptpay;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidateDataAfterCallExternal;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PromptPayValidateDataAfterCallExternal implements ValidateDataAfterCallExternal {
    private boolean isRequireCommonAuthen;
    private CommonAuthenticationValidationCommonPaymentResponse commonAuthentication;
    private BigDecimal feeAfterCalculated;
    private BigDecimal totalAmount;
    private CommonAuthenResult commonAuthenResult;
}
