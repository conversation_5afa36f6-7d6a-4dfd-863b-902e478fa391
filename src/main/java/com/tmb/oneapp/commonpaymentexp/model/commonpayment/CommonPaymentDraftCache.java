package com.tmb.oneapp.commonpaymentexp.model.commonpayment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonPaymentDraftCache {
    //Got data when Initial success
    private String crmId;
    private String partnerName;
    private String processorType;
    private PaymentInformation paymentInformation;
    private CommonPaymentConfig commonPaymentConfig;
    private DeepLinkRequestInCache deepLinkRequest;

    //Got data when integrate with wow point on get payment method success
    private CommonPaymentRuleInCache commonPaymentRule;

    //Got data when Validate success
    private ValidationCommonPaymentRequest validateRequest;
    private ValidationCommonPaymentDraftCache validateDraftCache;
}
