package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ExternalValidateResponse;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class BillPayExternalValidateResponse implements ExternalValidateResponse {
    private OCPBillRequest ocpRequest;
    private OCPBillPayment ocpDataResponse;
}
