package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.promptpay;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ExternalValidateResponse;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class PromptPayExternalValidateResponse implements ExternalValidateResponse {
    private PromptPayETEValidateRequest eteRequest;
    private PromptPayETEValidateResponse eteResponse;
}
