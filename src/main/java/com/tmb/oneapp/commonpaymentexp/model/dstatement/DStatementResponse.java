package com.tmb.oneapp.commonpaymentexp.model.dstatement;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DStatementResponse {
    private DebitAccount debitAccount;
    private Double availBalance;
    private Double ledgerBalance;
    private String waiveProductCode;
    private String amountWaived;
    private String waiveRemaining;
    private String waiveUsed;
    private String waiveFlag;
    private String transactionReference;
    private Double feeAmount;
    private String feeType;
    private Double transactionAmount;
    private String tellerId;
}
