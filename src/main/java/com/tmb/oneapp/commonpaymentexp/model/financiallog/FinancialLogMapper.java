package com.tmb.oneapp.commonpaymentexp.model.financiallog;

import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AutoLoanOCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.BillPaymentCreditCard;
import com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import static com.tmb.common.constants.TmbCommonUtilityConstants.ACCOUNT_TYPE_LOC;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_SUCCESS;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.MAX_TO_ACCT_ID;


@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class FinancialLogMapper {
    public static final FinancialLogMapper INSTANCE = new FinancialLogMapper();

    private static final String D00 = "000";
    private static final String BILLER_METHOD_TMB_LOAN = "3";
    private static final String PAYMENT_METHOD_TMB_PRODUCT = "15";
    private static final String FIN_LOG_ERROR_CD_SUCCESS_CODE = "000";
    private static final String MB = "mb";
    private static final String FIN_LOG_CLEARING_STATUS = "01";
    private static final String TTB_BANK_CODE = "11";
    private static final String FIN_LOG_SMART_FLG = "";
    private static final String CATEGORY_BILL_PAY_ID = "3";

    public static final String BILLER_GROUP_BILL_PAY = "0";
    public static final String BILLER_GROUP_TOP_UP = "1";
    public static final String ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID = "027";
    public static final String ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_TOP_UP_ID = "030";
    public static final String TXN_TYPE_TOP_UP = "005";
    public static final String TXN_TYPE_BILL = "002";
    public static final String FROM_DEPOSIT_ACCOUNT_ERROR_MESSAGE_NULL = "From deposit account should not be null in cache, Please verify validate action when set cache";
    public static final String VALIDATE_REQUEST_ERROR_MESSAGE_NULL = "Validate request should not be null in cache, Please verify validate action when set cache";

    public FinancialAutoLoanBillPayActivityLog mapToFinancialAutoLoanBillPayActivityLog(BaseConfirmLogRecord baseConfirmLogRecord, String activityLogTypeId, CommonPaymentDraftCache draftCache) {
        final String activityIdFromTouch = ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID;
        final var masterBiller = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getMasterBillerResponse(), "MasterBiller should not be null in cache, Please verify validate action when set cache");
        final var validateRequestFromFE = NullSafeUtils.requireNonNull(draftCache::getValidateRequest, VALIDATE_REQUEST_ERROR_MESSAGE_NULL);
        final var fromDepositAccount = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getFromDepositAccount(), FROM_DEPOSIT_ACCOUNT_ERROR_MESSAGE_NULL);
        final var paymentInformation = NullSafeUtils.requireNonNull(draftCache::getPaymentInformation);

        FinancialAutoLoanBillPayActivityLog financialAutoLoanBillPayActivityLog = FinancialAutoLoanBillPayActivityLog.builder()
                .referenceID(baseConfirmLogRecord.referenceId())
                .crmId(baseConfirmLogRecord.crmId())
                .activityRefId(baseConfirmLogRecord.correlationId())
                .txnDt(baseConfirmLogRecord.transactionDateTime())
                .activityTypeIdNew(activityLogTypeId)
                .activityId(activityIdFromTouch)

                .errorCd(FIN_LOG_ERROR_CD_SUCCESS_CODE)
                .channelId(MB)
                .txnStatus(ACTIVITY_SUCCESS)
                .clearingStatus(FIN_LOG_CLEARING_STATUS)
                .bankCode(TTB_BANK_CODE)
                .smartFlag(FIN_LOG_SMART_FLG)
                .categoryId(CATEGORY_BILL_PAY_ID)
                .toAccType(D00)
                .txnType(TXN_TYPE_BILL)

                .toAccName(masterBiller.getBillerInfo().getNameEn())
                .memo(validateRequestFromFE.getNote())
                .fromAccNickName(fromDepositAccount.getProductNickname())
                .fromAccName(fromDepositAccount.getAccountName())
                .fromAccNo(fromDepositAccount.getAccountNumber())
                .fromAccType(fromDepositAccount.getAccountType())

                .txnFee(String.valueOf(draftCache.getValidateDraftCache().getFeeCalculated()))
                .compCode(paymentInformation.getCompCode())
                .billerRef1(paymentInformation.getProductDetail().getProductRef1())
                .billerRef2(paymentInformation.getProductDetail().getProductRef2())
                .build();

        if (WowPointUtils.isWowPointTransaction(validateRequestFromFE, draftCache.getCommonPaymentRule())) {
            AutoLoanOCPBillRequest autoLoanOCPBillRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getAutoLoanOCPBillPaymentConfirmRequest();
            WowPointRedeemConfirmRequest wowPointRequest = draftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest();

            financialAutoLoanBillPayActivityLog.setToAccNo(shortenAccountId(autoLoanOCPBillRequest.getToAccount().getAccountId()));
            financialAutoLoanBillPayActivityLog.setFinFlexValues1(autoLoanOCPBillRequest.getAutoLoan().getCustomerName());
            financialAutoLoanBillPayActivityLog.setTxnAmount(String.valueOf(wowPointRequest.getTxnAmount()));

            financialAutoLoanBillPayActivityLog.setWowPointDiscount(String.valueOf(wowPointRequest.getPointUnits()));
            financialAutoLoanBillPayActivityLog.setWowPointDiscountAmount(String.valueOf(wowPointRequest.getAmount()));
            financialAutoLoanBillPayActivityLog.setTotalPayWithWowAmount(String.valueOf(wowPointRequest.getTxnAmount()
                    .add(wowPointRequest.getAmount())));
        } else {
            TopUpETEPaymentRequest eteConfirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getTopUpETEPaymentRequest();
            financialAutoLoanBillPayActivityLog.setToAccNo(shortenAccountId(eteConfirmRequest.getToAccount().getAccountId()));
            financialAutoLoanBillPayActivityLog.setTxnAmount(String.valueOf(eteConfirmRequest.getAmount()));
            financialAutoLoanBillPayActivityLog.setFinFlexValues1(eteConfirmRequest.getAutoLoanDetails().getCustomerName());
        }

        return financialAutoLoanBillPayActivityLog;
    }

    public FinancialCreditCardBillPayActivityLog mapToFinancialCreditCardBillPayActivityLog(BaseConfirmLogRecord baseConfirmLogRecord, String activityLogTypeId, CommonPaymentDraftCache draftCache) {
        final String activityIdFromTouch = ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID;
        final MasterBillerResponseInCache masterBiller = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getMasterBillerResponse(), "MasterBiller should not be null in cache, Please verify validate action when set cache");
        final ValidationCommonPaymentRequest validateRequestFromFE = NullSafeUtils.requireNonNull(draftCache::getValidateRequest, VALIDATE_REQUEST_ERROR_MESSAGE_NULL);
        final BillPaymentCreditCard externalRequestBillPayCreditCard = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getExternalConfirmRequest().getCreditCardConfirmRequest().getBillPayment(), "External request Bill payment request should not be null in cache, Please verify validate action when set cache");
        final DepositAccountInCache fromDepositAccount = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getFromDepositAccount(), FROM_DEPOSIT_ACCOUNT_ERROR_MESSAGE_NULL);

        return FinancialCreditCardBillPayActivityLog.builder()
                .referenceID(baseConfirmLogRecord.referenceId())
                .crmId(baseConfirmLogRecord.crmId())
                .activityRefId(baseConfirmLogRecord.correlationId())
                .txnDt(baseConfirmLogRecord.transactionDateTime())
                .activityTypeIdNew(activityLogTypeId)
                .activityId(activityIdFromTouch)

                .errorCd(FIN_LOG_ERROR_CD_SUCCESS_CODE)
                .channelId(MB)
                .txnStatus(ACTIVITY_SUCCESS)
                .clearingStatus(FIN_LOG_CLEARING_STATUS)
                .bankCode(TTB_BANK_CODE)
                .smartFlag(FIN_LOG_SMART_FLG)
                .categoryId(CATEGORY_BILL_PAY_ID)
                .toAccType(D00)
                .txnType(TXN_TYPE_BILL)

                .toAccName(masterBiller.getBillerInfo().getNameEn())
                .memo(validateRequestFromFE.getNote())
                .fromAccNickName(fromDepositAccount.getProductNickname())
                .fromAccName(fromDepositAccount.getAccountName())

                .billerRef1(externalRequestBillPayCreditCard.getRef1())
                .billerRef2(externalRequestBillPayCreditCard.getRef2())
                .txnAmount(externalRequestBillPayCreditCard.getAmount())
                .compCode(externalRequestBillPayCreditCard.getCompCode())
                .fromAccNo(externalRequestBillPayCreditCard.getPayerAccount().getId())
                .fromAccType(externalRequestBillPayCreditCard.getPayerAccount().getType())
                .toAccNo(externalRequestBillPayCreditCard.getPayeeCard().getAccountId())
                .billerCustomerName(externalRequestBillPayCreditCard.getPayeeCard().getCardEmbossingName())
                .txnFee(externalRequestBillPayCreditCard.getFee().getBillPayment())
                .build();
    }

    public FinancialLoanBillPayActivityLog mapToFinancialLoanBillPayActivityLog(BaseConfirmLogRecord baseConfirmLogRecord, String activityLogTypeId, CommonPaymentDraftCache draftCache) {
        final String activityIdFromTouch = ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID;
        final String toAccountTypeLocOnlyForLoan = ACCOUNT_TYPE_LOC;
        final MasterBillerResponseInCache masterBiller = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getMasterBillerResponse(), "MasterBiller should not be null in cache, Please verify validate action when set cache");
        final ValidationCommonPaymentRequest validateRequestFromFE = NullSafeUtils.requireNonNull(draftCache::getValidateRequest, VALIDATE_REQUEST_ERROR_MESSAGE_NULL);
        final OCPBillRequest externalConfirmRequest = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest(), "External request Bill payment request should not be null in cache, Please verify validate action when set cache");
        final DepositAccountInCache fromDepositAccount = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getFromDepositAccount(), FROM_DEPOSIT_ACCOUNT_ERROR_MESSAGE_NULL);
        final var paymentInformation = NullSafeUtils.requireNonNull(draftCache::getPaymentInformation);
        final ValidationCommonPaymentDraftCache validateDraftCache = draftCache.getValidateDraftCache();
        final String toAccountNo13Digits = StringUtils.right(externalConfirmRequest.getToAccount().getAccountId(), 13);

        FinancialLoanBillPayActivityLog financialLoanBillPayActivityLog = FinancialLoanBillPayActivityLog.builder()
                .referenceID(baseConfirmLogRecord.referenceId())
                .crmId(baseConfirmLogRecord.crmId())
                .activityRefId(baseConfirmLogRecord.correlationId())
                .txnDt(baseConfirmLogRecord.transactionDateTime())
                .activityTypeIdNew(activityLogTypeId)
                .activityId(activityIdFromTouch)

                .errorCd(FIN_LOG_ERROR_CD_SUCCESS_CODE)
                .channelId(MB)
                .txnStatus(ACTIVITY_SUCCESS)
                .clearingStatus(FIN_LOG_CLEARING_STATUS)
                .bankCode(TTB_BANK_CODE)
                .smartFlag(FIN_LOG_SMART_FLG)
                .categoryId(CATEGORY_BILL_PAY_ID)
                .txnType(TXN_TYPE_BILL)

                .toAccType(toAccountTypeLocOnlyForLoan)
                .toAccNo(toAccountNo13Digits)
                .txnFee(String.valueOf(validateDraftCache.getFeeCalculated()))
                .toAccName(masterBiller.getBillerInfo().getNameEn())
                .memo(validateRequestFromFE.getNote())

                .fromAccNickName(fromDepositAccount.getProductNickname())
                .fromAccName(fromDepositAccount.getAccountName())

                .billerRef1(paymentInformation.getProductDetail().getProductRef1())
                .billerRef2(paymentInformation.getProductDetail().getProductRef2())

                .txnAmount(externalConfirmRequest.getAmount())
                .compCode(externalConfirmRequest.getCompCode())
                .fromAccNo(externalConfirmRequest.getFromAccount().getAccountId())
                .fromAccType(externalConfirmRequest.getFromAccount().getAccountType())
                .build();

        if (WowPointUtils.isWowPointTransaction(validateRequestFromFE, draftCache.getCommonPaymentRule())) {
            WowPointRedeemConfirmRequest wowPointRequest = draftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest();
            financialLoanBillPayActivityLog.setTxnAmount(String.valueOf(wowPointRequest.getTxnAmount()));
            financialLoanBillPayActivityLog.setWowPointDiscount(String.valueOf(wowPointRequest.getPointUnits()));
            financialLoanBillPayActivityLog.setWowPointDiscountAmount(String.valueOf(wowPointRequest.getAmount()));
            financialLoanBillPayActivityLog.setTotalPayWithWowAmount(String.valueOf(wowPointRequest.getTxnAmount()
                    .add(wowPointRequest.getAmount())));
        }

        return financialLoanBillPayActivityLog;
    }


    private static String getTxnTypeId(String billerGroupType) {
        //todo please change to use Constant
        return BILLER_GROUP_BILL_PAY.equals(billerGroupType) ? TXN_TYPE_BILL : TXN_TYPE_TOP_UP;
    }

    private static String getActivityFinAndTransConfirmId(String billerGroupType) {
        //todo get this method only when transaction onlineOfflineTopUp
        return BILLER_GROUP_BILL_PAY.equals(billerGroupType) ? ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID : ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_TOP_UP_ID;
    }

    private static String shortenAccountId(String accountId) {
        if (accountId == null) {
            return null;
        }

        return accountId.substring(Math.max(0, accountId.length() - MAX_TO_ACCT_ID));
    }

//      todo these code block write for transaction pay with credit card (OnlineOffLine...)
//  if (draftCache.getValidateRequest().isPayWithCreditCard()) {
//      FromAccNo(ocpBillPayment.getPaymentCacheData().getCardNumber());
//      FinFlexValues1(ocpBillPayment.getFromAccount().getAccountId());
//  } else {
//      FromAccNo(ocpBillPayment.getFromAccount().getAccountId());
//  }
}
