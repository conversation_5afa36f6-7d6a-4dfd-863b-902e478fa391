package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BillPaymentCreditCard {
    String amount;
    String compCode;
    String paymentId;
    String bankRefId;
    String ref1;
    String ref2;
    String epayCode;
    PayerAccount payerAccount;
    PayeeCard payeeCard;
    CreditCardFee fee;
    String requestUid;
    String requestDateTime;
    String bankId;
    String branchId;
    String channelId;
    String tellerId;
    String currency;
}
