
package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Setter
@Getter
@Accessors(chain = true)
@ToString
@JsonPropertyOrder({
        "accountId",
        "accountType",
        "accountName",
        "accountLength",
        "taxId"
})
public class Sender {

    private String accountId;
    private String accountType;
    private String accountName;
    private Integer accountLength;
    private String taxId;
    private String bankCode;
    private String bankName;
    private String customerTypeFlag;
    private String customerId;
    private String merchantType;
}
