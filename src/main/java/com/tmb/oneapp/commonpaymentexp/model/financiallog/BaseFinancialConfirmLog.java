package com.tmb.oneapp.commonpaymentexp.model.financiallog;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@SuperBuilder
public abstract class BaseFinancialConfirmLog extends FinancialRequest {

    public void setFailureStatusWithErrorCodeFromException(Exception e) {
        super.setTxnStatus(CommonPaymentExpConstant.ACTIVITY_FAILURE);
        if (e instanceof TMBCommonException ee) {
            super.setErrorCd(ee.getErrorCode());
        } else {
            super.setErrorCd(null);
        }
    }
}
