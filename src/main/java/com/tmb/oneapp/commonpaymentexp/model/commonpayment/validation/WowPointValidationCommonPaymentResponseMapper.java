package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation;

public class WowPointValidationCommonPaymentResponseMapper {
    public static final WowPointValidationCommonPaymentResponseMapper INSTANCE = new WowPointValidationCommonPaymentResponseMapper();

    private WowPointValidationCommonPaymentResponseMapper() {
    }

    public WowPointValidationCommonPaymentResponse mapWowPointValidationCommonPaymentResponse(ValidationCommonPaymentRequest request) {
        return new WowPointValidationCommonPaymentResponse()
                .setWowPointAmount(request.getWowPoint().getWowPointAmount())
                .setDiscountAmount(request.getWowPoint().getDiscountAmount());
    }
}
