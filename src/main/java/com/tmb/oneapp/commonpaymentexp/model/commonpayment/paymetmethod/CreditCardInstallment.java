package com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CreditCardInstallment {

    private String cardNo;
    private String accountId;
    private String productNameEn;
    private String productNameTh;

}
