package com.tmb.oneapp.commonpaymentexp.model.loanaccount;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@JsonInclude(Include.NON_NULL)
@Data
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Balances {

    @JsonProperty("original_loan")
    private String originalLoan;
    @JsonProperty("principal")
    private String principal;
    @JsonProperty("ledger")
    private String ledger;
    @JsonProperty("outstanding")
    private String outstanding;
    @JsonProperty("available")
    private String available;
    @JsonProperty("current")
    private String current;
    @JsonProperty("accrued_interest")
    private String accruedInterest;
    @JsonProperty("payoff")
    private String payoff;

}