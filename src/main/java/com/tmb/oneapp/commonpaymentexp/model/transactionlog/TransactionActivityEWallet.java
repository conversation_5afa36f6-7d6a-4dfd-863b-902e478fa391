package com.tmb.oneapp.commonpaymentexp.model.transactionlog;

import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETERequest;

import static com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialActivityLog.ACTIVITY_FIN_AND_TRANS_TOP_UP_ID;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialActivityLog.TXN_TYPE_TOP_UP;

public class TransactionActivityEWallet extends TransactionActivityBillPay {
    public TransactionActivityEWallet(String refId, String crmId, CommonPaymentDraftCache commonPaymentDraftCache, String transactionDateTime) {
        super(refId, crmId, commonPaymentDraftCache, transactionDateTime);

        final EWalletETERequest eWalletConfirmRequest = commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getEWalletConfirmRequest();
        final ValidationCommonPaymentRequest validateRequest = commonPaymentDraftCache.getValidateRequest();
        final DepositAccountInCache fromDepositAccount = commonPaymentDraftCache.getValidateDraftCache().getFromDepositAccount();

        super.setActivityId(ACTIVITY_FIN_AND_TRANS_TOP_UP_ID);
        super.setFromAccountNo(eWalletConfirmRequest.getSender().getAccountId());
        super.setFinancialTransferAmount(String.valueOf(eWalletConfirmRequest.getAmount()));
        super.setFinancialTransferMemo(validateRequest.getNote());
        super.setToAccountNo(eWalletConfirmRequest.getReceiver().getAccountId());
        super.setToAccountName(eWalletConfirmRequest.getReceiver().getAccountDisplayName());
        super.setBillerRef1(eWalletConfirmRequest.getReceiver().getProxyValue());
        super.setProxyType(eWalletConfirmRequest.getReceiver().getProxyType());
        super.setProxyValue(eWalletConfirmRequest.getReceiver().getProxyValue());
        super.setTxnType(TXN_TYPE_TOP_UP);

        super.setFromAccountNickname(fromDepositAccount.getProductNickname());
    }
}
