package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.fleetcard;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PaymentAmount {
    private Double dueAmount;
    private Double unbilledAmount;
    private Double fullAmount;
    private Double minAmount;
}
