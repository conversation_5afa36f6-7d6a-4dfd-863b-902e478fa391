
package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
@JsonPropertyOrder({
    "available",
    "ledger"
})
public class Balance {

    @JsonProperty("available")
    private BigDecimal available;
    @JsonProperty("ledger")
    private BigDecimal ledger;

   

}
