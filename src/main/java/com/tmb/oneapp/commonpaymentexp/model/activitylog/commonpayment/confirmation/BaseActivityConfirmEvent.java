package com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.BaseActivityEvent;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpHeaders;


@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public abstract class BaseActivityConfirmEvent extends BaseActivityEvent {
    protected String flow;
    protected String step;
    protected String cardNumber;
    protected String fromAccount;
    protected String billerName;
    protected String reference1;
    protected String reference2;
    protected String amount;
    protected String fee;
    protected String refNo;
    protected String topupRefNo;
    protected String toBankShortName;
    protected String toLinkedAccountNumber;
    protected String statusName;
    protected String reason;
    protected String toFavoriteName;
    protected String paymentMethod;
    protected String wowPoint;
    protected String creditCardPoint;
    protected String couponDiscount;
    protected String totalAmount;
    protected String pinFree;
    protected String ddp;

    public BaseActivityConfirmEvent(String activityTypeId, HttpHeaders headers) {
        super(headers);
        super.setActivityTypeId(activityTypeId);
    }
}
