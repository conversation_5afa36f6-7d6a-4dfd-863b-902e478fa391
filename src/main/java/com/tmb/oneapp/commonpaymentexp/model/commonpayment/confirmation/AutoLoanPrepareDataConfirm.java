package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2AddServiceRequestResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@SuperBuilder
public class AutoLoanPrepareDataConfirm extends BasePrepareDataConfirm implements PrepareDataForConfirm {
    private K2AddServiceRequestResponse k2AddServiceResponse;
}
