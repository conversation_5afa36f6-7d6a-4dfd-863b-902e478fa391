package com.tmb.oneapp.commonpaymentexp.model.cache;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CreditCardAccountInCache {
    private String cardNo;
    private String accountId;
    private String productNameEn;
    private String productNameTh;
    private String productNickname;
    private String cardName;
    private String cardEmbossingName1;
    private CardBalancesAsyncInCache cardBalances;
}
