package com.tmb.oneapp.commonpaymentexp.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WowPoint {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal minPaymentAmount;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal min;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal max;
    private ConversionRateDetail conversionRate;

}
