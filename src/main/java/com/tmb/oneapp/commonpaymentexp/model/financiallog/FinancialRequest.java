package com.tmb.oneapp.commonpaymentexp.model.financiallog;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.MAX_TO_ACCT_ID;

@Data
@Accessors(chain = true)
@ToString
@SuperBuilder
@NoArgsConstructor
public class FinancialRequest {
    @JsonProperty("reference_id")
    protected String referenceID;
    @JsonProperty("crm_id")
    protected String crmId;

    @JsonProperty("transaction_date")
    private String txnDt;
    @JsonProperty("from_account_no")
    private String fromAccNo;
    @JsonProperty("from_account_name")
    private String fromAccName;
    @JsonProperty("from_account_nickname")
    private String fromAccNickName;
    @JsonProperty("to_account_no")
    private String toAccNo;
    @JsonProperty("to_account_name")
    private String toAccName;
    @JsonProperty("to_account_nickname")
    private String toAccNickName;
    @JsonProperty("bankcode")
    private String bankCode;
    @JsonProperty("transaction_amount")
    private String txnAmount;
    @JsonProperty("transaction_fee")
    private String txnFee;
    @JsonProperty("memo")
    private String memo;
    @JsonProperty("activity_type_id")
    private String activityId;

    @JsonProperty("from_account_type")
    protected String fromAccType;
    @JsonProperty("to_account_type")
    protected String toAccType;
    @JsonProperty("channel_id")
    protected String channelId;
    @JsonProperty("transaction_balance")
    protected String txnBal;
    @JsonProperty("transaction_Status")
    protected String txnStatus;
    @JsonProperty("smart_flag")
    protected String smartFlag;
    @JsonProperty("clearing_status")
    protected String clearingStatus;
    @JsonProperty("category_id")
    protected String categoryId;
    @JsonProperty("proxy_id")
    protected String proxyId;
    @JsonProperty("proxy_value")
    protected String proxyValue;
    @JsonProperty("activity_type_id_new")
    protected String activityTypeIdNew;
    @JsonProperty("comp_code")
    protected String compCode;
    @JsonProperty("biller_ref1")
    protected String billerRef1;
    @JsonProperty("biller_ref2")
    protected String billerRef2;
    @JsonProperty("error_cd")
    protected String errorCd;
    @JsonProperty("biller_customer_name")
    protected String billerCustomerName;
    @JsonProperty("biller_balance")
    protected String billerBalance;
    @JsonProperty("activity_ref_id")
    protected String activityRefId;
    @JsonProperty("fin_flex_values1")
    protected String finFlexValues1;
    @JsonProperty("fin_flex_values2")
    protected String finFlexValues2;
    @JsonProperty("fin_flex_values3")
    protected String finFlexValues3;
    @JsonProperty("fin_flex_values4")
    protected String finFlexValues4;
    @JsonProperty("fin_flex_values5")
    protected String finFlexValues5;
    @JsonProperty("fin_linkage_id")
    protected String finLinkageId;


    @JsonProperty("txn_cd")
    protected String txnCd;
    @JsonProperty("note_to_recipient")
    protected String noteToRecipient;
    @JsonProperty("event_id")
    protected String eventId;
    @JsonProperty("txn_type")
    protected String txnType;
    @JsonProperty("create_date")
    protected String createDate;

    @JsonProperty("td_interest_amount")
    protected String tdInterestAmount;
    @JsonProperty("td_tax_amount")
    protected String tdTaxAmount;
    @JsonProperty("td_penalty_amount")
    protected String tdPenaltyAmount;
    @JsonProperty("td_net_amount")
    protected String tdNetAmount;
    @JsonProperty("td_maturity_date")
    protected String tdMaturityDate;

    @JsonProperty("wow_point_discount")
    protected String wowPointDiscount;
    @JsonProperty("wow_point_discount_amount")
    protected String wowPointDiscountAmount;
    @JsonProperty("total_pay_with_wow_amount")
    protected String totalPayWithWowAmount;

    @JsonIgnore
    public String shortenAccountId(String accountId) {
        return accountId.substring(Math.max(0, accountId.length() - MAX_TO_ACCT_ID));
    }
}
