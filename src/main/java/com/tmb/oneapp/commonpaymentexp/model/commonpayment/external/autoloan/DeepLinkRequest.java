package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeepLinkRequest {
    String callFrom;
    String compCode;
    String ref1;
    String ref2;
    String ref3;
    String amount;
    String account;
    String backIconUrl;
    String transType;
    String transId;
    String transMemo;
    String setSchedule;
    String addFavorite;
    String backBtnKey;
    String backBtnUrl;
    String callBackUrl;
}
