package com.tmb.oneapp.commonpaymentexp.model.transactionlog;

import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.cache.BillerInfoResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.TransactionActivities;
import lombok.experimental.SuperBuilder;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.MAX_TO_ACCT_ID;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialActivityLog.ACTIVITY_FIN_AND_TRANS_BILL_PAY_ONLINE_ID;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialActivityLog.ACTIVITY_FIN_AND_TRANS_TOP_UP_ID;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialActivityLog.BILLER_GROUP_BILL_PAY;
import static com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionLogMapper.TRANSACTION_LOG_DEBIT_TRANSACTION;
import static com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionLogMapper.TRANSACTION_LOG_REFERENCE_ACTIVITY_ID_VALUE;

@SuperBuilder
public abstract class TransactionActivityBillPay extends TransactionActivities {
    protected TransactionActivityBillPay(String refId, String crmId, CommonPaymentDraftCache commonPaymentDraftCache, String transactionDateTime) {
        setUpFields(refId, crmId, commonPaymentDraftCache, transactionDateTime);
    }

    private void setUpFields(String refId, String crmId, CommonPaymentDraftCache commonPaymentDraftCache, String transactionDateTime) {
        MasterBillerResponseInCache masterBillerResponse = commonPaymentDraftCache.getValidateDraftCache().getMasterBillerResponse();
        BillerInfoResponseInCache billerInfo = commonPaymentDraftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo();

        setActivityId(ACTIVITY_FIN_AND_TRANS_BILL_PAY_ONLINE_ID);
        setReferenceActivityTypeId(TRANSACTION_LOG_REFERENCE_ACTIVITY_ID_VALUE);
        setChannelId(CommonPaymentExpConstant.MB);
        setTransactionStatus(CommonPaymentExpConstant.ACTIVITY_SUCCESS);
        setFinancialTransferCRDR(TRANSACTION_LOG_DEBIT_TRANSACTION);

        setTransactionDate(transactionDateTime);
        setFinancialTransferRefId(refId);
        setCrmId(crmId);

        setBillerCompCode(billerInfo.getBillerCompCode());
        setBillerNameEn(billerInfo.getNameEn());
        setBillerNameTh(billerInfo.getNameTh());
        setLabelRef1En(masterBillerResponse.getRef1().getLabelEn());
        setLabelRef1Th(masterBillerResponse.getRef1().getLabelTh());

        setToAccountNickname(generateBillerName(
                billerInfo.getBillerCompCode(),
                billerInfo.getNameEn()));

        if (masterBillerResponse.getRef2() != null) {
            setLabelRef2En(masterBillerResponse.getRef2().getLabelEn());
            setLabelRef2Th(masterBillerResponse.getRef2().getLabelTh());
        }
    }

    private String generateBillerName(String compCode, String billerName) {
        return billerName.substring(0, Math.min(billerName.length(), 13)).concat(" (").concat(compCode).concat(")");
    }

    protected String shortenAccountId(String accountId) {
        return accountId.substring(Math.max(0, accountId.length() - MAX_TO_ACCT_ID));
    }

    protected String getActivityTransConfirmId(String billerGroupType) {
        return billerGroupType.equals(BILLER_GROUP_BILL_PAY) ? ACTIVITY_FIN_AND_TRANS_BILL_PAY_ONLINE_ID : ACTIVITY_FIN_AND_TRANS_TOP_UP_ID;
    }

    protected void setBillerRef1(String ref1, String billerCategoryCode) {
        if (BILLER_CATEGORY_CODE_CREDIT_CARD.equals(billerCategoryCode)) {
            setBillerRef1(maskingAndFormatCreditCard(ref1));
        } else {
            setBillerRef1(ref1);
        }
    }

    protected String maskingAndFormatCreditCard(String ref1) {

        if (ref1.length() != 16) {
            return ref1;
        }
        return new StringBuilder(ref1)
                .replace(6, 12, "XXXXXX")
                .insert(4, "-")
                .insert(9, "-")
                .insert(14, "-")
                .toString();
    }
}
