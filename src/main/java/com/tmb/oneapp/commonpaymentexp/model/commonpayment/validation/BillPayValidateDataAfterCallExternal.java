package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BillPayValidateDataAfterCallExternal implements ValidateDataAfterCallExternal {
    private boolean isRequireCommonAuthen;
    private String easyPassAccountName;
    private CommonAuthenticationValidationCommonPaymentResponse commonAuthentication;
    private BigDecimal feeAfterCalculated;
    private BigDecimal totalAmount;
    private CommonAuthenResult commonAuthenResult;
}
