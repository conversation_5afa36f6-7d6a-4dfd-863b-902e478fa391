package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityEWalletConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialEWallet;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityEWallet;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class EWalletLogsConfirm implements LogsForConfirm {
    private ActivityEWalletConfirmationEvent activityEWalletConfirmationEvent;
    private FinancialEWallet financialEWallet;
    private TransactionActivityEWallet transactionActivityEWallet;
    private ActivityCustomSlipCompleteEvent activityCustomSlipCompleteEvent;
}
