package com.tmb.oneapp.commonpaymentexp.model.biller;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BillerInfoResponse {
    private Integer billerId;
    private String nameTh;
    private String nameEn;
    private String billerShortName;
    private String billerCompCode;
    private String imageUrl;
    private String imageFullUrl;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal fee;
    private String billerMethod;
    private String billerGroupType;
    private String startTime;
    private String endTime;
    private String toAccountId;
    private String paymentMethod;
    private Boolean isFullPayment;
    private String serviceType;
    private String transactionType;
    private String billerCategoryCode;
    private String barcodeOnly;
    private String toBankId;
    private String requireAmount;
    private String allowAddFavorite;
    private boolean allowSetSchedule;
    private boolean frequencyOnceOnly;
    private String eSurEncryptedMerchantKey;
    private String fgurl;
    private boolean creditCardFlag;
    private String expiredDate;
    private String effectiveDate;
    private EPayment ePayment;
    private String telcoUrl;
    private String jsonWeb;
    private List<ValidChannel> validChannels;
    private boolean allowCallCommonPayment;
}
