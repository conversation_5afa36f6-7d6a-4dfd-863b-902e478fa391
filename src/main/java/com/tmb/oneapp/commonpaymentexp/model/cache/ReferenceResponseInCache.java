package com.tmb.oneapp.commonpaymentexp.model.cache;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReferenceResponseInCache {
    private String labelTh;
    private String labelEn;
    private Boolean isRequirePay;
    private Boolean isRequireAddFavorite;

    private Boolean isMobile;
}