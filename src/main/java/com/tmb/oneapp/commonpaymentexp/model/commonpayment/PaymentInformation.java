package com.tmb.oneapp.commonpaymentexp.model.commonpayment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.CallbackInitialRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PaymentInformation {
    @NotBlank(message = "entry id is required")
    private String entryId;
    @NotBlank(message = "transaction type is required")
    private String transactionType;
    private String compCode;
    private String fundCode;
    private String deepLinkTransactionId;
    @NotNull(message = "require address flag is required")
    private boolean requireAddressFlag;
    @NotNull(message = "product detail is required")
    @Valid
    private ProductDetail productDetail;
    @NotNull(message = "amount detail is required")
    @Valid
    private AmountDetail amountDetail;
    @Valid
    private CompleteScreenDetail completeScreenDetail;
    private ReviewScreenDetail reviewScreenDetail;
    @Valid
    private Schedule schedule;

    /**
     * This field use only Transaction from partner (Line-man, Shopee etc),
     * when finish transaction, OneApp will call-back to update transaction status.
     */
    private CallbackInitialRequest callback;
}
