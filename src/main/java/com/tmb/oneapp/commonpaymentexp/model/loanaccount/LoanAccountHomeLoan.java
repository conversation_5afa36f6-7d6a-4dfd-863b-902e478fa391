package com.tmb.oneapp.commonpaymentexp.model.loanaccount;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.fleetcard.CreditLimit;
import com.tmb.oneapp.commonpaymentexp.model.dstatement.DebitAccount;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LoanAccountHomeLoan {
    private String id;
    private String type;
    private String branchId;
    private String productId;
    private String title;
    private String currency;
    private DebitAccount debitAccount;
    private Balances balances;
    private CreditLimit creditLimit;
}
