package com.tmb.oneapp.commonpaymentexp.model.financiallog;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.PEABill;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


public class FinancialPEABillPayActivityLog extends FinancialCustomBillPayActivityLog {
    private static final String PARAM_TOTAL_VAT = "TotalVat";
    private static final String AMOUNT_INFO_DELIMITER = "|";
    private static final String DEFAULT_EMPTY_VALUE = "";
    private static final String DEFAULT_AMOUNT = "0.00";
    private static final String PARAM_MSG = "Msg";
    private static final String PARAM_CA_NAME = "CaName";
    private static final String PARAM_CA_ADDRESS = "CaAddress";

    public FinancialPEABillPayActivityLog(String crmId, String refId, CommonPaymentDraftCache commonPaymentDraftCache, String correlationId, String transactionDateTime) {
        super(crmId, refId, commonPaymentDraftCache, correlationId, transactionDateTime);
    }

    @Override
    protected void setFinFlexValues(CommonPaymentDraftCache commonPaymentDraftCache) {
        Map<String, String> params = extractAdditionalParams(commonPaymentDraftCache);
        String peaSlipDate = extractPeaSlipDate(params);
        String amountInfo = buildAmountInfo(commonPaymentDraftCache, params);

        setFinFlexValues1(params.getOrDefault(PARAM_CA_NAME, DEFAULT_EMPTY_VALUE));
        setFinFlexValues2(StringUtils.isEmpty(peaSlipDate) ? null : peaSlipDate);
        setFinFlexValues3(params.getOrDefault(PARAM_CA_ADDRESS, DEFAULT_EMPTY_VALUE));
        setFinFlexValues4(amountInfo);
    }

    private Map<String, String> extractAdditionalParams(CommonPaymentDraftCache commonPaymentDraftCache) {
        return commonPaymentDraftCache.getValidateDraftCache()
                .getExternalConfirmRequest()
                .getOcpBillPaymentConfirmRequest()
                .getAdditionalParams()
                .stream()
                .collect(Collectors.toMap(
                        AdditionalParam::getName,
                        param -> String.valueOf(param.getValue()),
                        (existing, replacement) -> existing
                ));
    }

    private String extractPeaSlipDate(Map<String, String> params) {
        String msg = params.getOrDefault(PARAM_MSG, null);
        if (msg == null) {
            return null;
        }

        List<PEABill> peaBillList = getBillPerMonth(msg);
        return peaBillList.stream()
                .map(PEABill::getEletricityOf)
                .collect(Collectors.joining(" "));
    }

    private String buildAmountInfo(CommonPaymentDraftCache commonPaymentDraftCache, Map<String, String> params) {
        return String.join(AMOUNT_INFO_DELIMITER,
                commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getAmount(),
                params.getOrDefault(PARAM_TOTAL_VAT, DEFAULT_AMOUNT)
        );
    }

    private List<PEABill> getBillPerMonth(String billOfBiller) {
        String[] bills = billOfBiller.split("\\|");
        int months = Integer.parseInt(bills[0]);

        List<PEABill> billPerMonths = new ArrayList<>();
        for (int i = 0; i < months; i++) {
            PEABill billPerMonth = new PEABill();
            String month = bills[i * 4 + 2];
            String eletricityOf = "";
            if (!month.isBlank()) {
                eletricityOf = month.substring(4) + "/" + month.substring(0, 4);
            }

            billPerMonth.setEletricityOf(eletricityOf);

            String vat = bills[i * 4 + 3];
            BigDecimal vatDouble = new BigDecimal(vat);
            billPerMonth.setVat(vatDouble.setScale(2, RoundingMode.HALF_UP).toString());

            String amount = bills[i * 4 + 4];
            BigDecimal amountDouble = new BigDecimal(amount);
            billPerMonth.setAmount(amountDouble.setScale(2, RoundingMode.HALF_UP).toString());

            billPerMonths.add(billPerMonth);
        }

        return billPerMonths;
    }
}
