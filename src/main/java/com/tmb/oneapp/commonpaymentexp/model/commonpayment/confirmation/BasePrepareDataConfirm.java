package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@SuperBuilder
public class BasePrepareDataConfirm implements PrepareDataForConfirm {
    private String transactionTime;
    private CustomerCrmProfile customerCrmProfile;
}
