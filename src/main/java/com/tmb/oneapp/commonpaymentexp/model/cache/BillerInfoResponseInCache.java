package com.tmb.oneapp.commonpaymentexp.model.cache;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BillerInfoResponseInCache {
    private Integer billerId;
    private String nameTh;
    private String nameEn;
    private String billerCompCode;
    private String imageUrl;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal fee;
    private String startTime;
    private String endTime;

    private String billerMethod;
    private String billerGroupType;
    private String paymentMethod;
    private String billerCategoryCode;
}
