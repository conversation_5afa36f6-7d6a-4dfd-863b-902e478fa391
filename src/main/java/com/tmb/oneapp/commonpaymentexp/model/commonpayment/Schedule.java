package com.tmb.oneapp.commonpaymentexp.model.commonpayment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class Schedule {
    @Pattern(regexp = "^$|^([a-zA-Z\\dก-์-._@]+\\s)*[a-zA-Z\\dก-์-._@]*$")
    private String scheduleName;

    private String amountType;
    private String frequencyType;
    private String selectedEnd;
    private String startDate;
    private String endDate;
    private String executeTimes;
    private String dayOfWeek;
    private String dayOfMonth;

    /**
     * Display start date show only on UI Get-payment-method
     */
    private String displayStartDate;

    /**
     * Display end date show only on UI Get-payment-method
     */
    private String displayEndDate;
}
