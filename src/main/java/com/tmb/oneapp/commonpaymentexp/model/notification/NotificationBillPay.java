package com.tmb.oneapp.commonpaymentexp.model.notification;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Accessors(chain = true)
public abstract class NotificationBillPay extends NotificationPayment {
    protected String billerNameEN;
    protected String billerNameTH;
    protected String compcode;
    protected String ref1LabelEN;
    protected String ref1LabelTH;
    protected String ref2LabelEN;
    protected String ref2LabelTH;
    protected String reference1EN;
    protected String reference1TH;
    protected String reference2EN;
    protected String reference2TH;
    protected String typeEN;
    protected String typeTH;
    protected String cardId;

    protected NotificationBillPay(String templateName, String crmId, String correlationId) {
        super(templateName, crmId, correlationId);
    }

}
