package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomBillPayConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialCustomBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityCustomBillPay;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class CustomLogsConfirm implements LogsForConfirm {
    private ActivityCustomBillPayConfirmationEvent activityCustomBillPayConfirmationEvent;
    private FinancialCustomBillPayActivityLog financialCustomBillPayActivityLog;
    private TransactionActivityCustomBillPay transactionActivityCustomBillPay;
    private ActivityCustomSlipCompleteEvent activityCustomSlipCompleteEvent;
}
