package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay;

import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.PrepareDataForValidate;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class BillPayPrepareDataValidate implements PrepareDataForValidate {
    private BillPayConfiguration billPayConfiguration;
    private MasterBillerResponse masterBillerResponse;
    private DepositAccount fromDepositAccount;
    private CreditCardSupplementary fromCreditCardDetail;
    private CustomerCrmProfile customerCrmProfile;
}
