package com.tmb.oneapp.commonpaymentexp.model.biller;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TopUpAmount {
    private String min;
    private String max;
    private Boolean isDisplayPlaceholder;
    private Boolean isKeyIn;
    private List<String> stepAmount;
}