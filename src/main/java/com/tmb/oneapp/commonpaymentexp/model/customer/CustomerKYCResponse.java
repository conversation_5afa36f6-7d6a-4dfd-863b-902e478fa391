package com.tmb.oneapp.commonpaymentexp.model.customer;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerKYCResponse {
    private String rmId;
    private String idNo;
    private String idType;
    private String customerFirstNameEn;
    private String customerFirstNameTh;
    private String customerLastNameEn;
    private String customerLastNameTh;
    private String gender;
    private String email;
    private String emailVerifyStatus;
    private String emailType;
    private String customerType;
}
