package com.tmb.oneapp.commonpaymentexp.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LoanAccount {
    private String loanType;
    private String accountNumber;
    private String productCode;

    private String acctCtrl1;
    private String acctCtrl2;
    private String acctCtrl3;
    private String acctCtrl4;

    private String productOrder;
    private String relationshipCode;
}