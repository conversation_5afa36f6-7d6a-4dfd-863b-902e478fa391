package com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.cache.BillerInfoResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.ReferenceResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.BillPaymentCreditCard;
import com.tmb.oneapp.commonpaymentexp.utils.MaskingUtils;
import com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils;
import com.tmb.oneapp.commonpaymentexp.utils.NumberUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_PAYMENT_METHOD_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_PAYMENT_METHOD_DEPOSIT;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_MOBILE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_WOW_POINT_FORMAT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.BILL_PAYMENT_ACTIVITY_CONFIRM_STEP;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.DASH;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ActivityLogConfirmMapper {
    public static final ActivityLogConfirmMapper INSTANCE = new ActivityLogConfirmMapper();

    private static final List<String> BILLER_CATEGORY_CODE_SHOULD_MASK_REFERENCE_1 = Arrays.asList(BILLER_CATEGORY_CODE_CREDIT_CARD, BILLER_CATEGORY_CODE_MOBILE);
    private static final String VALIDATE_REQUEST_ERROR_MESSAGE_NULL = "Validate request should not be null in cache, Please verify validate action when set cache";
    private static final String MASTER_BILLER_ERROR_MESSAGE_NULL = "MasterBiller should not be null in cache, Please verify validate action when set cache";
    private static final String PAYMENT_INFORMATION_ERROR_MESSAGE_NULL = "Payment information should not be null in cache, Please verify validate action when set cache";
    private static final String EXTERNAL_CONFIRM_ERROR_MESSAGE_NULL = "External confirm request should not be null in cache, Please verify validate action when set cache";

    public ActivityAutoLoanBillPayConfirmationEvent mapToActivityAutoLoanBillPayConfirmationEvent(HttpHeaders headers, CommonPaymentDraftCache draftCache) {
        final var validateDraftCache = NullSafeUtils.requireNonNull(draftCache::getValidateDraftCache);
        final var masterBIller = NullSafeUtils.requireNonNull(validateDraftCache::getMasterBillerResponse, MASTER_BILLER_ERROR_MESSAGE_NULL);
        final var paymentInformation = NullSafeUtils.requireNonNull(draftCache::getPaymentInformation, PAYMENT_INFORMATION_ERROR_MESSAGE_NULL);
        var activityAutoLoan = new ActivityAutoLoanBillPayConfirmationEvent(headers);

        activityAutoLoan.setFromAccount(validateDraftCache.getFromDepositAccount().getAccountNumber());
        activityAutoLoan.setPaymentMethod(ACTIVITY_PAYMENT_METHOD_DEPOSIT);

        activityAutoLoan.setStep(BILL_PAYMENT_ACTIVITY_CONFIRM_STEP);
        activityAutoLoan.setReference1(paymentInformation.getProductDetail().getProductRef1());
        activityAutoLoan.setReference2(paymentInformation.getProductDetail().getProductRef2());
        activityAutoLoan.setBillerName(generateActivityBillerName(
                masterBIller.getBillerInfo().getNameEn(),
                masterBIller.getBillerInfo().getBillerCompCode()));
        activityAutoLoan.setFee(NumberUtils.insertCommas(validateDraftCache.getFeeCalculated()));
        activityAutoLoan.setFlow(getOverrideFlow(draftCache));

        activityAutoLoan.setTotalAmount(NumberUtils.insertCommas(validateDraftCache.getTotalAmount()));
        activityAutoLoan.setCouponDiscount(DASH);
        activityAutoLoan.setWowPoint(DASH);
        activityAutoLoan.setCreditCardPoint(DASH);
        activityAutoLoan.setIsForceFr(DASH);
        activityAutoLoan.setIsForceFr(DASH);
        activityAutoLoan.setPinFree(DASH);

        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            activityAutoLoan.setAmount(NumberUtils.insertCommas(new BigDecimal(validateDraftCache.getExternalConfirmRequest().getAutoLoanOCPBillPaymentConfirmRequest().getAmount())));
            activityAutoLoan.setRefNo(validateDraftCache.getExternalConfirmRequest().getAutoLoanOCPBillPaymentConfirmRequest().getEpayCode());
            activityAutoLoan.setWowPoint(String.format(ACTIVITY_WOW_POINT_FORMAT, validateDraftCache.getWowPointRedeemConfirmRequest().getAmount(),
                    validateDraftCache.getWowPointRedeemConfirmRequest().getPointUnits()));
        } else {
            activityAutoLoan.setAmount(NumberUtils.insertCommas(validateDraftCache.getExternalConfirmRequest().getTopUpETEPaymentRequest().getAmount()));
            activityAutoLoan.setRefNo(validateDraftCache.getExternalConfirmRequest().getTopUpETEPaymentRequest().getEpayCode());
        }

        return activityAutoLoan;
    }

    public ActivityCreditCardBillPayConfirmationEvent mapToActivityCreditCardBillPayConfirmationEvent(HttpHeaders headers, CommonPaymentDraftCache draftCache) {
        final var validateRequest = NullSafeUtils.requireNonNull(draftCache::getValidateRequest, VALIDATE_REQUEST_ERROR_MESSAGE_NULL);
        final var masterBIller = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getMasterBillerResponse(), MASTER_BILLER_ERROR_MESSAGE_NULL);
        final var paymentInformation = NullSafeUtils.requireNonNull(draftCache::getPaymentInformation, PAYMENT_INFORMATION_ERROR_MESSAGE_NULL);
        final BillPaymentCreditCard billPaymentExternalConfirmRequest = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getExternalConfirmRequest().getCreditCardConfirmRequest().getBillPayment(), EXTERNAL_CONFIRM_ERROR_MESSAGE_NULL);

        var activityCreditCard = new ActivityCreditCardBillPayConfirmationEvent(headers);

        if (validateRequest.isPayWithCreditCard()) {
            activityCreditCard.setFromAccount(validateRequest.getCreditCard().getAccountId());
            activityCreditCard.setPaymentMethod(ACTIVITY_PAYMENT_METHOD_CREDIT_CARD);
        } else {
            activityCreditCard.setFromAccount(draftCache.getValidateDraftCache().getFromDepositAccount().getAccountNumber());
            activityCreditCard.setPaymentMethod(ACTIVITY_PAYMENT_METHOD_DEPOSIT);
        }

        activityCreditCard.setStep(BILL_PAYMENT_ACTIVITY_CONFIRM_STEP);
        activityCreditCard.setReference1(MaskingUtils.maskWithLastFour(paymentInformation.getProductDetail().getProductRef1()));
        activityCreditCard.setReference2(paymentInformation.getProductDetail().getProductRef2());
        activityCreditCard.setAmount(NumberUtils.insertCommas((
                billPaymentExternalConfirmRequest.getAmount()))
        );
        activityCreditCard.setBillerName(generateActivityBillerName(
                masterBIller.getBillerInfo().getNameEn(),
                masterBIller.getBillerInfo().getBillerCompCode()
        ));
        activityCreditCard.setFee(NumberUtils.insertCommas(NullSafeUtils.getSafeNull(() -> draftCache.getValidateDraftCache().getFeeCalculated())));
        activityCreditCard.setFlow(getOverrideFlow(draftCache));
        activityCreditCard.setRefNo(billPaymentExternalConfirmRequest.getEpayCode());
        activityCreditCard.setTotalAmount(NumberUtils.insertCommas(draftCache.getValidateDraftCache().getTotalAmount()));

        activityCreditCard.setCouponDiscount(DASH);
        activityCreditCard.setWowPoint(DASH);
        activityCreditCard.setCreditCardPoint(DASH);
        activityCreditCard.setIsForceFr(DASH);
        activityCreditCard.setIsForceDipchip(DASH);
        activityCreditCard.setPinFree(DASH);
        return activityCreditCard;
    }

    public ActivityLoanBillPayConfirmationEvent mapToActivityLoanBillPayConfirmationEvent(HttpHeaders headers, CommonPaymentDraftCache draftCache) {
        final var validateRequest = NullSafeUtils.requireNonNull(draftCache::getValidateRequest, VALIDATE_REQUEST_ERROR_MESSAGE_NULL);
        final var validateDraftCache = NullSafeUtils.requireNonNull(draftCache::getValidateDraftCache);
        final var masterBiller = NullSafeUtils.requireNonNull(validateDraftCache::getMasterBillerResponse, MASTER_BILLER_ERROR_MESSAGE_NULL);
        final var paymentInformation = NullSafeUtils.requireNonNull(draftCache::getPaymentInformation, PAYMENT_INFORMATION_ERROR_MESSAGE_NULL);
        final var externalETEConfirmRequest = NullSafeUtils.requireNonNull(() -> validateDraftCache.getExternalConfirmRequest().getOcpBillPaymentConfirmRequest(), EXTERNAL_CONFIRM_ERROR_MESSAGE_NULL);
        final boolean isPayWithWowPoint = WowPointUtils.isWowPointTransaction(validateRequest, draftCache.getCommonPaymentRule());

        final String billerNameForSaveActLog = generateActivityBillerName(masterBiller.getBillerInfo().getNameEn(), masterBiller.getBillerInfo().getBillerCompCode());

        var activityLoan = new ActivityLoanBillPayConfirmationEvent(headers);

        activityLoan.setFromAccount(validateDraftCache.getFromDepositAccount().getAccountNumber());
        activityLoan.setPaymentMethod(ACTIVITY_PAYMENT_METHOD_DEPOSIT);

        activityLoan.setStep(BILL_PAYMENT_ACTIVITY_CONFIRM_STEP);
        activityLoan.setReference1(paymentInformation.getProductDetail().getProductRef1());
        activityLoan.setReference2(paymentInformation.getProductDetail().getProductRef2());
        activityLoan.setAmount(NumberUtils.insertCommas(externalETEConfirmRequest.getAmount()));
        activityLoan.setBillerName(billerNameForSaveActLog);
        activityLoan.setFee(NumberUtils.insertCommas(validateDraftCache.getFeeCalculated()));
        activityLoan.setFlow(getOverrideFlow(draftCache));
        activityLoan.setRefNo(externalETEConfirmRequest.getEpayCode());
        activityLoan.setTotalAmount(NumberUtils.insertCommas(validateDraftCache.getTotalAmount()));

        activityLoan.setCouponDiscount(DASH);
        activityLoan.setCreditCardPoint(DASH);
        activityLoan.setDdp(DASH);
        activityLoan.setPinFree(DASH);
        activityLoan.setWowPoint(DASH);

        if (isPayWithWowPoint) {
            final var wowPointConfirmRequest = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest(), "WowPointRedeemConfirmRequest should not be null in cache, Please verify validate action when set cache");
            activityLoan.setWowPoint(String.format(ACTIVITY_WOW_POINT_FORMAT, wowPointConfirmRequest.getAmount(), wowPointConfirmRequest.getPointUnits()));
        }

        if (isShouldMaskingReference1(masterBiller)) {
            activityLoan.setReference1(MaskingUtils.maskWithLastFour(paymentInformation.getProductDetail().getProductRef1()));
        }

        return activityLoan;
    }


    private static String generateActivityBillerName(String billerName, String compCode) {
        return "%s (%s)".formatted(billerName, compCode);
    }

    private static String getOverrideFlow(CommonPaymentDraftCache draftCache) {
        var validateRequest = draftCache.getValidateRequest();
        var deepLinkRequestInCache = draftCache.getDeepLinkRequest();

        boolean isTransactionFromDeepLink = deepLinkRequestInCache != null;
        String overrideFlow = validateRequest.getFlow();
        if (isTransactionFromDeepLink) {
            overrideFlow = Optional.of(deepLinkRequestInCache).map(DeepLinkRequestInCache::getCallFrom).orElse("call-from-in-deep-link-is-null");
        }
        return overrideFlow;
    }

    private static boolean isShouldMaskingReference1(MasterBillerResponseInCache masterBiller) {
        final String billerCategoryCode = Optional.ofNullable(masterBiller.getBillerInfo()).map(BillerInfoResponseInCache::getBillerCategoryCode).orElse(null);
        final boolean isRef1Mobile = Optional.ofNullable(masterBiller.getRef1()).map(ReferenceResponseInCache::getIsMobile).orElse(false);

        return BILLER_CATEGORY_CODE_SHOULD_MASK_REFERENCE_1.contains(billerCategoryCode) || isRef1Mobile;
    }
}
