package com.tmb.oneapp.commonpaymentexp.model.financiallog;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.MWABill;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


public class FinancialMWABillPayActivityLog extends FinancialCustomBillPayActivityLog {
    private static final String PARAM_TOTAL_INTEREST = "TotalInterest";
    private static final String BILL_PAYMENT_ADDITIONAL_PARAM_MESSAGE = "Message";
    private static final String PARAM_UI = "UI";
    private static final String AMOUNT_INFO_DELIMITER = "|";
    private static final String DEFAULT_EMPTY_VALUE = "";
    private static final String DEFAULT_AMOUNT = "0.00";
    private static final int SCALE = 2;

    public FinancialMWABillPayActivityLog(String crmId, String refId, CommonPaymentDraftCache commonPaymentDraftCache,
            String correlationId, String transactionDateTime) {
        super(crmId, refId, commonPaymentDraftCache, correlationId, transactionDateTime);
    }

    @Override
    protected void setFinFlexValues(CommonPaymentDraftCache commonPaymentDraftCache) {
        Map<String, String> params = extractAdditionalParams(commonPaymentDraftCache);
        
        String amount = calculateAmount(commonPaymentDraftCache);
        String customerName = params.getOrDefault(PARAM_UI, DEFAULT_EMPTY_VALUE);
        String totalInterest = params.getOrDefault(PARAM_TOTAL_INTEREST, DEFAULT_AMOUNT);
        List<MWABill> billPerMonths = getBillPerMonth(params.getOrDefault(BILL_PAYMENT_ADDITIONAL_PARAM_MESSAGE, DEFAULT_EMPTY_VALUE));

        setFinFlexValues1(customerName);
        setFinFlexValues2(billPerMonths.isEmpty() ? null : billPerMonths.get(0).getBillNumber());
        setFinFlexValues3(null);
        setFinFlexValues4(buildAmountInfo(amount, totalInterest));
    }

    private Map<String, String> extractAdditionalParams(CommonPaymentDraftCache commonPaymentDraftCache) {
        return commonPaymentDraftCache.getValidateDraftCache()
                .getExternalConfirmRequest()
                .getOcpBillPaymentConfirmRequest()
                .getAdditionalParams()
                .stream()
                .collect(Collectors.toMap(
                        AdditionalParam::getName,
                        param -> String.valueOf(param.getValue()),
                        (existing, replacement) -> existing
                ));
    }

    private String calculateAmount(CommonPaymentDraftCache commonPaymentDraftCache) {
        var confirmRequest = commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest();
        BigDecimal totalAmount = new BigDecimal(confirmRequest.getBalance().getMax());
        BigDecimal vat = new BigDecimal(confirmRequest.getRef2());
        return totalAmount.subtract(vat)
                .setScale(SCALE, RoundingMode.HALF_UP)
                .toString();
    }

    private List<MWABill> getBillPerMonth(String billOfBiller) {
        if (billOfBiller.equals(DEFAULT_EMPTY_VALUE)) {
            return new ArrayList<>();
        }

        String[] bills = billOfBiller.split("\\" + AMOUNT_INFO_DELIMITER);
        int months = Integer.parseInt(bills[0]);

        List<MWABill> billPerMonths = new ArrayList<>();
        for (int i = 0; i < months; i++) {
            MWABill billPerMonth = new MWABill();
            billPerMonth.setBillNumber(bills[i * 4 + 2]);

            String vat = bills[i * 4 + 3];
            BigDecimal vatDecimal = new BigDecimal(vat);
            billPerMonth.setVat(vatDecimal.setScale(SCALE, RoundingMode.HALF_UP).toString());

            String amount = bills[i * 4 + 4];
            BigDecimal amountDecimal = new BigDecimal(amount);
            billPerMonth.setAmount(amountDecimal.setScale(SCALE, RoundingMode.HALF_UP).toString());

            billPerMonths.add(billPerMonth);
        }

        return billPerMonths;
    }

    private String buildAmountInfo(String amount, String totalInterest) {
        return String.join(AMOUNT_INFO_DELIMITER, amount, totalInterest);
    }
}
