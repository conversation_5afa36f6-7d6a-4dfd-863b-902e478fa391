package com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization;

import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.DeepLinkRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class InitialPrepareData {
    private MasterBillerResponse masterBillerResponse;
    private CommonPaymentConfig commonPaymentConfig;
    private DeepLinkRequest deepLinkRequest;
}
