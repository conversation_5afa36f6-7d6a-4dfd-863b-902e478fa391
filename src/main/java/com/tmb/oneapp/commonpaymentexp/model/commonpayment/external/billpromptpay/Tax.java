
package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Setter
@Getter
@JsonPropertyOrder({ "amount", "rate" })
public class Tax {

	@JsonProperty("amount")
	private BigDecimal amount;
	@JsonProperty("rate")
	private BigDecimal rate;
	private String type;

}
