package com.tmb.oneapp.commonpaymentexp.model.customer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tmb.common.model.customer.SourceOfIncome;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CustomerDetailResponse {
    @JsonProperty("rm_id")
    private String rmId;
    @JsonProperty("id_no")
    private String idNo;

    @JsonProperty("id_type")
    private String idType;

    @JsonProperty("customer_type")
    private String customerType;
    @JsonProperty("customer_name_en")
    private String customerNameEn;
    @JsonProperty("customer_name_th")
    private String customerNameTh;

    @JsonProperty("customer_title_en")
    private String customerTitleEn;
    @JsonProperty("customer_title_th")
    private String customerTitleTh;
    @JsonProperty("customer_first_name_en")
    private String customerFirstNameEn;
    @JsonProperty("customer_first_name_th")
    private String customerFirstNameTh;
    @JsonProperty("customer_middle_name_en")
    private String customerMiddleNameEn;
    @JsonProperty("customer_middle_name_th")
    private String customerMiddleNameTh;
    @JsonProperty("customer_last_name_en")
    private String customerLastNameEn;
    @JsonProperty("customer_last_name_th")
    private String customerLastNameTh;

    @JsonProperty("title_code")
    private String titleCode;
    @JsonProperty("nationality")
    private String nationality;

    @JsonProperty("phone_no_full")
    private String phoneNoFull;
    @JsonProperty("gender")
    private String gender;
    @JsonProperty("marital_status")
    private String maritalStatus;
    @JsonProperty("mobile_number")
    private String mobileNumber;
    @JsonProperty("email")
    private String email;
    @JsonProperty("email_verify_status")
    private String emailVerifyStatus;
    @JsonProperty("email_type")
    private String emailType;
    @JsonProperty("birth_date")
    private String birthDate;
    @JsonProperty("issue_date")
    private String issueDate;
    @JsonProperty("expiry_date")
    private String expiryDate;
    @JsonProperty("contact_address")
    private String contactAddress;
    @JsonProperty("register_address")
    private String registerAddress;
    @JsonProperty("office_address")
    private String officeAddress;
    @JsonProperty("home_phone_no")
    private String homePhoneNo;
    @JsonProperty("office_phone_no")
    private String officePhoneNo;
    @JsonProperty("ekyc_flag")
    private String ekycFlag;
    // Attributes for other service detail
    @JsonProperty("kyc_limited_flag")
    private String kycLimitedFlag;
    @JsonProperty("kyc_block_date")
    private String kycBlockDate;
    @JsonProperty("kyc_last_update_date")
    private String kycLastUpdateDate;
    @JsonProperty("kyc_review_last_mtn_date")
    private String kycReviewLastMtnDate;
    @JsonProperty("ekyc_identify_assurance_level")
    private String ekycIdentifyAssuranceLevel;

    @JsonProperty("pdpa_flag")
    private String pdpaFlag;
    @JsonProperty("pdpa_accepted_version")
    private String pdpaAcceptedVersion;
    @JsonProperty("pdpa_from_channel")
    private String pdpaFromChannel;
    @JsonProperty("pdpa_last_updated_date")
    private String pdpaLastUpdatedDate;
    @JsonProperty("pdpa_signup_date")
    private String pdpaSignupDate;

    @JsonProperty("data_analytic_flag")
    private String dataAnalyticFlag;
    @JsonProperty("data_analytic_accepted_version")
    private String dataAnalyticAcceptedVersion;
    @JsonProperty("data_analytic_from_channel")
    private String dataAnalyticFromChannel;
    @JsonProperty("data_analytic_last_updated_date")
    private String dataAnalyticLastUpdatedDate;
    @JsonProperty("data_analytic_signup_date")
    private String dataAnalyticSignupDate;

    @JsonProperty("market_conduct_flag")
    private String marketConductFlag;
    @JsonProperty("market_conduct_accepted_version")
    private String marketConductAcceptedVersion;
    @JsonProperty("market_conduct_from_channel")
    private String marketConductFromChannel;
    @JsonProperty("market_conduct_last_updated_date")
    private String marketConductLastUpdatedDate;
    @JsonProperty("market_conduct_signup_date")
    private String marketConductSignupDate;

    @JsonProperty("occupation_code")
    private String occupationCode;
    @JsonProperty("education_code")
    private String educationCode;
    @JsonProperty("business_type_desc")
    private String businessTypeDesc;
    @JsonProperty("salary")
    private String salary;
    @JsonProperty("working_place")
    private String workingPlace;
    @JsonProperty("country_of_income")
    private String countryOfIncome;
    @JsonProperty("source_of_incomes")
    private List<SourceOfIncome> sourceOfIncomes;

    // Attributes for investment service detail
    @JsonProperty("fatca_flag")
    private String fatcaFlag;

    @JsonProperty("customer_level")
    private String customerLevel;
    @JsonProperty("customer_status")
    private String customerStatus;

    @JsonProperty("nationality_2")
    private String nationalitySecond;

    @JsonProperty("amlo_refuse_flag")
    private String amloRefuseFlag;

    @JsonProperty("business_type_code")
    private String businessTypeCode;

    @JsonProperty("office_address_data")
    private AddreseWithPhone officeAddressData;

    @JsonProperty("primary_address_data")
    private AddreseWithPhone primaryAddressData;

    @JsonProperty("registered_addressData")
    private AddreseWithPhone registeredAddressData;

    @JsonProperty("phones")
    private List<Phone> phones;

    @JsonProperty("full_fill_flag")
    private String fullFillFlag;

    @JsonProperty("ref_id")
    private String refId;

    @JsonProperty("customer_stage")
    private String customerStage;
    @JsonProperty("employment_status")
    private String employmentStatus;
    @JsonProperty("employment_characteristic")
    private String employmentCharacteristic;
    @JsonProperty("cust_type_id")
    private String custTypeId;
    @JsonProperty("id_issue_country")
    private String idIssueCountry;
    @JsonProperty("entry_branch")
    private String entryBranch;
    @JsonProperty("place_of_birth")
    private String placeOfBirth;
    @JsonProperty("cc_id")
    private String ccId;

}