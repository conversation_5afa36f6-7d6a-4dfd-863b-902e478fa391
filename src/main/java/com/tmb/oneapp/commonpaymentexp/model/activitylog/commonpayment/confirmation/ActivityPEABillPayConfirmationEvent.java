package com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import org.springframework.http.HttpHeaders;

public class ActivityPEABillPayConfirmationEvent extends ActivityCustomBillPayConfirmationEvent {
    public ActivityPEABillPayConfirmationEvent(String activityTypeId, HttpHeaders headers, CommonPaymentDraftCache cache) {
        super(activityTypeId, headers, cache);
        setUpFields(cache);
    }

    private void setUpFields(CommonPaymentDraftCache cache) {
        setReference2(cache.getPaymentInformation().getProductDetail().getProductRef2());
    }

}
