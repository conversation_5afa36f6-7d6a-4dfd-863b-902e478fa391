package com.tmb.oneapp.commonpaymentexp.model.customer;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class Address {

    @JsonProperty("ident_address_reg")
    public String identAddressReg;
    @JsonProperty("ident_address_pri")
    public String identAddressPri;
    @JsonProperty("ident_address_off")
    public String identAddressOff;
    @JsonProperty("ident_employment")
    public String identEmployment;
    @JsonProperty("regis_addr_flag")
    public String regisAddrFlag;
    @JsonProperty("curr_addr_flag")
    public String currAddrFlag;
    @JsonProperty("work_addr_flag")
    public String workAddrFlag;
    @JsonProperty("address_no")
    public String addressNo;
    @JsonProperty("build_village_name")
    public String buildVillageName;
    @JsonProperty("room_no")
    public String roomNo;
    @JsonProperty("floor")
    public String floor;
    @JsonProperty("moo")
    public String moo;
    @JsonProperty("soi")
    public String soi;
    @JsonProperty("road")
    public String road;
    @JsonProperty("sub_district")
    public String subDistrict;
    @JsonProperty("district")
    public String district;
    @JsonProperty("province")
    public String province;
    @JsonProperty("postal_code")
    public String postalCode;
    @JsonProperty("country")
    public String country;
    @JsonProperty("system_create")
    public String systemCreate;
    @JsonProperty("create_date")
    public String createDate;
    @JsonProperty("create_by")
    public String createBy;
    @JsonProperty("update_date")
    public String updateDate;
    @JsonProperty("update_by")
    public String updateBy;
    @JsonProperty("working_place")
    public String workingPlace;
    @JsonProperty("accom_owner_type")
    public String accomOwnerType;
    @JsonProperty("addr_seq")
    public String addrSeq;
    @JsonProperty("province_code")
    public String provinceCode;
    @JsonProperty("province_other")
    public String provinceOther;
}