package com.tmb.oneapp.commonpaymentexp.model.biller;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.common.model.AisMobilePackage;
import com.tmb.common.model.BillValidation;
import com.tmb.common.model.BillerBarcodeSpecified;
import com.tmb.common.model.BillerCategory;
import com.tmb.common.model.BillerCreditcardMerchant;
import com.tmb.common.model.BillpayCategoryInfo;
import com.tmb.common.model.CreditCardValidation;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BillPayConfiguration {
    private String id;

    private List<String> topupSuggestList;

    private List<String> billpaySuggestList;

    private List<BillerCategory> billpayCategoryList;

    private List<String> billerAmountFullSpecified;

    private CreditCardValidation creditCardValidation;

    private BillValidation loanValidation;

    private List<BillerBarcodeSpecified> billerBarcodeSpecified;

    private List<BillpayCategoryInfo> billpayCategoryInfo;

    private List<String> billerExcludeList;

    private String masterBillerHash;

    private Map<String, List<String>> billerSortingId;

    private BillValidation promptpayValidation;

    private BillValidation billerOfflineValidation;

    private List<Map<String, String>> deeplinkCallFromMapping;

    private List<String> billerSpecialValidation;

    private List<BillerCreditcardMerchant> billerCreditcardMerchant;

    private String masterBillerKey;

    private List<String> billerErrorCodeAllowAddFavorite;

    private AisMobilePackage aisMobilePackage;
}
