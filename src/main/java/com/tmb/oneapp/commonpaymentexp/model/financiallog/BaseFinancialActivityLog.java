package com.tmb.oneapp.commonpaymentexp.model.financiallog;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.cache.BillerInfoResponseInCache;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.BANK_TMB_VALIDATE_DATEFORMAT;

@Getter
@Setter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@SuperBuilder
public abstract class BaseFinancialActivityLog extends FinancialRequest {
    private static final String STATUS_SUCCESS_CODE = "000";
    private static final String MB = "mb";
    private static final String CLEARING_STATUS = "01";
    private static final String TTB_BANK_CODE = "11";
    private static final String SMART_FLG = "";
    private static final String CATEGORY_BILL_PAY_ID = "3";
    private static final String D00 = "000";

    public static final String BILLER_GROUP_BILL_PAY = "0";
    public static final String BILLER_GROUP_TOP_UP = "1";
    public static final String ACTIVITY_FIN_AND_TRANS_BILL_PAY_ONLINE_ID = "027";
    public static final String ACTIVITY_FIN_AND_TRANS_TOP_UP_ID = "030";
    public static final String TXN_TYPE_TOP_UP = "005";
    public static final String TXN_TYPE_BILL = "002";

    protected BaseFinancialActivityLog(String crmId, String refId, String correlationId, String transactionDateTime, BillerInfoResponseInCache billerInfoResponse) {
        setUpFields(crmId, refId, correlationId, transactionDateTime, billerInfoResponse.getBillerGroupType(), billerInfoResponse.getNameEn());
    }

    private void setUpFields(String crmId, String refId, String correlationId, String transactionDateTime, String billerGroupType, String billerEnName) {
        setTxnDt(transactionDateTime);
        setReferenceID(refId);
        setCrmId(crmId);
        setActivityRefId(correlationId);

        setErrorCd(STATUS_SUCCESS_CODE);
        setActivityTypeIdNew(ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID); // todo please check it again
        setChannelId(MB);
        setTxnStatus(CommonPaymentExpConstant.ACTIVITY_SUCCESS);
        setActivityId(getActivityFinAndTransConfirmId(billerGroupType));
        setClearingStatus(CLEARING_STATUS);
        setBankCode(TTB_BANK_CODE);
        setSmartFlag(SMART_FLG);
        setCategoryId(CATEGORY_BILL_PAY_ID);
        setToAccType(D00);
        setToAccName(billerEnName);
        setTxnType(getTxnTypeId(billerGroupType));
    }

    protected String getActivityFinAndTransConfirmId(String billerGroupType) {
        return BILLER_GROUP_BILL_PAY.equals(billerGroupType) ? ACTIVITY_FIN_AND_TRANS_BILL_PAY_ONLINE_ID : ACTIVITY_FIN_AND_TRANS_TOP_UP_ID;
    }

    protected String getActivityConfirmId(String billerGroupType) {
        //todo cancel to use it. will be removed when move all logic to use FinancialLogMapper
        return BILLER_GROUP_BILL_PAY.equals(billerGroupType) ? "ACTIVITY_LOG_BILL_PAY_CONFIRM_ID" : "ACTIVITY_LOG_TOP_UP_CONFIRM_ID";
    }

    protected String getTxnTypeId(String billerGroupType) {
        return BILLER_GROUP_BILL_PAY.equals(billerGroupType) ? TXN_TYPE_BILL : TXN_TYPE_TOP_UP;
    }

    public void setFailureStatusWithErrorCodeFromException(Exception e) {
        setTxnStatus(CommonPaymentExpConstant.ACTIVITY_FAILURE);
        if (e instanceof TMBCommonException ee) {
            setErrorCd(ee.getErrorCode());
        } else {
            setErrorCd(null);
        }
    }

    @JsonIgnore
    public String getDateFormatFrontEndFromTxnDt() {
        Date dateFromString = new Date(Long.parseLong(getTxnDt()));
        SimpleDateFormat dateFormat = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
        return dateFormat.format(dateFromString);
    }
}
