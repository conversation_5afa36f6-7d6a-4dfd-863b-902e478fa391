package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TopUpETEPaymentRequest {
    private BigDecimal amount;
    private String currency;
    private String bankId;
    private String branchId;
    private String compCode;
    private String paymentId;
    private String bankReferenceId;

    @JsonProperty("reference_1")
    private String reference1;

    @JsonProperty("reference_2")
    private String reference2;

    @JsonProperty("reference_3")
    private String reference3;

    @JsonProperty("reference_4")
    private String reference4;
    private TopUpAccount fromAccount;
    private TopUpAccount toAccount;
    private List<AdditionalParam> additionalParams;
    private TopUpFee fee;
    private String pmtRefIdent;
    private String invoiceNum;
    private String epayCode;
    private String tranCode;

    private String tellerId;
    private String transactionId;
    private AutoLoanDetail autoLoanDetails;
    private AutoLoanSender sender;
}
