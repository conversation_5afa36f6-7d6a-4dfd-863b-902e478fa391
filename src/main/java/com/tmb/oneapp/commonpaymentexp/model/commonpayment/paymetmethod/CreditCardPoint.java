package com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CardPoints;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CreditCardPoint extends CardPoints {

    private String cardNo;
    private String accountId;
    private String productNameEn;
    private String productNameTh;

}
