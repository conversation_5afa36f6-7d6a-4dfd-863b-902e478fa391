package com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.WowPoint;
import com.tmb.oneapp.commonpaymentexp.model.cache.CreditCardSupplementaryInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PaymentMethodCommonPaymentResponse {
    private PaymentInformation paymentInformation;
    private Address address;
    private String defaultPaymentOverride;
    private boolean depositAccountFlag;
    private List<DepositAccountInCache> depositAccountList;
    private boolean creditCardFlag;
    private List<CreditCardSupplementaryInCache> creditCardList;
    private boolean creditCardPointFlag;
    private List<CreditCardPoint> creditCardPoint;
    private boolean creditCardInstallmentFlag;
    private List<CreditCardInstallment> creditCardInstallmentList;
    private boolean creditCardOtherFlag;
    private boolean qrCodeFlag;
    private boolean couponFlag;
    private boolean wowPointFlag;
    private WowPoint wowPoint;
    private MasterBillerResponseInCache masterBillerResponse;
    private boolean partnerFlag;
}
