package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ValidationCommonPaymentRequest {

    @NotBlank(message = "transaction_id should not be blank")
    private String transactionId;
    @NotBlank(message = "flow should not be blank")
    private String flow;

    @Pattern(regexp = "[^\"'=<>~\\\\]*$", message = "Note must not contain invalid characters: \" ' = < > ~ \\")
    private String note;

    private DepositValidationCommonPaymentRequest deposit;
    private CreditCardValidationCommonPaymentRequest creditCard;
    private EDonationValidationCommonPaymentRequest eDonation;
    private TopUpTelcoValidationCommonPaymentRequest topUpTelco;
    private WowPointValidationCommonPaymentRequest wowPoint;

    @JsonIgnore
    public BigDecimal getAmount() {
        if (this.isPayWithCreditCard()) {
            return this.creditCard.getAmount();
        } else if (this.deposit != null) {
            return this.deposit.getAmount();
        } else {
            return null;
        }
    }

    @JsonIgnore
    public boolean isPayWithCreditCard() {
        return this.creditCard != null && this.creditCard.isPayWithCreditCardFlag();
    }
}
