package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@SuperBuilder(toBuilder = true)
@ToString
public class OCPBillRequest {
    String rbaNo;
    String requestId;
    String requestDateTime;
    String paymentId;
    String bankRefId;
    String appId;
    String channel;
    String bankId;
    String branchId;
    String tellerId;
    String amount;
    String currency;
    String compCode;
    String ref1;
    String ref2;
    String ref3;
    String ref4;
    String tranCode;
    String epayCode;
    OCPAccountPayment fromAccount;
    OCPAccountPayment toAccount;
    OCPFee fee;
    OCPAccount account;
    OCPBiller biller;
    List<AdditionalParam> additionalParams;
    WaiveOCP waive;
    String pmtRefIdent;
    String invoiceNum;
    OCPCard card;
    OCPMerchant merchant;
    OCPBalance balance;
}
