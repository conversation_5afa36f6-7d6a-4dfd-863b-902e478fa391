package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ExternalConfirmResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * Response from TopUpETE service
 */
@Getter
@Setter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
//TODO change field name
public class TopUpETEResponse implements ExternalConfirmResponse {
    private Status status;
    private TopUpETETransaction transaction;
}
