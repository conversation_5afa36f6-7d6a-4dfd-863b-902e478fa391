package com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.BaseActivityBillPayEvent;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_PAYMENT_METHOD_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_PAYMENT_METHOD_DEPOSIT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_WOW_POINT_FORMAT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.BILL_PAYMENT_ACTIVITY_CONFIRM_STEP;


public class ActivityBillPayOCPConfirmationEvent extends BaseActivityBillPayEvent {
    public ActivityBillPayOCPConfirmationEvent(String activityTypeId, HttpHeaders headers, CommonPaymentDraftCache cache) {
        super(activityTypeId, headers);
        setUpFields(cache);
    }

    private void setUpFields(CommonPaymentDraftCache cache) {
        if (cache.getValidateRequest().getCreditCard().isPayWithCreditCardFlag()) {
            setFromAccount(cache.getValidateRequest().getCreditCard().getAccountId());
            setPaymentMethod(ACTIVITY_PAYMENT_METHOD_CREDIT_CARD);
        } else {
            setFromAccount(cache.getValidateDraftCache().getFromDepositAccount().getAccountNumber());
            setPaymentMethod(ACTIVITY_PAYMENT_METHOD_DEPOSIT);
        }

        setStep(BILL_PAYMENT_ACTIVITY_CONFIRM_STEP);
        setReference1(
                cache.getPaymentInformation().getProductDetail().getProductRef1(),
                cache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerCategoryCode(),
                cache.getValidateDraftCache().getMasterBillerResponse().getRef1().getIsMobile()
        );
        setReference2(cache.getPaymentInformation().getProductDetail().getProductRef2());
        setBillerName(generateActivityBillerName(
                cache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getNameEn(),
                cache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerCompCode()));
        setAmount(insertCommas((new BigDecimal(cache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getAmount()))));
        setFee(insertCommas(cache.getValidateDraftCache().getFeeCalculated()));
        setTotalAmount(insertCommas(cache.getValidateDraftCache().getTotalAmount()));
        setFlow(getOverrideFlow(cache));
        setRefNo(cache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getEpayCode());

        setWowPoint(WowPointUtils.isWowPointTransaction(cache.getValidateRequest(), cache.getCommonPaymentRule()) ? String.format(ACTIVITY_WOW_POINT_FORMAT,
                cache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getAmount(),
                cache.getValidateDraftCache().getWowPointRedeemConfirmRequest().getPointUnits())
                : "-");
        setCouponDiscount("-");
        setCreditCardPoint("-");

        setIsForceFr("-");
        setIsForceDipchip("-");
        setPinFree("-");
    }

}
