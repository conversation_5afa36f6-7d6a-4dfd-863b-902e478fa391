package com.tmb.oneapp.commonpaymentexp.model.commonpayment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PhraseDetail {

    private String labelEn;
    private String labelTh;
    @NotBlank(message = "value en is required.")
    private String valueEn;
    @NotBlank(message = "value th is required.")
    private String valueTh;

}
