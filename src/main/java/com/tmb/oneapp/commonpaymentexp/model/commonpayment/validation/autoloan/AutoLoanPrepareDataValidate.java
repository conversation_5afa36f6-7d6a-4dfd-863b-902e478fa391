package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.autoloan;

import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.LoanAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.PrepareDataForValidate;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class AutoLoanPrepareDataValidate implements PrepareDataForValidate {
    private BillPayConfiguration billPayConfiguration;
    private MasterBillerResponse masterBillerResponse;
    private DepositAccount fromDepositAccount;
    private CustomerCrmProfile customerCrmProfile;
    private CustomerKYCResponse customerKYCResponse;
    private List<LoanAccount> hpAccountList;
}
