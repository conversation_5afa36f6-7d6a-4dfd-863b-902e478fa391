package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external;

import com.tmb.common.cache.Transaction;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Receiver;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Sender;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Terminal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.promptpay.PromptPayExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.promptpay.PromptPayPrepareDataValidate;
import org.apache.commons.lang3.StringUtils;

import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.CATEGORY_E_DONATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.ALPHABET_I;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.B011B;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.PROMPTPAY_REF_SEQ;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.SENDER_TYPE_KEY_IN;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.TERMINAL_TYPE;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.TMBO;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.TMB_BANK_SHORT_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.TRANS_ID_DATE_FORMAT;
import static com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.BillPromptPayValidationProcessor.THAI_NATION_ID_TYPE;
import static com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.BillPromptPayValidationProcessor.TTB_BANK_CODE_3DIGIT_ISO20022;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PromptPayETEMapper {
    private final PromptPayETEConfirmRequestMapper promptPayETEConfirmRequestMapper;

    public PromptPayETEValidateRequest toPromptPayETERequestForValidatePromptPayPayment(ValidationCommonPaymentRequest request, CommonPaymentDraftCache cache, PromptPayPrepareDataValidate prepareData, boolean isAmountExceedAmloThreshold, boolean isShouldCallToETEISO20022) {
        PromptPayETEValidateRequest result = new PromptPayETEValidateRequest();
        final String compCode = cache.getPaymentInformation().getCompCode();
        final ProductDetail productDetail = cache.getPaymentInformation().getProductDetail();
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();
        final MasterBillerResponse masterBiller = prepareData.getMasterBillerResponse();
        final boolean isEDonationTransaction = StringUtils.equals(CATEGORY_E_DONATION_ID, masterBiller.getBillerInfo().getBillerCategoryCode());

        String reference2AfterTransform = cache.getPaymentInformation().getProductDetail().getProductRef2();
        String taxIdFromCustomerKYC = null;
        String InstructionId = null;

        if (isEDonationTransaction) {
            boolean isThaiNation = StringUtils.equals(THAI_NATION_ID_TYPE, prepareData.getCustomerKYC().getIdType());
            if (request.getEDonation().isAllowShareToRdFlag() && isThaiNation) {
                reference2AfterTransform = prepareData.getCustomerKYC().getIdNo();
            }
        }

        if (isAmountExceedAmloThreshold) {
            taxIdFromCustomerKYC = prepareData.getCustomerKYC().getIdNo();

        }

        if (isShouldCallToETEISO20022) {
            InstructionId = generateInstructionId();
        }

        //todo inform P'Nu about this logic (Confirmed. will implement in next sprint)
//        boolean isTransactionFromQR = StringUtils.isNotBlank(promptPayRequest.getQr());
//        sender.setCustomerTypeFlag(isTransactionFromQR ? SENDER_TYPE_QR : SENDER_TYPE_KEY_IN);

        result.setSender(new Sender()
                .setAccountId(fromDepositAccount.getAccountNumber())
                .setAccountType(fromDepositAccount.getAccountType())
                .setBankName(TMB_BANK_SHORT_NAME)
                .setAccountName(fromDepositAccount.getAccountName())
                .setTaxId(taxIdFromCustomerKYC)
                .setCustomerTypeFlag(SENDER_TYPE_KEY_IN));

        result.setReceiver(new Receiver()
                .setId(compCode));

        result.setTerminal(new Terminal()
                .setId(this.generateTerminalId())
                .setType(TERMINAL_TYPE));

        result.setReference1(productDetail.getProductRef1().toUpperCase());
        result.setReference2(reference2AfterTransform);
        result.setReference3(productDetail.getProductRef3());
        result.setAmount(request.getAmount());
        result.setTransactionCreatedDatetime(ZonedDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
        result.setInstructionId(InstructionId);

        return result;
    }

    public PromptPayETEConfirmRequest toPromptPayETERequestForConfirmPromptPayPayment(PromptPayExternalValidateResponse externalValidateResponse) {
        final PromptPayETEValidateResponse eteResponse = externalValidateResponse.getEteResponse();
        final PromptPayETEValidateRequest eteRequest = externalValidateResponse.getEteRequest();

        PromptPayETEConfirmRequest confirmRequest = promptPayETEConfirmRequestMapper.mapToPromptPayETEConfirmRequest(eteResponse);
        confirmRequest.setReference2(eteRequest.getReference2());
        confirmRequest.setReference3(eteRequest.getReference3());
        confirmRequest.setInstructionId(eteRequest.getInstructionId());
        confirmRequest.setTransactionReference(Transaction.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, 8));

        return confirmRequest;
    }


    private String generateInstructionId() {
        Date now = new Date(System.currentTimeMillis());
        SimpleDateFormat df = new SimpleDateFormat(TRANS_ID_DATE_FORMAT);
        SimpleDateFormat jdf = new SimpleDateFormat("yDDDHH");
        return df.format(now)
                + getSixDigitsRandomNumber()
                + TTB_BANK_CODE_3DIGIT_ISO20022
                + StringUtils.right(jdf.format(now), 6)
                + getSixDigitsRandomNumber();
    }

    private int getSixDigitsRandomNumber() {
        int start = 100000;
        int end = 999999;
        return new SecureRandom().nextInt(end - start) + start;
    }

    private String generateTerminalId() {
        String sequenceKey = Transaction.getSequenceKey(PROMPTPAY_REF_SEQ, 6);
        return ALPHABET_I + sequenceKey + B011B + TMBO;
    }

}
