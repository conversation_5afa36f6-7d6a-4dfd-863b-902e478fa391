package com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation;

import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.BillPayValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.autoloan.AutoLoanValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ewallet.EWalletValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.loan.LoanValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.promptpay.PromptPayValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;

import java.util.List;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_COMMON_PAYMENT_VERIFY_STEP;
import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_LOG_COMMON_PAYMENT_VERIFY_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_PAYMENT_METHOD_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_PAYMENT_METHOD_DEPOSIT;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_MOBILE;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_MEA;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_MWA;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_WOW_POINT_FORMAT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.CATEGORY_E_DONATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TOP_UP_DEFAULT_FEE;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNullOrDefault;
import static com.tmb.oneapp.commonpaymentexp.utils.NumberUtils.insertCommas;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ActivityBillPayMapper {

    public static final ActivityBillPayMapper INSTANCE = new ActivityBillPayMapper();

    public ActivityBillPayValidationEvent toActivityBillPayValidation(HttpHeaders headers, ValidationCommonPaymentRequest request, CommonPaymentDraftCache cache) {
        final String commonPaymentVerifyActivityId = ACTIVITY_LOG_COMMON_PAYMENT_VERIFY_ID;
        final String compCode = cache.getPaymentInformation().getCompCode();
        final boolean isTransactionFromDeepLink = cache.getDeepLinkRequest() != null;

        var activityEvent = new ActivityBillPayValidationEvent(commonPaymentVerifyActivityId, headers);
        activityEvent.setStep(ACTIVITY_COMMON_PAYMENT_VERIFY_STEP);
        activityEvent.setFee(TOP_UP_DEFAULT_FEE);
        activityEvent.setReference1(cache.getPaymentInformation().getProductDetail().getProductRef1());
        activityEvent.setBillerNameWithCompCode(compCode);

        String overrideFlow = request.getFlow();
        if (isTransactionFromDeepLink) {
            overrideFlow = getSafeNullOrDefault(() -> cache.getDeepLinkRequest().getCallFrom(), "call-from-in-deep-link-is-null");
        }
        activityEvent.setFlow(overrideFlow);


        if (Boolean.TRUE.equals(request.getCreditCard().isPayWithCreditCardFlag())) {
            activityEvent.setFromAccount(request.getCreditCard().getAccountId());
            activityEvent.setAmount(insertCommas(request.getCreditCard().getAmount()));
            activityEvent.setPaymentMethod(ACTIVITY_PAYMENT_METHOD_CREDIT_CARD);
        } else {
            activityEvent.setFromAccount(request.getDeposit().getAccountNumber());
            activityEvent.setAmount(insertCommas(request.getDeposit().getAmount()));
            activityEvent.setPaymentMethod(ACTIVITY_PAYMENT_METHOD_DEPOSIT);
        }

        boolean isNotMEAOrMWA = !StringUtils.equalsAny(compCode, BILL_COMP_CODE_MEA, BILL_COMP_CODE_MWA);
        if (isNotMEAOrMWA) {
            activityEvent.setReference2(cache.getPaymentInformation().getProductDetail().getProductRef2());
        }

        activityEvent.setWowPoint(WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule()) ? String.format(ACTIVITY_WOW_POINT_FORMAT,
                request.getWowPoint().getDiscountAmount(),
                request.getWowPoint().getWowPointAmount())
                : "-");
        activityEvent.setCouponDiscount("-");
        activityEvent.setCreditCardPoint("-");
        activityEvent.setPinFree("-");
        activityEvent.setIsForceFr("-");
        activityEvent.setIsForceDipchip("-");

        return activityEvent;
    }

    public ActivityBillPayValidationEvent updateActivityBillPayValidation(ActivityBillPayValidationEvent activityEvent, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, ValidationCommonPaymentRequest request, CreditCardSupplementary creditCardDetail, BillPayValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CommonAuthenResult commonAuthenResult = Optional.ofNullable(validateDataAfterCallExternal.getCommonAuthenResult()).orElse(new CommonAuthenResult());
        boolean isPayWithCreditCardFlag = Optional.ofNullable(request.getCreditCard())
                .map(CreditCardValidationCommonPaymentRequest::isPayWithCreditCardFlag)
                .orElse(false);

        activityEvent.setTotalAmount(insertCommas(validateDataAfterCallExternal.getTotalAmount()));
        activityEvent.setFee(insertCommas(validateDataAfterCallExternal.getFeeAfterCalculated()));
        activityEvent.setBillerNameWithCompCode(compCode, masterBillerResponse.getBillerInfo().getNameEn());
        activityEvent.setPinFree(String.valueOf(commonAuthenResult.isPinFree()));
        activityEvent.setIsForceFr(commonAuthenResult.getIsForceFr() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceFr()));
        activityEvent.setIsForceDipchip(commonAuthenResult.getIsForceDipChip() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceDipChip()));
        if (this.isShouldMaskingRef1(masterBillerResponse)) {
            activityEvent.maskingReference1();
        }

        if (isPayWithCreditCardFlag) {
            String cardIdWithMasking = creditCardDetail.getCardNo();
            activityEvent.setCardNumber(cardIdWithMasking);
        }
        return activityEvent;
    }

    public ActivityBillPayValidationEvent updateActivityEWalletValidation(ActivityBillPayValidationEvent activityEvent, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, ValidationCommonPaymentRequest request, EWalletValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CommonAuthenResult commonAuthenResult = Optional.ofNullable(validateDataAfterCallExternal.getCommonAuthenResult()).orElse(new CommonAuthenResult());

        activityEvent.setTotalAmount(insertCommas(validateDataAfterCallExternal.getTotalAmount()));
        activityEvent.setFee(insertCommas(validateDataAfterCallExternal.getFeeAfterCalculated()));
        activityEvent.setBillerNameWithCompCode(compCode, masterBillerResponse.getBillerInfo().getNameEn());
        activityEvent.setPinFree(String.valueOf(commonAuthenResult.isPinFree()));
        activityEvent.setIsForceFr(commonAuthenResult.getIsForceFr() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceFr()));
        activityEvent.setIsForceDipchip(commonAuthenResult.getIsForceDipChip() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceDipChip()));
        if (this.isShouldMaskingRef1(masterBillerResponse)) {
            activityEvent.maskingReference1();
        }
        return activityEvent;
    }

    public ActivityBillPayValidationEvent updateActivityAutoLoanBillPayValidation(ActivityBillPayValidationEvent activityEvent, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, ValidationCommonPaymentRequest request, AutoLoanValidateDataAfterCallExternal autoLoanValidateDataAfterCallExternal) {
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CommonAuthenResult commonAuthenResult = Optional.ofNullable(autoLoanValidateDataAfterCallExternal.getCommonAuthenResult()).orElse(new CommonAuthenResult());

        activityEvent.setTotalAmount(insertCommas(autoLoanValidateDataAfterCallExternal.getTotalAmount()));
        activityEvent.setFee(insertCommas(autoLoanValidateDataAfterCallExternal.getFeeAfterCalculated()));
        activityEvent.setBillerNameWithCompCode(compCode, masterBillerResponse.getBillerInfo().getNameEn());
        activityEvent.setPinFree(String.valueOf(commonAuthenResult.isPinFree()));
        activityEvent.setIsForceFr(Optional.ofNullable(commonAuthenResult.getIsForceFr()).map(String::valueOf).orElse("-"));
        activityEvent.setIsForceDipchip(Optional.ofNullable(commonAuthenResult.getIsForceDipChip()).map(String::valueOf).orElse("-"));
        if (this.isShouldMaskingRef1(masterBillerResponse)) {
            activityEvent.maskingReference1();
        }

        return activityEvent;
    }

    public ActivityBillPayValidationEvent updateActivityLoanValidation(ActivityBillPayValidationEvent activityEvent, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, LoanValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CommonAuthenResult commonAuthenResult = Optional.ofNullable(validateDataAfterCallExternal.getCommonAuthenResult()).orElse(new CommonAuthenResult());

        activityEvent.setTotalAmount(insertCommas(validateDataAfterCallExternal.getTotalAmount()));
        activityEvent.setFee(insertCommas(validateDataAfterCallExternal.getFeeAfterCalculated()));
        activityEvent.setBillerNameWithCompCode(compCode, masterBillerResponse.getBillerInfo().getNameEn());
        activityEvent.setPinFree(String.valueOf(commonAuthenResult.isPinFree()));
        activityEvent.setIsForceFr(commonAuthenResult.getIsForceFr() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceFr()));
        activityEvent.setIsForceDipchip(commonAuthenResult.getIsForceDipChip() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceDipChip()));
        if (this.isShouldMaskingRef1(masterBillerResponse)) {
            activityEvent.maskingReference1();
        }

        return activityEvent;
    }


    public ActivityBillPayValidationEvent updateActivityCreditCardBillPayValidation(ActivityBillPayValidationEvent activityEvent, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, ValidationCommonPaymentRequest request, CreditCardSupplementary creditCardDetail, CreditCardValidateDataAfterCallExternal creditCardValidateDataAfterCallExternal) {
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CommonAuthenResult commonAuthenResult = Optional.ofNullable(creditCardValidateDataAfterCallExternal.getCommonAuthenResult()).orElse(new CommonAuthenResult());
        boolean isPayWithCreditCardFlag = Optional.ofNullable(request.getCreditCard())
                .map(CreditCardValidationCommonPaymentRequest::isPayWithCreditCardFlag)
                .orElse(false);

        activityEvent.setTotalAmount(insertCommas(creditCardValidateDataAfterCallExternal.getTotalAmount()));
        activityEvent.setFee(insertCommas(creditCardValidateDataAfterCallExternal.getFeeAfterCalculated()));
        activityEvent.setBillerNameWithCompCode(compCode, masterBillerResponse.getBillerInfo().getNameEn());
        activityEvent.setPinFree(String.valueOf(commonAuthenResult.isPinFree()));
        activityEvent.setIsForceFr(commonAuthenResult.getIsForceFr() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceFr()));
        activityEvent.setIsForceDipchip(commonAuthenResult.getIsForceDipChip() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceDipChip()));
        if (this.isShouldMaskingRef1(masterBillerResponse)) {
            activityEvent.maskingReference1();
        }

        if (isPayWithCreditCardFlag) {
            String cardIdWithMasking = creditCardDetail.getCardNo();
            activityEvent.setCardNumber(cardIdWithMasking);
        }
        return activityEvent;
    }

    protected boolean isShouldMaskingRef1(MasterBillerResponse masterBillerResponse) {
        String billerCategoryCode = masterBillerResponse.getBillerInfo().getBillerCategoryCode();
        Boolean isMobile = masterBillerResponse.getRef1().getIsMobile();
        return List.of(BILLER_CATEGORY_CODE_CREDIT_CARD, BILLER_CATEGORY_CODE_MOBILE).contains(billerCategoryCode) ||
                Boolean.TRUE.equals(isMobile);
    }

    public ActivityBillPayValidationEvent updateActivityBillPayValidationFailed(ActivityBillPayValidationEvent activityEvent, MasterBillerResponse masterBiller, CommonPaymentDraftCache cache, ValidationCommonPaymentRequest request, CreditCardSupplementary creditCardDetail, Exception e) {
        final String compCode = cache.getPaymentInformation().getCompCode();
        boolean isPayWithCreditCardFlag = Optional.ofNullable(request.getCreditCard()).map(CreditCardValidationCommonPaymentRequest::isPayWithCreditCardFlag).orElse(false);

        boolean isSuccessToGetMasterBiller = masterBiller != null;
        if (isSuccessToGetMasterBiller) {
            activityEvent.setBillerNameWithCompCode(compCode, masterBiller.getBillerInfo().getNameEn());
            if (this.isShouldMaskingRef1(masterBiller)) {
                activityEvent.maskingReference1();
            }
        }

        boolean isSuccessToGetCreditCardDetail = creditCardDetail != null;
        if (isPayWithCreditCardFlag && isSuccessToGetCreditCardDetail) {
            String cardIdWithMasking = creditCardDetail.getCardNo();
            activityEvent.setCardNumber(cardIdWithMasking);
        }

        activityEvent.setFailureStatusWithReasonFromException(e);

        return activityEvent;
    }

    public ActivityBillPayValidationEvent updateActivityBillPayValidationFailed(ActivityBillPayValidationEvent activityEvent, MasterBillerResponse masterBiller, CommonPaymentDraftCache cache, ValidationCommonPaymentRequest request, Exception e) {
        final String compCode = cache.getPaymentInformation().getCompCode();

        boolean isSuccessToGetMasterBiller = masterBiller != null;
        if (isSuccessToGetMasterBiller) {
            activityEvent.setBillerNameWithCompCode(compCode, masterBiller.getBillerInfo().getNameEn());
            if (this.isShouldMaskingRef1(masterBiller)) {
                activityEvent.maskingReference1();
            }
        }

        activityEvent.setFailureStatusWithReasonFromException(e);

        return activityEvent;
    }

    public ActivityBillPayValidationEvent updateActivityLoanValidationFailed(ActivityBillPayValidationEvent activityEvent, MasterBillerResponse masterBiller, CommonPaymentDraftCache cache, Exception e) {
        final String compCode = cache.getPaymentInformation().getCompCode();

        boolean isSuccessToGetMasterBiller = masterBiller != null;
        if (isSuccessToGetMasterBiller) {
            activityEvent.setBillerNameWithCompCode(compCode, masterBiller.getBillerInfo().getNameEn());
            if (this.isShouldMaskingRef1(masterBiller)) {
                activityEvent.maskingReference1();
            }
        }

        activityEvent.setFailureStatusWithReasonFromException(e);

        return activityEvent;
    }

    public ActivityBillPayValidationEvent updateActivityPromptPayValidation(ActivityBillPayValidationEvent activityEvent, ValidationCommonPaymentRequest request, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, PromptPayValidateDataAfterCallExternal validateDataAfterCallExternal, PromptPayETEValidateResponse externalResponse) {
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CommonAuthenResult commonAuthenResult = Optional.ofNullable(validateDataAfterCallExternal.getCommonAuthenResult()).orElse(new CommonAuthenResult());
        final boolean isAllowShareToRd = getSafeNullOrDefault(() -> request.getEDonation().isAllowShareToRdFlag(), false);
        final boolean isShouldMaskingRef2 = CATEGORY_E_DONATION_ID.equals(masterBillerResponse.getBillerInfo().getBillerCategoryCode()) && Boolean.TRUE.equals(isAllowShareToRd);
        final String reference2FromExternalResponse = externalResponse.getReference2();

        activityEvent.setFee(insertCommas(validateDataAfterCallExternal.getFeeAfterCalculated()));
        activityEvent.setTotalAmount(insertCommas(validateDataAfterCallExternal.getTotalAmount()));
        activityEvent.setPinFree(String.valueOf(commonAuthenResult.isPinFree()));
        activityEvent.setBillerNameWithCompCode(compCode, masterBillerResponse.getBillerInfo().getNameEn());
        activityEvent.setIsForceFr(commonAuthenResult.getIsForceFr() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceFr()));
        activityEvent.setIsForceDipchip(commonAuthenResult.getIsForceDipChip() == null ? "-" : String.valueOf(commonAuthenResult.getIsForceDipChip()));

        if (this.isShouldMaskingRef1(masterBillerResponse)) {
            activityEvent.maskingReference1();
        }

        activityEvent.setReference2(reference2FromExternalResponse);
        if (isShouldMaskingRef2) {
            activityEvent.maskingReference2();
        }

        return activityEvent;
    }

    public ActivityBillPayValidationEvent updateActivityPromptPayValidationFailed(ActivityBillPayValidationEvent activityEvent, MasterBillerResponse masterBiller, CommonPaymentDraftCache cache, ValidationCommonPaymentRequest request, Exception e) {
        final String compCode = cache.getPaymentInformation().getCompCode();

        boolean isSuccessToGetMasterBiller = masterBiller != null;
        if (isSuccessToGetMasterBiller) {
            boolean isAllowShareToRdFlag = NullSafeUtils.getSafeNullOrDefault(() -> request.getEDonation().isAllowShareToRdFlag(), false);
            final boolean isShouldMaskingRef2 = CATEGORY_E_DONATION_ID.equals(masterBiller.getBillerInfo().getBillerCategoryCode()) && isAllowShareToRdFlag;
            activityEvent.setBillerNameWithCompCode(compCode, masterBiller.getBillerInfo().getNameEn());
            if (this.isShouldMaskingRef1(masterBiller)) {
                activityEvent.maskingReference1();
            }

            if (isShouldMaskingRef2) {
                activityEvent.maskingReference2();
            }
        }

        activityEvent.setFailureStatusWithReasonFromException(e);

        return activityEvent;
    }
}
