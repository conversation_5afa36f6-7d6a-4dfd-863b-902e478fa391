package com.tmb.oneapp.commonpaymentexp.model.commonpayment;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ProductDetail {

    private String productNameEn;
    private String productNameTh;
    @Pattern(regexp = "^$|^[a-zA-Z\\d\\s._\\/]+$", message = "Reference 1 contains invalid characters")
    @JsonProperty("product_ref_1")
    private String productRef1;
    @Pattern(regexp = "^$|^[a-zA-Z\\d\\s._\\/]+$", message = "Reference 2 contains invalid characters")
    @JsonProperty("product_ref_2")
    private String productRef2;
    @Pattern(regexp = "^$|^[a-zA-Z\\d\\s._\\/]+$", message = "Reference 3 contains invalid characters")
    @JsonProperty("product_ref_3")
    private String productRef3;
    @Pattern(regexp = "^$|^[a-zA-Z\\d\\s._\\/]+$", message = "Reference 4 contains invalid characters")
    @JsonProperty("product_ref_4")
    private String productRef4;
    @Pattern(regexp = "^$|^[a-zA-Z\\d\\s._\\/]+$", message = "Reference 5 contains invalid characters")
    @JsonProperty("product_ref_5")
    private String productRef5;
    @NotEmpty
    @Valid
    private List<PhraseDetail> productAttributeList;

}
