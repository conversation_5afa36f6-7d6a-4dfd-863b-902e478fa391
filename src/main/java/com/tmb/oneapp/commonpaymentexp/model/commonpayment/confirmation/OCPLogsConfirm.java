package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityBillPayOCPConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialOCPBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityOCPBillPay;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class OCPLogsConfirm implements LogsForConfirm {
    private ActivityBillPayOCPConfirmationEvent activityBillPayOCPConfirmationEvent;
    private FinancialOCPBillPayActivityLog financialOCPBillPayActivityLog;
    private TransactionActivityOCPBillPay transactionActivityOCPBillPay;
    private ActivityCustomSlipCompleteEvent activityCustomSlipCompleteEvent;
}
