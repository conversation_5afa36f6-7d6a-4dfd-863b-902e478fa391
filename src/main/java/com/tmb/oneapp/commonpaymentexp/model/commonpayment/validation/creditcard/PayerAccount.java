package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AccountBalance;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PayerAccount {
    String id;
    String type;
    String title;
    AccountBalance balances;
}
