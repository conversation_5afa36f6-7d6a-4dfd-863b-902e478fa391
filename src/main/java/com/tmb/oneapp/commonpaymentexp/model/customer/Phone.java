package com.tmb.oneapp.commonpaymentexp.model.customer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class Phone {

    @JsonProperty("ident_phone")
    public String identPhone;
    @JsonProperty("phone_seq")
    public Integer phoneSeq;
    @JsonProperty("phone_type")
    public String phoneType;
    @JsonProperty("phone_prefix")
    public String phonePrefix;
    @JsonProperty("phone_no")
    public String phoneNo;
    @JsonProperty("phone_no_full")
    public String phoneNoFull;
    @JsonProperty("phone_no_ext")
    public String phoneNoExt;
    @JsonProperty("create_date")
    public String createDate;
    @JsonProperty("create_by")
    public String createBy;
    @JsonProperty("update_date")
    public String updateDate;
    @JsonProperty("update_by")
    public String updateBy;

}