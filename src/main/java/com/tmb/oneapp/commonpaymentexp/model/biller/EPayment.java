package com.tmb.oneapp.commonpaymentexp.model.biller;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EPayment {
    private String encryptedMerchantKey;
    private String fgurl;
    private String bgurl;
    private String billerUrl;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal fee;
}
