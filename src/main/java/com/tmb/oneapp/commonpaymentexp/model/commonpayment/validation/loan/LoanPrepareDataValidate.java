package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.loan;

import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.LoanAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.PrepareDataForValidate;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.loanaccount.HomeLoanFullInfoResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class LoanPrepareDataValidate implements PrepareDataForValidate {
    private BillPayConfiguration billPayConfiguration;
    private MasterBillerResponse masterBillerResponse;
    private DepositAccount fromDepositAccount;
    private CustomerCrmProfile customerCrmProfile;
    private List<LoanAccount> loanAccountList;
    private HomeLoanFullInfoResponse homeLoanForValidateDebt;
}
