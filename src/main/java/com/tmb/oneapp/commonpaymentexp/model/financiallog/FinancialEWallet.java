package com.tmb.oneapp.commonpaymentexp.model.financiallog;

import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETERequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_EWALLET;


public class FinancialEWallet extends BaseFinancialActivityLog {

    public FinancialEWallet(String crmId, String correlationId, String transactionDateTime, CommonPaymentDraftCache cache) {
        super(crmId, cache.getValidateDraftCache().getExternalConfirmRequest().getEWalletConfirmRequest().getTransactionReference(), correlationId, transactionDateTime, cache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo());

        setUpFields(cache);
    }

    private void setUpFields(CommonPaymentDraftCache cache) {
        final EWalletETERequest eWalletConfirmRequest = cache.getValidateDraftCache().getExternalConfirmRequest().getEWalletConfirmRequest();
        final ValidationCommonPaymentRequest validateRequest = cache.getValidateRequest();
        final DepositAccountInCache fromDepositAccount = cache.getValidateDraftCache().getFromDepositAccount();
        final MasterBillerResponseInCache masterBillerResponse = cache.getValidateDraftCache().getMasterBillerResponse();

        String overrideToAccName = eWalletConfirmRequest.getReceiver().getAccountDisplayName();
        super.setToAccName(overrideToAccName);

        super.setActivityId(ACTIVITY_FIN_AND_TRANS_TOP_UP_ID);
        super.setCategoryId(BILLER_CATEGORY_EWALLET);
        super.setMemo(validateRequest.getNote());
        super.setTxnAmount(String.valueOf(eWalletConfirmRequest.getAmount()));
        super.setTxnFee(eWalletConfirmRequest.getFee().toString());
        super.setReferenceID(eWalletConfirmRequest.getTransactionReference());
        super.setCompCode(masterBillerResponse.getBillerInfo().getBillerCompCode());
        super.setBillerRef1(eWalletConfirmRequest.getReceiver().getProxyValue());
        super.setProxyId(eWalletConfirmRequest.getReceiver().getProxyType());
        super.setProxyValue(eWalletConfirmRequest.getReceiver().getProxyValue());
        super.setBankCode(eWalletConfirmRequest.getReceiver().getBankCode());
        super.setFromAccNo(eWalletConfirmRequest.getSender().getAccountId());
        super.setFromAccType(eWalletConfirmRequest.getSender().getAccountType());
        super.setFromAccName(eWalletConfirmRequest.getSender().getAccountName());
        super.setFromAccNickName(fromDepositAccount.getProductNickname());
        super.setToAccNo(shortenAccountId(eWalletConfirmRequest.getReceiver().getAccountId()));
        super.setFinLinkageId(eWalletConfirmRequest.getTerminal().getId());
    }
}
