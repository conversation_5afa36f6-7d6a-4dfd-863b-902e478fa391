package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CompleteScreenDetail;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ConfirmationCommonPaymentResponse {
    private String transactionCreatedDatetime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "#0.00")
    private BigDecimal remainingBalance;

    private String referenceNo;
    private String qr;
    @JsonProperty("is_auto_save_slip")
    private boolean isAutoSaveSlip;
    private CompleteScreenDetail completeScreenDetail;

    private EasyPassConfirmationCommonPaymentResponse easyPass;
}
