package com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.BaseActivityEvent;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.CustomSlip;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.http.HttpHeaders;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_LOG_CUSTOM_SLIP_COMPLETE_BACKGROUND_ACTIVITY_ID;

/**
 * Represents the activity log for a completed custom slip transaction.
 */
@Getter
@Setter
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ActivityCustomSlipCompleteEvent extends BaseActivityEvent {
    private String txnType;
    private String slipId;
    private String slipCategory;
    private String slipDefaultFlag;

    public ActivityCustomSlipCompleteEvent(HttpHeaders headers, CustomSlip customSlip, String txnType) {
        super(headers);
        super.setActivityTypeId(ACTIVITY_LOG_CUSTOM_SLIP_COMPLETE_BACKGROUND_ACTIVITY_ID);
        this.txnType = txnType;

        if (customSlip != null) {
            this.slipId = customSlip.getSlipId();
            this.slipCategory = customSlip.getSlipCategory();
            this.slipDefaultFlag = Boolean.TRUE.equals(customSlip.getSlipDefaultFlag()) ? "true" : "false";
        }
    }
}
