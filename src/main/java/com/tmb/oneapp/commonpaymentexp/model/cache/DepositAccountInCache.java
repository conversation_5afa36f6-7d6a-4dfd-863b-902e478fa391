package com.tmb.oneapp.commonpaymentexp.model.cache;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class DepositAccountInCache {
    private String productNickname;
    private String productNameTh;
    private String productNameEn;
    private String accountNumber;
    private String accountName;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal availableBalance;
    private String waiveFeeForBillpay;
    private String accountType;
}

