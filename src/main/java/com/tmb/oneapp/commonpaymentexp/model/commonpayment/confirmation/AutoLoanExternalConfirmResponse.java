package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanOCPResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEResponse;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class AutoLoanExternalConfirmResponse implements ExternalConfirmResponse {
    private AutoLoanOCPResponse autoLoanOCPResponse;
    private TopUpETEResponse topUpETEResponse;
}

