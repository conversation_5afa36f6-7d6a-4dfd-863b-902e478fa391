package com.tmb.oneapp.commonpaymentexp.model.transactionlog;

import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;

public class TransactionActivityBillPromptPay extends TransactionActivityBillPay {
    public TransactionActivityBillPromptPay(String refId, String crmId, CommonPaymentDraftCache commonPaymentDraftCache, String transactionDateTime) {
        super(refId, crmId, commonPaymentDraftCache, transactionDateTime);

        final PromptPayETEConfirmRequest promptPayConfirmRequest = commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getPromptPayConfirmRequest();
        final ValidationCommonPaymentRequest validateRequest = commonPaymentDraftCache.getValidateRequest();
        final DepositAccountInCache fromDepositAccount = commonPaymentDraftCache.getValidateDraftCache().getFromDepositAccount();
        final MasterBillerResponseInCache masterBiller = commonPaymentDraftCache.getValidateDraftCache().getMasterBillerResponse();

        String productNickname = fromDepositAccount.getProductNickname();

        super.setFromAccountNickname(productNickname);
        super.setFinancialTransferMemo(validateRequest.getNote());
        super.setFromAccountNo(super.shortenAccountId(promptPayConfirmRequest.getSender().getAccountId()));
        super.setToAccountNo(super.shortenAccountId(promptPayConfirmRequest.getReceiver().getAccountId()));
        super.setFinancialTransferAmount(promptPayConfirmRequest.getAmount().toString());
        super.setBillerNameEn(promptPayConfirmRequest.getReceiver().getAccountDisplayName());
        super.setBillerNameTh(promptPayConfirmRequest.getReceiver().getAccountDisplayName());

        super.setBillerRef1(promptPayConfirmRequest.getReference1(), masterBiller.getBillerInfo().getBillerCategoryCode());

        if (masterBiller.getRef2() != null) {
            super.setBillerRef2(promptPayConfirmRequest.getReference2());
        }
    }
}
