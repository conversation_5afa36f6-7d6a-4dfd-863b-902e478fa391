package com.tmb.oneapp.commonpaymentexp.model.customer;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AccumulateUsageRequest {
    private BigDecimal billPayAccumulateUsageAmount;
    private BigDecimal paymentAccumulateUsageAmount;
    private BigDecimal dailyAccumulateUsageAmount;
}
