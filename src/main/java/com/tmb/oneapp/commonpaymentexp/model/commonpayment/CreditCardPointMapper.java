package com.tmb.oneapp.commonpaymentexp.model.commonpayment;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.CreditCardPoint;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import org.mapstruct.Mapper;
import java.util.List;

@Mapper(componentModel = "spring")
public interface CreditCardPointMapper {

    CreditCardPoint mapToCreditCardPoint(CreditCardSupplementary creditCardSupplementary);

    List<CreditCardPoint> mapToCreditCardPointList(List<CreditCardSupplementary> creditCardSupplementaryList);
}
