package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;

@JsonPropertyOrder({
        "debt_FeeCode",
        "debt_FeeDesc",
        "debt_FeeAmount"
})
@Getter
@Setter
public class AutoLoanHpPaymentDetails {

    @JsonProperty("debt_FeeCode")
    private String debtFeeCode;
    @JsonProperty("debt_FeeDesc")
    private String debtFeeDesc;
    @JsonProperty("debt_FeeAmount")
    private Double debtFeeAmount;

}
