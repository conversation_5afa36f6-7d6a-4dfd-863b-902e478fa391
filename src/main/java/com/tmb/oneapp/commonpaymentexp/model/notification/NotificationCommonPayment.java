package com.tmb.oneapp.commonpaymentexp.model.notification;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;


@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@Accessors(chain = true)
public class NotificationCommonPayment extends NotificationPayment {
    private String methodTH;
    private String methodEN;
    private String cardId;
    private String productNameTH;
    private String productNameEN;
    private String compcode;
    private String reference1TH;
    private String reference1EN;
    private String reference2TH;
    private String reference2EN;
    private String couponDiscount;
    private String couponCode;
    private String wowDiscount;
    private String wowPoint;
    private String creditCardDiscount;
    private String creditCardPoint;
    private String netAmount;

    public NotificationCommonPayment(String templateName, String crmId, String correlationId) {
        super(templateName, crmId, correlationId);
    }
}
