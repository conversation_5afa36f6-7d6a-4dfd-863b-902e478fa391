package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import java.util.Arrays;

public enum BillerGroupTypeEnum {
    TOPUP(1, "topup"),
    BILL_PAY(0, "bill_pay");

    private final int value;
    private final String description;

    BillerGroupTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static BillerGroupTypeEnum fromValue(int value) {
        return Arrays.stream(values())
                .filter(billerGroupTypeEnum -> billerGroupTypeEnum.value == value)
                .findFirst()
                .orElse(BILL_PAY);
    }

    public static BillerGroupTypeEnum fromDescription(String description) {
        return Arrays.stream(values())
                .filter(billerGroupTypeEnum -> billerGroupTypeEnum.description.equalsIgnoreCase(description))
                .findFirst()
                .orElse(BILL_PAY);
    }
}