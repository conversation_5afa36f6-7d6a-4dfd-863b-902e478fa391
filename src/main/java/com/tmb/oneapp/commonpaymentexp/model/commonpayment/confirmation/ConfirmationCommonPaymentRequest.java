package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ConfirmationCommonPaymentRequest {
    @NotBlank(message = "transaction_id should not be blank.")
    private String transactionId;
    private CustomSlip customSlip;
}
