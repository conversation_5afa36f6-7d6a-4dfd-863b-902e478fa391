package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Accessors(chain = true)
//TODO change field name
public class TopUpETETransaction {
    private String bankReferenceId;
    private String paymentId;
    private String paymentDateTime;
    private String paymentDueDate;
    private String dueDate;
    @JsonProperty("reference_1")
    private String reference1;

    @JsonProperty("reference_2")
    private String reference2;

    @JsonProperty("reference_3")
    private String reference3;

    @JsonProperty("reference_4")
    private String reference4;
    private BigDecimal amount;
    private String currency;
    private TopUpBalance balance;
    private Object customer;
    private TopUpAccount fromAccount;
    private TopUpAccount toAccount;
    private TopUpFee fee;
    private List<AdditionalParam> additionalParams;
    private ConfirmAccount account;
    private BigDecimal installmentAmount;
    private AutoLoanTBankCustomer tbankCustomer;
}
