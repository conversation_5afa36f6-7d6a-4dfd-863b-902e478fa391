package com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.AccountSaving;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentRule;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerDetailResponse;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonPaymentMethodPrepareDataTemp {
    private CommonPaymentRule commonPaymentRule;
    private MasterBillerResponse masterBillerResponse;
    private AccountSaving accountSaving;
    private List<CustomerDetailResponse> customerDetailResponseList;
}
