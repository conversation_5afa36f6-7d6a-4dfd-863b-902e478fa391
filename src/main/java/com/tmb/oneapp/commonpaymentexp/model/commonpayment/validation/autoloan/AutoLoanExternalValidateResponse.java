package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.autoloan;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ExternalValidateResponse;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class AutoLoanExternalValidateResponse implements ExternalValidateResponse {
    private TopUpETEPaymentRequest topUpETEPaymentRequest;
    private TopUpETEResponse topUpETEResponse;
}
