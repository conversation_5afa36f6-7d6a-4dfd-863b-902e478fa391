package com.tmb.oneapp.commonpaymentexp.model.financiallog;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;

import java.util.Map;
import java.util.stream.Collectors;


public class FinancialMEABillPayActivityLog extends FinancialCustomBillPayActivityLog {
    private static final String PARAM_TOTAL_INTEREST = "TotalInterest";
    private static final String PARAM_DISCONNECTED_AMOUNT = "DisconnectedAmount";
    private static final String PARAM_CUSTOMER_NAME = "CustName";
    private static final String PARAM_CUSTOMER_ADDRESS = "CustAddress";
    private static final String PARAM_UI = "UI";
    private static final String AMOUNT_INFO_DELIMITER = "|";
    private static final String DEFAULT_EMPTY_VALUE = "";
    private static final String DEFAULT_AMOUNT = "0.00";

    public FinancialMEABillPayActivityLog(String crmId, String refId, CommonPaymentDraftCache commonPaymentDraftCache, String correlationId, String transactionDateTime) {
        super(crmId, refId, commonPaymentDraftCache, correlationId, transactionDateTime);
    }

    @Override
    protected void setFinFlexValues(CommonPaymentDraftCache commonPaymentDraftCache) {
        Map<String, String> params = commonPaymentDraftCache.getValidateDraftCache()
                .getExternalConfirmRequest()
                .getOcpBillPaymentConfirmRequest()
                .getAdditionalParams()
                .stream()
                .collect(Collectors.toMap(
                        AdditionalParam::getName,
                        param -> String.valueOf(param.getValue()),
                        (existing, replacement) -> existing
                ));

        String amountInfo = String.join(AMOUNT_INFO_DELIMITER,
                commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getAmount(),
                params.getOrDefault(PARAM_TOTAL_INTEREST, DEFAULT_AMOUNT),
                params.getOrDefault(PARAM_DISCONNECTED_AMOUNT, DEFAULT_AMOUNT)
        );

        setFinFlexValues1(params.getOrDefault(PARAM_CUSTOMER_NAME, DEFAULT_EMPTY_VALUE));
        setFinFlexValues2(params.getOrDefault(PARAM_CUSTOMER_ADDRESS, DEFAULT_EMPTY_VALUE));
        setFinFlexValues3(params.getOrDefault(PARAM_UI, DEFAULT_EMPTY_VALUE));
        setFinFlexValues4(amountInfo);
    }
}
