package com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import jakarta.validation.Valid;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Optional;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InitializationCommonPaymentRequest {
    @NotNull(message = "Payment Information is required")
    @Valid
    private PaymentInformation paymentInformation;

    /**
     * This method will throw when amount value is negative and schedule is null
     * <pre>
     *  amountDetail.amountValue = 0.00   && schedule = null  ==>  Throw exception
     *  amountDetail.amountValue = -1     && schedule = null  ==>  Throw exception (@PositiveOrZero)
     *  amountDetail.amountValue = 0.01   && schedule = null  ==>  Not Throw
     *  amountDetail.amountValue = 0.00   && schedule != null ==>  Not Throw
     *  amountDetail.amountValue = -1     && schedule != null ==>  Throw exception (@PositiveOrZero)
     *  amountDetail.amountValue = 0.01   && schedule != null ==>  Not Throw
     * </pre>
     */
    @AssertTrue(message = "AmountValue must be positive")
    private boolean isAmountValueRequirePositive() {

        if (Optional.ofNullable(this.paymentInformation).map(PaymentInformation::getSchedule).isEmpty()) {
            return isAmountValuePositive();
        }
        return true;
    }

    private boolean isAmountValuePositive() {
        BigDecimal amountValue = Optional.ofNullable(this.paymentInformation).map(PaymentInformation::getAmountDetail).map(AmountDetail::getAmountValue).orElse(BigDecimal.ZERO);
        return amountValue.compareTo(BigDecimal.ZERO) >= 1;
    }
}
