package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateResponse;
import org.mapstruct.Mapper;
@Mapper(componentModel = "spring")
public interface PromptPayETEConfirmRequestMapper {


    /**
     * Maps PromptPayETEValidateResponse to PromptPayETEConfirmRequest.
     * @param eteResponse PromptPayETEValidateResponse to be mapped.
     * @return PromptPayETEConfirmRequest object.
     */
    PromptPayETEConfirmRequest mapToPromptPayETEConfirmRequest(PromptPayETEValidateResponse eteResponse);
}
