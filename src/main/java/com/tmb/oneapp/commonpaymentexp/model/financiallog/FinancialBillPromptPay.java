package com.tmb.oneapp.commonpaymentexp.model.financiallog;

import com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import org.apache.commons.lang3.StringUtils;


public class FinancialBillPromptPay extends BaseFinancialActivityLog {

    public FinancialBillPromptPay(String crmId, String correlationId, String transactionDateTime, CommonPaymentDraftCache cache, String activityLogActivityTypeId) {
        super(
                crmId,
                cache.getValidateDraftCache().getExternalConfirmRequest().getPromptPayConfirmRequest().getTransactionReference(),
                correlationId,
                transactionDateTime,
                cache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo()
        );
        super.setActivityTypeIdNew(activityLogActivityTypeId);

        setUpFields(cache);
    }

    private void setUpFields(CommonPaymentDraftCache cache) {
        final PromptPayETEConfirmRequest promptPayConfirmRequest = cache.getValidateDraftCache().getExternalConfirmRequest().getPromptPayConfirmRequest();
        final ValidationCommonPaymentRequest validateRequest = cache.getValidateRequest();
        final DepositAccountInCache fromDepositAccount = cache.getValidateDraftCache().getFromDepositAccount();
        final MasterBillerResponseInCache masterBillerResponse = cache.getValidateDraftCache().getMasterBillerResponse();
        final String fromAccountName = cache.getValidateDraftCache().getFromDepositAccount().getAccountName();

        String toBankAccCd = promptPayConfirmRequest.getReceiver().getBankCode();
        String overrideToBackAcctCd = StringUtils.isNotBlank(toBankAccCd) ? toBankAccCd : PromptPayETEConstant.TTB_BANK_CODE;

        super.setBankCode(overrideToBackAcctCd);
        super.setMemo(validateRequest.getNote());
        super.setFromAccNickName(fromDepositAccount.getProductNickname());
        super.setCompCode(masterBillerResponse.getBillerInfo().getBillerCompCode());
        super.setFromAccName(fromAccountName);

        super.setFromAccNo(promptPayConfirmRequest.getSender().getAccountId());
        super.setFromAccType(promptPayConfirmRequest.getSender().getAccountType());
        super.setToAccName(promptPayConfirmRequest.getReceiver().getAccountDisplayName());
        super.setToAccNo(shortenAccountId(promptPayConfirmRequest.getReceiver().getAccountId()));
        super.setTxnAmount(String.valueOf(promptPayConfirmRequest.getAmount()));
        super.setTxnFee(String.valueOf(promptPayConfirmRequest.getFee()));
        super.setBillerRef1(promptPayConfirmRequest.getReference1());
        super.setBillerRef2(promptPayConfirmRequest.getReference2());

        //todo wait to implement later when FE design to collect field transaction from QR scan
//        if (StringUtils.equals(tPromptPayVerifyETEResponse.getPaymentCacheData().getQr(), PAYMENT_QR_PROMPT_PAY)
//                || StringUtils.equals(tPromptPayVerifyETEResponse.getPaymentCacheData().getQr(), PAYMENT_QR_E_DONATION)) {
//            super.setFinFlexValues1(THAI_QR_FIN_FLEX_VALUES1);
//        }
    }
}
