package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CreditCardFee {
	private String billPayment;

	public CreditCardFee(BigDecimal billPayment) {
		this.billPayment = billPayment.setScale(2).toString();
	}
}
