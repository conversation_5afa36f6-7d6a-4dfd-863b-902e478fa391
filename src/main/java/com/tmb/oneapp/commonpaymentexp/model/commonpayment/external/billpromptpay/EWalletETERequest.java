package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;


@Setter
@Getter
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EWalletETERequest {

    private Sender sender;
    private Receiver receiver;
    private Terminal terminal;
    private String amount;
    private String rtpTransactionReference;
    private String transactionReference;
    private String transactionCreatedDatetime;
    private String senderType;
    private String effectiveDate;
    private Double fee;
    private String receiverType;
    private String chargeType;
    private String chargeCode;
    private String reference1;
    private String reference2;
    private String reference3;
    private String instructionId;

}
