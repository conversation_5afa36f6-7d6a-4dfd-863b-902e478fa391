package com.tmb.oneapp.commonpaymentexp.model.notification;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public abstract class NotificationPayment extends Notification {
    protected String addDateTimeEN;
    protected String addDateTimeTH;
    protected String accountNickname;
    protected String fromAcctId;
    protected String favoriteNickname;
    protected String amount;
    protected String interest;
    protected String fee;
    protected String note;
    protected String transactionRefNo;

    public void setToFavoriteNickname(String favoriteNickname) {
        this.favoriteNickname = StringUtils.isBlank(favoriteNickname) ? "-" : favoriteNickname;
    }

    protected NotificationPayment(String templateName, String crmId, String correlationId) {
        super(templateName, crmId, correlationId);
    }

}
