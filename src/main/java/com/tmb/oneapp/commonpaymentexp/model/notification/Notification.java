package com.tmb.oneapp.commonpaymentexp.model.notification;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
@ToString
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
public abstract class Notification {

    @JsonProperty("template_name")
    protected String templateName;
    protected String crmId;
    protected String xCorrelationId;
    protected String custFullNameTH;
    protected String custFullNameEN;
    protected String channelNameEN;
    protected String channelNameTH;

    protected Notification(String templateName, String crmId, String correlationId) {
        this.templateName = templateName;
        this.crmId = crmId;
        this.xCorrelationId = correlationId;
    }

    public void setCustomerName(String firstNameEn, String lastNameEn, String fistNameTh, String lastNameTh) {
        this.custFullNameEN = StringUtils.isBlank(lastNameEn) ? firstNameEn : firstNameEn + " " + lastNameEn;
        this.custFullNameTH = StringUtils.isBlank(lastNameTh) ? fistNameTh : fistNameTh + " " + lastNameTh;
    }

    public void setChannelName(String channelNameEn, String channelNameTh) {
        this.channelNameEN = channelNameEn;
        this.channelNameTH = channelNameTh;
    }
}
