package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AdditionalParamCommonPaymentDraftCache {
    private String easyPassAccountName;

    /**
     * Use for : Loan,
     */
    private boolean isPayByOwner;
}
