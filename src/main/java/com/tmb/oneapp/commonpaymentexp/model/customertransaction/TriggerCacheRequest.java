package com.tmb.oneapp.commonpaymentexp.model.customertransaction;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@ToString
@Accessors (chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class TriggerCacheRequest {
    @JsonProperty("crm_id")
    @Schema(description = "CRM ID of customer")
    private String crmId;
    @JsonProperty("channel_name")
    @Schema(description = "Channel through which customer updated acc details")
    private String channelName = "pb";
    @JsonProperty("product_group")
    @Schema(description = "Product group of customer(Debit,credit card) ")
    private String productGroup;
}
