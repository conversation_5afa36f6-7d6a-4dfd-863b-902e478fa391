
package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Setter
@Getter
@Accessors(chain = true)
@JsonPropertyOrder({"id", "type", "branchId", "recordId", "pccTraceId", "traceId", "issuerRoutingId", "benRoutingId",
        "acquirerRoutingId", "reversalCode", "originalType", "responseCode", "pin", "state", "country", "issuerFee",
        "benFee", "chargeFee", "region", "trackToLength", "trackToData", "routeStat", "productInd", "dpcId",
        "releaseId", "switchDate", "chequeNo", "senderBranch", "receiverBranch", "senderReferenceNo", "channelId"})
public class Terminal {

    private String id;
    private String type;
    private String branchId;
    private String recordId;
    private String pccTraceId;
    private String traceId;
    private String issuerRoutingId;
    private String benRoutingId;
    private String acquirerRoutingId;
    private String reversalCode;
    private String originalType;
    private String responseCode;
    private String pin;
    private String state;
    private String country;
    private Double issuerFee;
    private Double benFee;
    private Double chargeFee;
    private String region;
    private String trackToLength;
    private String trackToData;
    private String routeStat;
    private String productInd;
    private String dpcId;
    private String releaseId;
    private String switchDate;
    private String chequeNo;
    private String senderBranch;
    private String receiverBranch;
    private String senderReferenceNo;
    private String channelId;
    private Double acquirerFee;
    private String receiverAccountIdLength;
    private String senderAccountIdLength;

}
