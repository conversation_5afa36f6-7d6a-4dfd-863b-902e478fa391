package com.tmb.oneapp.commonpaymentexp.model.cache;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeepLinkRequestInCache {
    private String callFrom;
    @Deprecated
    private String amount;
    private String transType;
    private String transId;
    private String transMemo;
}
