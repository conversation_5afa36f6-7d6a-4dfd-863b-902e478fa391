package com.tmb.oneapp.commonpaymentexp.model.transactionlog;

import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AutoLoanOCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseConfirmLogRecord;
import com.tmb.oneapp.commonpaymentexp.utils.MaskingUtils;
import com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import java.util.Optional;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.MAX_TO_ACCT_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.MB;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNull;


@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TransactionLogMapper {
    public static final TransactionLogMapper INSTANCE = new TransactionLogMapper();
    public static final String TRANSACTION_LOG_SUCCESS = "success";
    public static final String TRANSACTION_LOG_REFERENCE_ACTIVITY_ID_VALUE = "000";
    public static final String TRANSACTION_LOG_DEBIT_TRANSACTION = "2";

    public TransactionActivityAutoLoanBillPay mapToTransactionActivityAutoLoanBillPay(BaseConfirmLogRecord baseConfirmLogRecord, CommonPaymentDraftCache draftCache) {
        final MasterBillerResponseInCache masterBillerResponse = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getMasterBillerResponse());
        final var validationRequest = NullSafeUtils.requireNonNull(draftCache::getValidateRequest);
        final var paymentInformation = NullSafeUtils.requireNonNull(draftCache::getPaymentInformation);
        final DepositAccountInCache fromDepositAccount = draftCache.getValidateDraftCache().getFromDepositAccount();
        final boolean isRef2Present = Optional.of(masterBillerResponse).map(MasterBillerResponseInCache::getRef2).isPresent();
        final String toAccountNickname = generateBillerName(
                getSafeNull(() -> masterBillerResponse.getBillerInfo().getBillerCompCode()),
                getSafeNull(() -> masterBillerResponse.getBillerInfo().getNameEn())
        );

        var autoLoanTransactionLogBuilder = TransactionActivityAutoLoanBillPay.builder()
                .transactionDate(baseConfirmLogRecord.transactionDateTime())
                .financialTransferRefId(baseConfirmLogRecord.referenceId())
                .crmId(baseConfirmLogRecord.crmId())
                .activityId(baseConfirmLogRecord.activityTypeId3Digits())

                .channelId(MB)
                .referenceActivityTypeId(TRANSACTION_LOG_REFERENCE_ACTIVITY_ID_VALUE)
                .transactionStatus(TRANSACTION_LOG_SUCCESS)
                .financialTransferCRDR(TRANSACTION_LOG_DEBIT_TRANSACTION)

                .billerCompCode(masterBillerResponse.getBillerInfo().getBillerCompCode())
                .billerNameEn(masterBillerResponse.getBillerInfo().getNameEn())
                .billerNameTh(masterBillerResponse.getBillerInfo().getNameTh())
                .labelRef1En(masterBillerResponse.getRef1().getLabelEn())
                .labelRef1Th(masterBillerResponse.getRef1().getLabelTh())
                .fromAccountNickname(fromDepositAccount.getProductNickname())

                .financialTransferMemo(validationRequest.getNote())
                .toAccountNickname(toAccountNickname)
                .billerRef1(paymentInformation.getProductDetail().getProductRef1());

        if (isRef2Present) {
            autoLoanTransactionLogBuilder
                    .billerRef2(paymentInformation.getProductDetail().getProductRef2())
                    .labelRef2En(masterBillerResponse.getRef2().getLabelEn())
                    .labelRef2Th(masterBillerResponse.getRef2().getLabelTh());
        }

        if (WowPointUtils.isWowPointTransaction(validationRequest, draftCache.getCommonPaymentRule())) {
            AutoLoanOCPBillRequest autoLoanOCPBillRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getAutoLoanOCPBillPaymentConfirmRequest();
            WowPointRedeemConfirmRequest wowPointRequest = draftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest();
            autoLoanTransactionLogBuilder
                    .fromAccountNo(shortenAccountId(autoLoanOCPBillRequest.getFromAccount().getAccountId()))
                    .toAccountNo(String.valueOf(autoLoanOCPBillRequest.getAmount()))
                    .financialTransferAmount(String.valueOf(wowPointRequest.getTxnAmount()))
                    .wowPointDiscount(String.valueOf(wowPointRequest.getPointUnits()))
                    .wowPointDiscountAmount(String.valueOf(wowPointRequest.getAmount()))
                    .totalPayWithWowAmount(String.valueOf(wowPointRequest.getTxnAmount().add(wowPointRequest.getAmount())));
        } else {
            TopUpETEPaymentRequest eteConfirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getTopUpETEPaymentRequest();
            autoLoanTransactionLogBuilder
                    .fromAccountNo(shortenAccountId(eteConfirmRequest.getFromAccount().getAccountId()))
                    .toAccountNo(shortenAccountId(eteConfirmRequest.getToAccount().getAccountId()))
                    .financialTransferAmount(String.valueOf(eteConfirmRequest.getAmount()));
        }

        return autoLoanTransactionLogBuilder.build();
    }

    public TransactionActivityCreditCardBillPay mapToTransactionActivityCreditCardBillPay(BaseConfirmLogRecord baseConfirmLogRecord, CommonPaymentDraftCache draftCache) {
        final MasterBillerResponseInCache masterBillerResponse = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getMasterBillerResponse());
        final CreditCardConfirmRequest creditCardConfirmRequest = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getExternalConfirmRequest().getCreditCardConfirmRequest());
        final DepositAccountInCache fromDepositAccount = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getFromDepositAccount());
        final ValidationCommonPaymentRequest validationRequest = NullSafeUtils.requireNonNull(draftCache::getValidateRequest);
        final boolean isRef2Present = Optional.of(masterBillerResponse).map(MasterBillerResponseInCache::getRef2).isPresent();
        final String toAccountNickname = generateBillerName(
                getSafeNull(() -> masterBillerResponse.getBillerInfo().getBillerCompCode()),
                getSafeNull(() -> masterBillerResponse.getBillerInfo().getNameEn())
        );

        var transactionCreditCardBuilder = TransactionActivityCreditCardBillPay.builder()
                .transactionDate(baseConfirmLogRecord.transactionDateTime())
                .financialTransferRefId(baseConfirmLogRecord.referenceId())
                .crmId(baseConfirmLogRecord.crmId())
                .activityId(baseConfirmLogRecord.activityTypeId3Digits())

                .channelId(MB)
                .referenceActivityTypeId(TRANSACTION_LOG_REFERENCE_ACTIVITY_ID_VALUE)
                .transactionStatus(TRANSACTION_LOG_SUCCESS)
                .financialTransferCRDR(TRANSACTION_LOG_DEBIT_TRANSACTION)

                .billerCompCode(masterBillerResponse.getBillerInfo().getBillerCompCode())
                .billerNameEn(masterBillerResponse.getBillerInfo().getNameEn())
                .billerNameTh(masterBillerResponse.getBillerInfo().getNameTh())
                .labelRef1En(masterBillerResponse.getRef1().getLabelEn())
                .labelRef1Th(masterBillerResponse.getRef1().getLabelTh())

                .fromAccountNo(shortenAccountId(creditCardConfirmRequest.getBillPayment().getPayerAccount().getId()))
                .toAccountNo(shortenAccountId(creditCardConfirmRequest.getBillPayment().getPayeeCard().getAccountId()))
                .financialTransferAmount(creditCardConfirmRequest.getBillPayment().getAmount())
                .billerRef1(creditCardConfirmRequest.getBillPayment().getRef1())
                .fromAccountNickname(fromDepositAccount.getProductNickname())
                .financialTransferMemo(validationRequest.getNote())

                .toAccountNickname(toAccountNickname);

        if (shouldMaskingRef1(masterBillerResponse)) {
            transactionCreditCardBuilder.billerRef1(MaskingUtils.maskCreditCard(creditCardConfirmRequest.getBillPayment().getRef1()));
        }

        if (isRef2Present) {
            transactionCreditCardBuilder
                    .labelRef2En(masterBillerResponse.getRef2().getLabelEn())
                    .labelRef2Th(masterBillerResponse.getRef2().getLabelTh())
                    .billerRef2(creditCardConfirmRequest.getBillPayment().getRef2());
        }

        return transactionCreditCardBuilder.build();
    }

    public TransactionActivityLoan mapToTransactionActivityLoanBillPay(BaseConfirmLogRecord baseConfirmLogRecord, CommonPaymentDraftCache draftCache) {
        final MasterBillerResponseInCache masterBillerResponse = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getMasterBillerResponse());
        final var externalConfirmRequest = NullSafeUtils.requireNonNull(() -> draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest());
        final var validationRequest = NullSafeUtils.requireNonNull(draftCache::getValidateRequest);
        final var paymentInformation = NullSafeUtils.requireNonNull(draftCache::getPaymentInformation);
        final boolean isRef2Present = Optional.of(masterBillerResponse).map(MasterBillerResponseInCache::getRef2).isPresent();
        final DepositAccountInCache fromDepositAccount = draftCache.getValidateDraftCache().getFromDepositAccount();
        final String toAccountNickname = generateBillerName(
                getSafeNull(() -> masterBillerResponse.getBillerInfo().getBillerCompCode()),
                getSafeNull(() -> masterBillerResponse.getBillerInfo().getNameEn())
        );

        var loanTransactionLogBuilder = TransactionActivityLoan.builder()
                .transactionDate(baseConfirmLogRecord.transactionDateTime())
                .financialTransferRefId(baseConfirmLogRecord.referenceId())
                .crmId(baseConfirmLogRecord.crmId())
                .activityId(baseConfirmLogRecord.activityTypeId3Digits())

                .channelId(MB)
                .referenceActivityTypeId(TRANSACTION_LOG_REFERENCE_ACTIVITY_ID_VALUE)
                .transactionStatus(TRANSACTION_LOG_SUCCESS)
                .financialTransferCRDR(TRANSACTION_LOG_DEBIT_TRANSACTION)

                .billerCompCode(masterBillerResponse.getBillerInfo().getBillerCompCode())
                .billerNameEn(masterBillerResponse.getBillerInfo().getNameEn())
                .billerNameTh(masterBillerResponse.getBillerInfo().getNameTh())
                .labelRef1En(masterBillerResponse.getRef1().getLabelEn())
                .labelRef1Th(masterBillerResponse.getRef1().getLabelTh())
                .billerRef1(paymentInformation.getProductDetail().getProductRef1())
                .fromAccountNo(shortenAccountId(externalConfirmRequest.getFromAccount().getAccountId()))
                .fromAccountNickname(fromDepositAccount.getProductNickname())
                .toAccountNo(shortenAccountId(externalConfirmRequest.getToAccount().getAccountId()))
                .financialTransferAmount(String.valueOf(externalConfirmRequest.getAmount()))

                .financialTransferMemo(validationRequest.getNote())
                .toAccountNickname(toAccountNickname);

        if (isRef2Present) {
            loanTransactionLogBuilder
                    .billerRef2(paymentInformation.getProductDetail().getProductRef2())
                    .labelRef2En(masterBillerResponse.getRef2().getLabelEn())
                    .labelRef2Th(masterBillerResponse.getRef2().getLabelTh());
        }

        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            WowPointRedeemConfirmRequest wowPointRequest = draftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest();
            loanTransactionLogBuilder
                    .financialTransferAmount(String.valueOf(wowPointRequest.getTxnAmount()))
                    .wowPointDiscount(String.valueOf(wowPointRequest.getPointUnits()))
                    .wowPointDiscountAmount(String.valueOf(wowPointRequest.getAmount()))
                    .totalPayWithWowAmount(String.valueOf(wowPointRequest.getTxnAmount().add(wowPointRequest.getAmount())));
        }

        return loanTransactionLogBuilder.build();
    }


    private static boolean shouldMaskingRef1(MasterBillerResponseInCache masterBiller) {
        String billerCategoryCode = getSafeNull(() -> masterBiller.getBillerInfo().getBillerCategoryCode());
        return StringUtils.equals(billerCategoryCode, BILLER_CATEGORY_CODE_CREDIT_CARD);
    }

    private static String generateBillerName(String compCode, String billerName) {
        if (compCode == null || billerName == null) {
            return null;
        }

        String billerName13DigitsFromLeft = StringUtils.left(billerName, 13);
        return "%s (%s)".formatted(billerName13DigitsFromLeft, compCode);
    }

    private static String shortenAccountId(String accountId) {
        return StringUtils.right(accountId, MAX_TO_ACCT_ID);
    }
}
