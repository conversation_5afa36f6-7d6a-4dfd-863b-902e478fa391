package com.tmb.oneapp.commonpaymentexp.model.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Accessors(chain = true)
public class CustomerCrmProfile {
    private String crmId;
    private Integer ebTxnLimitAmt;
    private Integer ebMaxLimitAmtCurrent;
    private String ebMaxLimitTempFlag;
    private Integer ebMaxLimitAmtHist;
    private Integer ebMaxLimitAmtRequest;
    private Double ebAccuUsgAmtDaily;
    private String ebCustomerStatusId;
    private String ebUserStatusDesc;
    private String ibUserStatusId;
    private String mbUserStatusId;
    private String mbUserStatusDesc;
    private String referCd;
    private Integer ottMaxLimitAmtCurrent;
    private String ottMaxLimitTempFlag;
    private Integer ottAccuUsgAmtDaily;
    private Integer ottMaxLimitAmtHist;
    private Integer ottMaxLimitAmtRequest;
    private String pinFreeSeetingFlag;
    private Integer pinFreeTrLimit;
    private Integer pinFreeBpLimit;
    private Integer pinFreeTxnCount;
    private String defaultAcctId;
    private String autoSaveSlipMain;
    private String autoSaveSlipOwn;
    private String quickBalanceSettingFlag;
    private Integer cardlessMaxLimitAmt;
    private Integer cardlessAccuUsgAmt;
    private Integer cardlessMaxLimitAmtHist;
    private String cardlessMaxLimitTempFlag;
    private String maskAcctIdFlag;
    private String faceRecognizeMode;
    private String frAllowFlag;
    private Integer billpayMaxLimitAmtHist;
    private String billpayMaxLimitTempFlag;
    private Integer billpayMaxLimitAmt;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal billpayAccuUsgAmt;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal paymentAccuUsgAmt;
}
