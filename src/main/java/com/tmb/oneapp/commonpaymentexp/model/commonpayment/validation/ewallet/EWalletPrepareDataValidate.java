package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ewallet;

import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.TransferConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.PrepareDataForValidate;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class EWalletPrepareDataValidate implements PrepareDataForValidate {
    private TransferConfiguration transferConfiguration;
    private MasterBillerResponse masterBillerResponse;
    private DepositAccount fromDepositAccount;
    private CustomerCrmProfile customerCrmProfile;
    private CustomerKYCResponse customerKYC;
}
