package com.tmb.oneapp.commonpaymentexp.model.commonpaymenttoggle;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonPaymentToggleConfig {

    private Boolean topUp;
    private Boolean topUpEWallet;
    private Boolean billOnline;
    private Boolean billOffline;
    private Boolean billTtbCreditCard;
    private Boolean billTtbLoan;
    private Boolean billTtbAutoLoan;
    private Boolean billTtbFleetCard;
    private Boolean billMea;
    private Boolean billPea;
    private Boolean billMwa;
    private Boolean billPromptPay;

}
