package com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation;

import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.BaseActivityBillPayEvent;
import org.springframework.http.HttpHeaders;


public class ActivityBillPayValidationEvent extends BaseActivityBillPayEvent implements ActivityEventForValidate {
    public ActivityBillPayValidationEvent(String activityTypeId, HttpHeaders headers) {
        super(activityTypeId, headers);
    }

    public void setBillerNameWithCompCode(String compCode) {
        super.billerName = super.generateActivityBillerName(compCode);
    }

    public void setBillerNameWithCompCode(String compCode, String billerName) {
        super.billerName = super.generateActivityBillerName(billerName, compCode);
    }

    public void maskingReference1() {
        super.reference1 = super.masking(super.reference1);
    }

    public void maskingReference2() {
        super.reference2 = super.masking(super.reference2);
    }
}
