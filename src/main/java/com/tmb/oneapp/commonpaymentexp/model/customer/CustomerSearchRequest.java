package com.tmb.oneapp.commonpaymentexp.model.customer;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString
public class CustomerSearchRequest {

    @NotBlank
    @Schema(description = "search type as rm-id or mobile-no or citizen-id or passport-no", example = "rm-id", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("search_type")
    private String searchType;

    @NotBlank
    @Schema(description = "search value as 001100000000000000000012027065 or 0629546159 or 3111402386382 or AA123456", example = "001100000000000000000012027065", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("search_value")
    private String searchValue;
}