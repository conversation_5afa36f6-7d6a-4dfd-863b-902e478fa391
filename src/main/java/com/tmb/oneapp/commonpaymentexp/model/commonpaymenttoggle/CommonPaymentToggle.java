package com.tmb.oneapp.commonpaymentexp.model.commonpaymenttoggle;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonPaymentToggle {

    private String id;
    private CommonPaymentToggleConfig androidConfig;
    private CommonPaymentToggleConfig iosConfig;
    private List<String> exceptCompCode;

}
