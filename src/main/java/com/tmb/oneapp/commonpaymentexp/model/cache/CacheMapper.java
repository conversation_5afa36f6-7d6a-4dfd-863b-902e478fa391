package com.tmb.oneapp.commonpaymentexp.model.cache;

import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.DeepLinkRequest;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CardBalancesAsync;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import org.mapstruct.Mapper;
import java.util.List;

@Mapper(componentModel = "spring")
public interface CacheMapper {

    BillerInfoResponseInCache toBillerInfoResponseInCache(BillerInfoResponse billerInfoResponse);
    ReferenceResponseInCache toReferenceResponseInCache(ReferenceResponse referenceResponse);
    MasterBillerResponseInCache toMasterBillerResponseInCache(MasterBillerResponse masterBillerResponse);
    List<DepositAccountInCache> toListDepositAccountInCache(List<DepositAccount> depositAccountList);
    DepositAccountInCache toDepositAccountInCache(DepositAccount depositAccount);
    CreditCardSupplementaryInCache toCreditCardSupplementaryInCache(CreditCardSupplementary creditCardSupplementary);
    List<CreditCardSupplementaryInCache> toListCreditCardSupplementaryInCache(List<CreditCardSupplementary> creditCardSupplementaryList);
    CardBalancesAsyncInCache toCardBalancesAsyncInCache(CardBalancesAsync cardBalancesAsync);
    DeepLinkRequestInCache toDeepLinkRequestInCache(DeepLinkRequest deepLinkRequest);
}
