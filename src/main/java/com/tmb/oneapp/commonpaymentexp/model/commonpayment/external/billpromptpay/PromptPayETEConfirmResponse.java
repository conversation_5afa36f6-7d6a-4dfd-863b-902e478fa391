package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ExternalConfirmResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@Accessors (chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PromptPayETEConfirmResponse implements ExternalConfirmResponse {

    private Sender sender;
    private Receiver receiver;
    private Terminal terminal;
    private BigDecimal amount;
    private String rtpTransactionReference;
    private String transactionReference;
    private String transactionCreatedDatetime;
    private String senderType;
    private String effectiveDate;
    private BigDecimal fee;
    private String receiverType;
    private String chargeType;
    private String chargeCode;
    private String reference1;
    private String reference2;
    private String reference3;
    private String instructionId;

    private Vat vat;
    private Tax tax;
    private Balance balance;

    private String reference4;
    private BigDecimal interRegionFee;
    private String paymentType;
    private String transferType;

}
