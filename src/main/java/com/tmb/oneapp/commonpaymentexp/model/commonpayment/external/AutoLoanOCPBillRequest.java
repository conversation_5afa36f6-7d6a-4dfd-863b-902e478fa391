package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanSender;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class AutoLoanOCPBillRequest extends OCPBillRequest {
    AutoLoanDetail autoLoan;
    AutoLoanSender sender;
}