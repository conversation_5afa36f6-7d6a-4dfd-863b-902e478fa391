package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.creditcard;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ExternalConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.ETEError;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.BillPaymentCreditCard;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreditCardConfirmResponse implements ExternalConfirmResponse {
    BillPaymentCreditCard billPayment;
    List<ETEError> errors;
}
