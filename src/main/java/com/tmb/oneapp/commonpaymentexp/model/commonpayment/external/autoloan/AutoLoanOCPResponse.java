package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccount;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccountPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBalance;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBiller;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPCard;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPMerchant;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.WaiveOCP;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AutoLoanOCPResponse {
    private String rbaNo;
    private String requestId;
    private String requestDateTime;
    private String paymentId;
    private String bankRefId;
    private String appId;
    private String channel;
    private String bankId;
    private String branchId;
    private String tellerId;
    private String amount;
    private String currency;
    private String compCode;
    private String ref1;
    private String ref2;
    private String ref3;
    private String ref4;
    private String tranCode;
    private String epayCode;
    private OCPAccountPayment fromAccount;
    private OCPAccountPayment toAccount;
    private OCPFee fee;
    private OCPAccount account;
    private OCPBiller biller;
    private List<AdditionalParam> additionalParams;
    private WaiveOCP waive;
    private String pmtRefIdent;
    private String invoiceNum;
    private OCPCard card;
    private OCPMerchant merchant;
    private OCPBalance balance;
    private AutoLoan autoLoan;
    private AutoLoanSender sender;
    private AutoLoanTerminal terminal;
}
