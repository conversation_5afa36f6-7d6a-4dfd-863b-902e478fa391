package com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ExternalConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ExternalValidateResponse;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@Accessors (chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EWalletETEResponse implements ExternalValidateResponse, ExternalConfirmResponse {

    private Sender sender;
    private Receiver receiver;
    private Terminal terminal;
    private BigDecimal amount;
    private String rtpTransactionReference;
    private String transactionReference;
    private String transactionCreatedDatetime;
    private String chargeCode;
    private BigDecimal fee;
    private Vat vat;
    private Tax tax;
    private Balance balance;
    private String senderType;
    private String receiverType;

    private String reference1;
    private String reference2;
    private String reference3;
    private String reference4;
    private BigDecimal interRegionFee;
    private String paymentType;
    private String transferType;
    private String instructionId;

}
