package com.tmb.oneapp.commonpaymentexp.model.commonpayment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.WowPoint;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonPaymentRule {

    private String id;
    private String code;
    private boolean couponFlag;
    private boolean creditCardInstallmentsFlag;
    private boolean creditCardOtherFlag;
    private boolean creditCardPointFlag;
    private String defaultPayment;
    private boolean qrCodeFlag;
    private String type;
    private boolean wowPointFlag;
    private WowPoint wowPoint;

}

