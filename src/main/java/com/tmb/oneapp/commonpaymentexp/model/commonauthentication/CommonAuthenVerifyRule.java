package com.tmb.oneapp.commonpaymentexp.model.commonauthentication;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CommonAuthenVerifyRule {
    private String amount;
    private String dailyAmount;
    private String featureId;
    private String compCode;
}
