package com.tmb.oneapp.commonpaymentexp.model.transactionlog;


import com.tmb.oneapp.commonpaymentexp.model.cache.CreditCardSupplementaryInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;

public class TransactionActivityOCPBillPay extends TransactionActivityBillPay {
    public TransactionActivityOCPBillPay(String ePayCode, String crmId, CommonPaymentDraftCache commonPaymentDraftCache, String transactionDateTime) {
        super(ePayCode, crmId, commonPaymentDraftCache, transactionDateTime);
        setUpFields(commonPaymentDraftCache);
    }

    private void setUpFields(CommonPaymentDraftCache commonPaymentDraftCache) {
        OCPBillRequest ocpBillRequest = commonPaymentDraftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest();
        setFromAccountNo(shortenAccountId(ocpBillRequest.getFromAccount().getAccountId()));
        setToAccountNo(shortenAccountId(ocpBillRequest.getToAccount().getAccountId()));
        setFinancialTransferAmount(ocpBillRequest.getAmount());
        setFinancialTransferMemo(commonPaymentDraftCache.getValidateRequest().getNote());

        setBillerRef1(commonPaymentDraftCache.getPaymentInformation().getProductDetail().getProductRef1(), commonPaymentDraftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerCategoryCode());
        if (commonPaymentDraftCache.getValidateDraftCache().getMasterBillerResponse().getRef2() != null) {
            setBillerRef2(commonPaymentDraftCache.getPaymentInformation().getProductDetail().getProductRef2());
        }

        if (commonPaymentDraftCache.getValidateRequest().getCreditCard().isPayWithCreditCardFlag()) {
            CreditCardSupplementaryInCache creditCardDetail = commonPaymentDraftCache.getValidateDraftCache().getFromCreditCardDetail();
            String creditCardNickname = creditCardDetail.getProductNameEn() != null ? creditCardDetail.getProductNameEn() : creditCardDetail.getProductNickname();
            setFromAccountNickname(creditCardNickname);
        } else {
            setFromAccountNickname(commonPaymentDraftCache.getValidateDraftCache().getFromDepositAccount().getProductNickname());
        }

        if (WowPointUtils.isWowPointTransaction(commonPaymentDraftCache.getValidateRequest(), commonPaymentDraftCache.getCommonPaymentRule())) {
            WowPointRedeemConfirmRequest wowPointRequest = commonPaymentDraftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest();
            setFinancialTransferAmount(String.valueOf(wowPointRequest.getTxnAmount()));
            setWowPointDiscount(String.valueOf(wowPointRequest.getPointUnits()));
            setWowPointDiscountAmount(String.valueOf(wowPointRequest.getAmount()));
            setTotalPayWithWowAmount(String.valueOf(wowPointRequest.getTxnAmount().add(wowPointRequest.getAmount())));
        }

        setActivityId(getActivityTransConfirmId(commonPaymentDraftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType()));
    }
}
