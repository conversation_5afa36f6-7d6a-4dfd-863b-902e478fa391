package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityBillPromptPayConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialBillPromptPay;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityBillPromptPay;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class BillPromptPayLogsConfirm implements LogsForConfirm {
    private ActivityBillPromptPayConfirmationEvent activityBillPromptPayConfirmationEvent;
    private FinancialBillPromptPay financialBillPromptPay;
    private TransactionActivityBillPromptPay transactionActivityBillPromptPay;
    private ActivityCustomSlipCompleteEvent activityCustomSlipCompleteEvent;
}
