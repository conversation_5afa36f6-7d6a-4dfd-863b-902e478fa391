package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.cache.CreditCardSupplementaryInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.AdditionalParamCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ValidationCommonPaymentDraftCache {
    /**
     * Deposit account of from account.
     */
    private DepositAccountInCache fromDepositAccount;

    /**
     * Details credit card of from account.
     */
    private CreditCardSupplementaryInCache fromCreditCardDetail;

    /**
     * Field for indicate is require common authentication or not
     * Used in api "confirm" for check if require common authentication
     */
    private boolean isRequireCommonAuthen;

    /**
     * Response of common authentication validation
     * used in api "confirm" for validate common authentication
     * Will be null if #isRequireCommonAuthen is false
     */
    private CommonAuthenticationValidationCommonPaymentResponse commonAuthentication;

    private MasterBillerResponseInCache masterBillerResponse;

    /**
     * Calculated fee after processing the payment
     * This field will be used to store the result of the fee calculation
     * logic during the payment process.
     */
    private BigDecimal feeCalculated;


    /**
     * Total amount of the payment
     * This field will be used to store the result of the total amount calculation with fee
     * logic during the payment process.
     */
    private BigDecimal totalAmount;

    /**
     * External confirm request for confirm with external service in api "confirm"
     */
    private ExternalConfirmRequest externalConfirmRequest;

    private WowPointRedeemConfirmRequest wowPointRedeemConfirmRequest;

    /**
     * Additional param for saving additional param to use it in api "confirm"
     */
    private AdditionalParamCommonPaymentDraftCache additionalParam;
}
