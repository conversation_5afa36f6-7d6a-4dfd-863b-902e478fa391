package com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.BaseActivityEvent;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpHeaders;
import org.springframework.util.StringUtils;

import java.text.DecimalFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.FAIL_REASON;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.FROM_ACCOUNT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.SR_NO;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ActivityAutoLoanBillPayServiceEvent extends BaseActivityEvent {
    private static Map<String, String> activityTypeIdMapper = new HashMap<>();

    static {
        activityTypeIdMapper.put("01", "*********");
        activityTypeIdMapper.put("02", "*********");
        activityTypeIdMapper.put("04", "*********");
    }

    private String hpAccountNo;
    private String result;
    private String requestNo;
    private String paymentAmount;
    private String fromAccount;
    private String ref1;
    private String ref2;


    public ActivityAutoLoanBillPayServiceEvent(HttpHeaders headers, Map<String, String> params,
                                               DeepLinkRequestInCache deepLink, CommonPaymentDraftCache cache) {
        super(headers);
        setUpFields(params, deepLink, cache);
    }

    private void setUpFields(Map<String, String> params, DeepLinkRequestInCache deepLink, CommonPaymentDraftCache cache) {
        String objectiveId = deepLink.getTransType();
        String hpAccountNoParam = Arrays.stream(deepLink.getTransId().split("_")).skip(3).findFirst().orElse("");
        String reasonParam = params.get(FAIL_REASON);
        String fromAccountParam = params.get(FROM_ACCOUNT);
        String srNo = params.get(SR_NO);
        String activityTypeId = activityTypeIdMapper.get(objectiveId);
        setActivityTypeId(activityTypeId);
        setHpAccountNo(hpAccountNoParam);
        setResult("Completed");
        if (srNo != null) {
            setRequestNo(srNo);
        } else {
            setRequestNo("N/A");
        }
        DecimalFormat df = new DecimalFormat("#.00");
        String amount = cache.getPaymentInformation().getAmountDetail().getAmountValue().toPlainString();
        setPaymentAmount(amount);
        setFromAccount(fromAccountParam);
        setRef1(cache.getPaymentInformation().getProductDetail().getProductRef1());
        setRef2(cache.getPaymentInformation().getProductDetail().getProductRef2());
        if (StringUtils.hasLength(reasonParam)) {
            setResult("Failed");
            setActivityStatus(ACTIVITY_FAILURE);
            setFailReason(reasonParam);
        }
    }

}
