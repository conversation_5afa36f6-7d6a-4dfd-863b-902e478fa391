package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import org.springframework.http.HttpHeaders;

public record ConfirmationRequestContext(String crmId, String correlationId, String ipAddress, String cacheKey, String acceptLanguage, String appVersion) {
    public HttpHeaders toHttpHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.add(CommonPaymentExpConstant.HEADER_IP_ADDRESS, ipAddress);
        headers.add(CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE, acceptLanguage);
        headers.add(CommonPaymentExpConstant.HEADER_APP_VERSION, appVersion);
        return headers;
    }
}
