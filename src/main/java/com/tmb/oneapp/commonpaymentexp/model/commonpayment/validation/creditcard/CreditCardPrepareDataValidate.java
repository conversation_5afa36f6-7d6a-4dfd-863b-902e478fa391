package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard;

import com.tmb.common.model.creditcard.CreditCardDetail;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.PrepareDataForValidate;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CreditCardPrepareDataValidate implements PrepareDataForValidate {
    private BillPayConfiguration billPayConfiguration;
    private CustomerKYCResponse customerKYCResponse;
    private MasterBillerResponse masterBillerResponse;
    private CustomerCrmProfile customerCrmProfile;
    private DepositAccount fromDepositAccount;
    private CreditCardSupplementary fromCreditCardDetail;
    private CreditCardDetail targetCreditCardDetail;
}
