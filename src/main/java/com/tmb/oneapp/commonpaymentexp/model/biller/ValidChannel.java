package com.tmb.oneapp.commonpaymentexp.model.biller;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ValidChannel {
    private String channelCode;
    private String itmxChannelCode;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal fee;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal interregionFee;

}
