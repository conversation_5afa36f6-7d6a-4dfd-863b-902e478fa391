package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.BaseActivityConfirmEvent;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialConfirmLog;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.BaseTransactionConfirmLog;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class LogsConfirm implements LogsForConfirm {
    private BaseActivityConfirmEvent activityConfirmEvent;
    private BaseFinancialConfirmLog financialActivityLog;
    private BaseTransactionConfirmLog transactionActivityLog;
    private ActivityCustomSlipCompleteEvent activityCustomSlipCompleteEvent;
}
