package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccount;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.creditcard.CreditCardConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.utils.DateUtils;
import com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils;
import com.tmb.oneapp.commonpaymentexp.utils.QRUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

import static com.tmb.common.constants.TmbCommonUtilityConstants.ACCOUNT_TYPE_CCA;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_EASY_PASS;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_TTB_FLEET_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.PAYMENT_METHOD_TMB_PRODUCT;


public class ConfirmationCommonPaymentResponseMapper {
    private static final TMBLogger<ConfirmationCommonPaymentResponseMapper> logger = new TMBLogger<>(ConfirmationCommonPaymentResponseMapper.class);
    public static final ConfirmationCommonPaymentResponseMapper INSTANCE = new ConfirmationCommonPaymentResponseMapper();

    private ConfirmationCommonPaymentResponseMapper() {
    }

    public ConfirmationCommonPaymentResponse mapToOCPConfirmationCommonPaymentResponse(CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPBillPayment externalResponse) {
        try {
            final String ePayCode = NullSafeUtils.getSafeNull(() -> draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getEpayCode());
            final String transactionTime = prepareData.getTransactionTime();
            final CustomerCrmProfile customerProfile = prepareData.getCustomerCrmProfile();
            final MasterBillerResponseInCache masterBiller = draftCache.getValidateDraftCache().getMasterBillerResponse();

            ConfirmationCommonPaymentResponse response = new ConfirmationCommonPaymentResponse();
            response.setTransactionCreatedDatetime(DateUtils.formatTimestampToISO(transactionTime));
            response.setReferenceNo(ePayCode);
            response.setRemainingBalance(Optional.ofNullable(externalResponse.getAccount()).map(OCPAccount::getAvailBal).map(BigDecimal::new).orElse(null));
            response.setCompleteScreenDetail(draftCache.getPaymentInformation().getCompleteScreenDetail());
            response.setAutoSaveSlip(StringUtils.equalsAnyIgnoreCase("Y", customerProfile.getAutoSaveSlipMain()));

            if (isEasyPassPayment(draftCache)) {
                setEasyPassDetails(response, externalResponse);
            }

            if (!isHideMiniQr(externalResponse, masterBiller)) {
                response.setQr(QRUtils.generateMiniQRSafely(ePayCode));
            }

            return response;
        } catch (NullPointerException eIgnore) {
            logger.error("Error map to OCPConfirmationCommonPaymentResponse got NullPointerException. Please verify the exception. : {}", eIgnore.getMessage(), eIgnore);
            return null;
        }
    }

    public ConfirmationCommonPaymentResponse mapToCreditCardConfirmationCommonPaymentResponse(CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, CreditCardConfirmResponse externalResponse) {
        try {
            final String transactionTime = prepareData.getTransactionTime();
            final String referenceId = draftCache.getValidateDraftCache().getExternalConfirmRequest().getCreditCardConfirmRequest().getBillPayment().getEpayCode();
            final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
            final String availableBalanceFromExternalResponse = NullSafeUtils.getSafeNullOrDefault(() -> externalResponse.getBillPayment().getPayerAccount().getBalances().getAvailable(), "0");

            ConfirmationCommonPaymentResponse confirmResponse = new ConfirmationCommonPaymentResponse();
            confirmResponse.setTransactionCreatedDatetime(DateUtils.formatTimestampToISO(transactionTime));
            confirmResponse.setReferenceNo(referenceId);
            confirmResponse.setRemainingBalance(new BigDecimal(availableBalanceFromExternalResponse).setScale(2, RoundingMode.HALF_DOWN));
            confirmResponse.setCompleteScreenDetail(draftCache.getPaymentInformation().getCompleteScreenDetail());
            confirmResponse.setAutoSaveSlip(StringUtils.equalsAnyIgnoreCase("Y", customerCrmProfile.getAutoSaveSlipMain()));

            return confirmResponse;
        } catch (NullPointerException eIgnore) {
            logger.error("Error map to CreditCardConfirmationCommonPaymentResponse got NullPointerException. Please verify the exception. : {}", eIgnore.getMessage(), eIgnore);
            return null;
        }
    }

    private boolean isEasyPassPayment(CommonPaymentDraftCache draftCache) {
        return BILL_COMP_CODE_EASY_PASS.equals(draftCache.getPaymentInformation().getCompCode());
    }

    private void setEasyPassDetails(ConfirmationCommonPaymentResponse response, OCPBillPayment ocpResponse) {
        response.setEasyPass(new EasyPassConfirmationCommonPaymentResponse().setBalanceAfterTopUp(new BigDecimal(ocpResponse.getRef4())));
    }

    private boolean isHideMiniQr(OCPBillPayment ocpResponse, MasterBillerResponseInCache masterBiller) {
        boolean isTTBProduct = masterBiller.getBillerInfo().getPaymentMethod().equals(PAYMENT_METHOD_TMB_PRODUCT);
        boolean payWithCreditCard = ocpResponse.getFromAccount().getAccountType().equals(ACCOUNT_TYPE_CCA);
        boolean isTTBFleetCard = masterBiller.getBillerInfo().getBillerCompCode().equals(BILL_COMP_CODE_TTB_FLEET_CARD);
        return isTTBProduct || payWithCreditCard || isTTBFleetCard;
    }
}
