package com.tmb.oneapp.commonpaymentexp.model.commonpayment;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CompleteScreenDetail {
    private String remarkEn;
    private String remarkTh;
    private String footerEn;
    private String footerTh;
    private String backBtnKeyEn;
    private String backBtnKeyTh;
    private String backBtnUrl;
}
