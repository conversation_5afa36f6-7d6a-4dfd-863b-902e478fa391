package com.tmb.oneapp.commonpaymentexp.model.creditcardaccount;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.common.model.creditcard.CardCreditLimit;
import com.tmb.common.model.creditcard.CardEmail;
import com.tmb.common.model.creditcard.CardStatus;
import com.tmb.oneapp.commonpaymentexp.model.billpayaccount.AccountShortcut;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CreditCardAccount {
    private String cardNo;
    private String accountId;
    private String cardImageAndroid;
    private String cardImageIos;
    private String productNameEn;
    private String productNameTh;
    private String productNickname;
    private String dueDate;
    private String cardType;
    private String cardName;
    private String updatedDate;
    private String compcode;

    @JsonIgnore
    private String binNumber;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal dueAmount;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal creditSpend;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal creditRemain;

    private List<AccountShortcut> shortcuts;
    private String accountStatus;
    private String productOrder;

    private String productCode;

    @JsonIgnore
    private String cardPloanFlag;

    @JsonIgnore
    private String rawCardNo;

    private String nickname;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal pointRemain;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal creditLimit;
    private CardStatus cardStatus;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal minimumDue;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalUnbilledAmounts;
    private CardEmail cardEmail;
    private CardCreditLimit cardCreditLimit;
    private String totalSpendingThisCycle;
    private String rslProductCode;
    private String allowFromForBillPayTopUpEpayment;
    private String allowSetQuickBalance;
    private String spendingFlag;

    private String statementDate;
    private String cardEmbossingName1;
    private String expiredBy;
    private Long billingCycle;
    private CardPoints cardPoints;
    private CardBalancesAsync cardBalances;
    private CreditCardMenuConfig menuConfig;

    @JsonIgnore
    private MyBenefitRedeemPointsFlag redeemPointsFlag;

    private String cardFace;
}
