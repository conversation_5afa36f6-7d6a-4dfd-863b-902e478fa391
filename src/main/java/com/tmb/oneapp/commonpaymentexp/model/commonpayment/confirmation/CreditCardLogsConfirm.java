package com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation;

import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCreditCardBillPayConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialCreditCardBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityCreditCardBillPay;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class CreditCardLogsConfirm implements LogsForConfirm {
    private ActivityCreditCardBillPayConfirmationEvent activityCreditCardBillPayConfirmationEvent;
    private FinancialCreditCardBillPayActivityLog financialCreditCardBillPayActivityLog;
    private TransactionActivityCreditCardBillPay transactionActivityCreditCardBillPay;
    private ActivityCustomSlipCompleteEvent activityCustomSlipCompleteEvent;
}
