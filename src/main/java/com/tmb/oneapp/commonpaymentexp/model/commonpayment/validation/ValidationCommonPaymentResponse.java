package com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ValidationCommonPaymentResponse {
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "#0.00")
    private BigDecimal fee;
    private String transactionId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "#0.00")
    private BigDecimal amount;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "#0.00")
    private BigDecimal totalAmount;
    private Boolean isRequireCommonAuthen;


    /**
     * This field will only be set when E-Donation transaction (Bill prompt-pay).
     */
    private String citizenId;

    private TopUpTelcoValidationCommonPaymentResponse topUpTelco;
    private EasyPassValidationCommonPaymentResponse easyPass;
    private WowPointValidationCommonPaymentResponse wowPoint;

    private CommonAuthenticationValidationCommonPaymentResponse commonAuthenticationInformation;
    private PEAValidationCommonPaymentResponse peaValidationCommonPaymentResponse;
    private MWAValidationCommonPaymentResponse mwaValidationCommonPaymentResponse;
}