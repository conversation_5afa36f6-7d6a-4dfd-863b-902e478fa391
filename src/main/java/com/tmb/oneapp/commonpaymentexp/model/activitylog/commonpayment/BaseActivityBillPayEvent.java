package com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.BaseActivityEvent;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;

import java.util.Arrays;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_MOBILE;


@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public abstract class BaseActivityBillPayEvent extends BaseActivityEvent {

    protected String flow;
    protected String step;
    protected String cardNumber;
    protected String fromAccount;
    protected String billerName;
    protected String reference1;
    protected String reference2;
    protected String amount;
    protected String fee;
    protected String refNo;
    protected String topupRefNo;
    protected String toBankShortName;
    protected String toLinkedAccountNumber;
    protected String statusName;
    protected String reason;
    protected String toFavoriteName;
    protected String paymentMethod;
    protected String wowPoint;
    protected String creditCardPoint;
    protected String couponDiscount;
    protected String totalAmount;
    protected String pinFree;
    protected String isForceFr;
    protected String isForceDipchip;

    protected BaseActivityBillPayEvent(String activityTypeId, HttpHeaders headers) {
        super(headers);
        super.setActivityTypeId(activityTypeId);
    }

    protected String generateActivityBillerName(String billerName, String compCode) {
        return billerName + " (" + compCode + ")";
    }

    protected String generateActivityBillerName(String compCode) {
        if (compCode == null) {
            return null;
        }

        if (this.billerName == null) {
            return "biller-name (" + compCode + ")";
        }

        return billerName + " (" + compCode + ")";
    }

    protected String masking(String data) {
        if (data == null) {
            return null;
        }
        if (StringUtils.length(data) <= 3) {
            return data;
        }

        return "XX" + data.substring(data.length() - 4);
    }

    protected void setReference1(String reference1, String billerCategoryCode, Boolean isMobile) {
        if (Arrays.asList(BILLER_CATEGORY_CODE_CREDIT_CARD, BILLER_CATEGORY_CODE_MOBILE).contains(billerCategoryCode) ||
                Boolean.TRUE.equals(isMobile)) {
            this.reference1 = masking(reference1);
        } else {
            this.reference1 = reference1;
        }
    }

    protected void maskingReference1() {
        this.reference1 = this.masking(this.reference1);
    }

    protected void maskingReference2() {
        this.reference2 = this.masking(this.reference2);
    }

    protected String getOverrideFlow(CommonPaymentDraftCache draftCache) {
        var validateRequest = draftCache.getValidateRequest();
        var deepLinkRequestInCache = draftCache.getDeepLinkRequest();

        boolean isTransactionFromDeepLink = deepLinkRequestInCache != null;
        String overrideFlow = validateRequest.getFlow();
        if (isTransactionFromDeepLink) {
            overrideFlow = Optional.of(deepLinkRequestInCache).map(DeepLinkRequestInCache::getCallFrom).orElse("call-from-in-deep-link-is-null");
        }
        return overrideFlow;
    }
}
