package com.tmb.oneapp.commonpaymentexp.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

@Getter
public class NullPointerCustomException extends NullPointerException {
    private final HttpStatus status;

    public NullPointerCustomException(String errorMessage, HttpStatus status) {
        super(errorMessage);
        this.status = status;
    }

    public NullPointerCustomException(String errorMessage) {
        super(errorMessage);
        this.status = HttpStatus.BAD_REQUEST;
    }

    public NullPointerCustomException() {
        super();
        this.status = HttpStatus.BAD_REQUEST;
    }

}
