package com.tmb.oneapp.commonpaymentexp.exception;

import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Description;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ValidationExceptionHandler {
    private static final TMBLogger<ValidationExceptionHandler> logger = new TMBLogger<>(ValidationExceptionHandler.class);
    private static final String DEFAULT_ERROR_CODE = "999999";
    private final Description defaultDescription;

    public ValidationExceptionHandler(@Qualifier("errorMapping") Map<String, Description> errorMapping) {
        this.defaultDescription = errorMapping.get(DEFAULT_ERROR_CODE);
    }


    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<TmbServiceResponse<Void>> handleValidationExceptions(MethodArgumentNotValidException ex) {
        Map<String, String> errors = new HashMap<>();

        ex.getBindingResult().getFieldErrors().forEach(error -> {
            String fieldName = error.getField();
            String errorMessage = error.getDefaultMessage();
            String rejectedValue = error.getRejectedValue() != null ?
                    String.valueOf(error.getRejectedValue()) : "null";
            errors.put(fieldName, errorMessage + " (receive: " + rejectedValue + ")");
        });

        TmbServiceResponse<Void> response = new TmbServiceResponse<>();
        response.setStatus(new Status(ResponseCode.MISSING_REQUIRED_FIELD_ERROR.getCode(),
                "Validation failed: " + errors,
                ResponseCode.MISSING_REQUIRED_FIELD_ERROR.getService(),
                defaultDescription));

        return ResponseEntity.badRequest().body(response);
    }

    @ExceptionHandler(NullPointerCustomException.class)
    public ResponseEntity<TmbServiceResponse<Void>> handleNullPointerCustomException(final NullPointerCustomException ex) {
        logger.error("handle NullPointerCustomException occurred. Stack trace is: {}", ExceptionUtils.getStackTrace(ex));
        TmbServiceResponse<Void> response = new TmbServiceResponse<>();
        response.setStatus(new Status(ResponseCode.FAILED_V2.getCode(),
                "Null pointer failed: " + ex.getMessage(),
                ResponseCode.FAILED_V2.getService(),
                defaultDescription));

        return ResponseEntity.status(ex.getStatus()).body(response);
    }
}
