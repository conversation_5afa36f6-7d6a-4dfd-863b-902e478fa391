package com.tmb.oneapp.commonpaymentexp.service.dailylimit;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.DailyUsageLimit;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;

import java.math.BigDecimal;
import java.util.Optional;

public class BillPayDailyLimitStrategy implements DailyLimitStrategy {
    @Override
    public DailyUsageLimit calculateLimit(CustomerCrmProfile profile) {
        return new DailyUsageLimit(
                Optional.ofNullable(profile.getBillpayAccuUsgAmt())
                        .orElse(BigDecimal.ZERO),
                BigDecimal.valueOf(profile.getBillpayMaxLimitAmt())
        );
    }
}
