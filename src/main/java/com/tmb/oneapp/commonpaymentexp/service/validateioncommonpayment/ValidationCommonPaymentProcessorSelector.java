package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class ValidationCommonPaymentProcessorSelector {

    private static final TMBLogger<ValidationCommonPaymentProcessorSelector> logger = new TMBLogger<>(ValidationCommonPaymentProcessorSelector.class);
    private final Map<String, ValidationCommonPaymentProcessor> processorMap = new HashMap<>();

    @Autowired
    public ValidationCommonPaymentProcessorSelector(List<ValidationCommonPaymentProcessor> processors) {
        for (ValidationCommonPaymentProcessor processor : processors) {
            processorMap.put(processor.getProcessorType().toLowerCase(), processor);
        }
    }

    /**
     * Get processor service by transactionType match with processor key
     *
     * @param transactionType value in cache. (PaymentInformation.transactionType)
     * @return Processor of transactionType service like BillPayValidationProcessor (when transactionType = bill_pay)
     */
    public ValidationCommonPaymentProcessor getProcessor(String transactionType) {
        return processorMap.get(transactionType.toLowerCase());
    }

    /**
     * This processor allows transactions to be matched with the key:
     * <pre>
     *     COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY = "bill_pay"
     * </pre>
     * The method validates that the transaction type matches the key of the processor.
     *
     * @param transactionType the value in the cache (PaymentInformation.transactionType).
     * @throws TMBCommonException if the transactionType does not match the key of the processor, a MISSING_REQUIRED_FIELD_ERROR is thrown.
     */
    public void validateTransactionType(String transactionType) throws TMBCommonException {
        boolean isTransactionTypeNotMatch = !processorMap.containsKey(transactionType.toLowerCase());
        if (isTransactionTypeNotMatch) {
            logger.error("Error transaction_type incorrect. [ transactionType = {}, [allow_transaction_type = {}]]", transactionType, getAllKeys());
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.MISSING_REQUIRED_FIELD_ERROR, "Please verify request transaction_type when Initial process");
        }
    }

    private Set<String> getAllKeys() {
        return processorMap.keySet();
    }
}
