package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.autoloan;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.client.HpExpServiceFeignClient;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityAutoLoanBillPayServiceEvent;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.AutoLoanExternalConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2AddServiceRequest;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2AddServiceRequestResponse;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2UpdatePaymentFlagRequest;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2UpdatePaymentFlagResponse;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import feign.FeignException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_SUCCESS;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ERROR_DESC;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.FAIL_REASON;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.FROM_ACCOUNT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.PAYMENT_STATUS;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.SR_NO;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNull;
import static java.util.Objects.nonNull;

@Component
@RequiredArgsConstructor
public class AutoLoanDeepLinkHelper {
    private static final TMBLogger<AutoLoanDeepLinkHelper> logger = new TMBLogger<>(AutoLoanDeepLinkHelper.class);
    private final HpExpServiceFeignClient hpExpServiceFeignClient;
    private final LogEventPublisherService logEventPublisherService;

    public K2AddServiceRequestResponse handleServiceRequest(DeepLinkRequestInCache deepLinkRequest, HttpHeaders headers, String fromAccountId, CommonPaymentDraftCache draftCache) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        String installmentPay = "00";
        if (installmentPay.equals(deepLinkRequest.getTransType())) {
            logger.info("payment from auto-loan is installment");
            return null;
        }

        try {
            logger.info("calling hp-exp-service Add Service Deep Link:{}", TMBUtils.convertJavaObjectToString(deepLinkRequest));
        } catch (JsonProcessingException e) {
            logger.info("calling hp-exp-service Add Service Request unknown error with json");
        }

        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        K2AddServiceRequest k2AddServiceRequest = new K2AddServiceRequest();
        k2AddServiceRequest.setRedisKey(deepLinkRequest.getTransId());
        k2AddServiceRequest.setTransactionDateTime(sf.format(new Date()));

        Map<String, String> paramsActLog = new HashMap<>();
        paramsActLog.put(FROM_ACCOUNT, fromAccountId);
        paramsActLog.put(HEADER_CORRELATION_ID, correlationId);

        try {
            logger.info("calling hp-exp-service Add Service Request RedisKey:{}", k2AddServiceRequest.getRedisKey());
            var oneAppResponse = hpExpServiceFeignClient.addServiceRequest(correlationId, k2AddServiceRequest);
            logger.info("calling hp-exp-service Add Service Request SUCCESS RedisKey:{}", k2AddServiceRequest.getRedisKey());
            K2AddServiceRequestResponse k2AddServiceRequestResponse = oneAppResponse.getData();
            String k2AddServiceStatusCode = k2AddServiceRequestResponse.getCode();

            if (!AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE.equalsIgnoreCase(k2AddServiceStatusCode)) {
                paramsActLog.put(FAIL_REASON, "Cannot add service request to K2");
                performAutoLoanActivityLog(paramsActLog, headers, deepLinkRequest, draftCache);
                return null;
            }

            return k2AddServiceRequestResponse;
        } catch (FeignException e) {
            logger.error("hp-exp-service Add Service Request unavailable: {}", e);
            paramsActLog.put(FAIL_REASON, e.status() + ": service down for add service request");
            performAutoLoanActivityLog(paramsActLog, headers, deepLinkRequest, draftCache);
            throw e;
        }
    }

    public void updateAutoLoanPaymentStatusSuccess(HttpHeaders headers, CommonPaymentDraftCache draftCache, AutoLoanExternalConfirmResponse externalResponse, K2AddServiceRequestResponse k2AddServiceResponse) {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final DeepLinkRequestInCache deepLinkRequest = Objects.requireNonNull(draftCache.getDeepLinkRequest());
        String accountId;
        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            accountId = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getPaymentId();
        } else {
            accountId = draftCache.getValidateDraftCache().getExternalConfirmRequest().getTopUpETEPaymentRequest().getPaymentId();
        }

        Map<String, String> params = new HashMap<>();
        params.put(FROM_ACCOUNT, accountId);
        params.put(HEADER_CRM_ID, crmId);
        params.put(HEADER_CORRELATION_ID, correlationId);
        params.put(PAYMENT_STATUS, "01");
        this.handleUpdatePaymentStatus(params, headers, deepLinkRequest, externalResponse, k2AddServiceResponse, draftCache);
    }

    public void handleUpdatePaymentStatus(Map<String, String> params, HttpHeaders headers, DeepLinkRequestInCache deepLinkRequest, AutoLoanExternalConfirmResponse externalResponse, K2AddServiceRequestResponse k2AddServiceResponse, CommonPaymentDraftCache draftCache) {
        if (!AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE.equalsIgnoreCase(k2AddServiceResponse.getCode())) {
            logger.info("NOT calling hp-exp-service update payment status due to add service request return: {}", k2AddServiceResponse.getCode());
            return;
        }

        String objectiveId = deepLinkRequest.getTransType();
        String fromAccount = params.get(FROM_ACCOUNT);
        String crmId = params.get(HEADER_CRM_ID);
        String correlationId = params.get(HEADER_CORRELATION_ID);
        String paymentStatus = params.get(PAYMENT_STATUS);
        String errorDesc = params.get(ERROR_DESC);

        Map<String, String> paramsActLog = new HashMap<>();
        paramsActLog.put(FROM_ACCOUNT, fromAccount);
        paramsActLog.put(HEADER_CORRELATION_ID, correlationId);
        paramsActLog.put(SR_NO, k2AddServiceResponse.getSrNo());

        List<String> failedReasons = new ArrayList<>();

        try {
            K2UpdatePaymentFlagRequest k2UpdatePaymentFlagRequest = new K2UpdatePaymentFlagRequest();
            k2UpdatePaymentFlagRequest.setPaymentStatus(paymentStatus);
            k2UpdatePaymentFlagRequest.setCrmId(crmId);

            logger.info("calling hp-exp-service Add Service Request Response:{}", TMBUtils.convertJavaObjectToString(k2AddServiceResponse));
            k2UpdatePaymentFlagRequest.setSrid(k2AddServiceResponse.getSrId());
            k2UpdatePaymentFlagRequest.setSrNo(k2AddServiceResponse.getSrNo());

            if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
                k2UpdatePaymentFlagRequest.setTransactionId(externalResponse.getAutoLoanOCPResponse().getPaymentId());
            }

            if (nonNull(getSafeNull(() -> externalResponse.getTopUpETEResponse().getTransaction()))) {
                logger.info("calling hp-exp-service ETE Payment Response:{}", TMBUtils.convertJavaObjectToString(externalResponse.getTopUpETEResponse().getTransaction()));
                k2UpdatePaymentFlagRequest.setPaymentDateTime(externalResponse.getTopUpETEResponse().getTransaction().getPaymentDateTime());
                k2UpdatePaymentFlagRequest.setTransactionId(externalResponse.getTopUpETEResponse().getTransaction().getPaymentId());
            }

            if (k2UpdatePaymentFlagRequest.getPaymentDateTime() == null) {
                SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                k2UpdatePaymentFlagRequest.setPaymentDateTime(sf.format(new Date()));
            }

            if ("02".equals(paymentStatus)) {
                failedReasons.add("Failed payment from ETE");
                k2UpdatePaymentFlagRequest.setPaymentStatusDesc(errorDesc);

            }

            k2UpdatePaymentFlagRequest.setObjectiveId(objectiveId);

            logger.info("calling hp-exp-service Update Payment Flag Request:{}", TMBUtils.convertJavaObjectToString(k2UpdatePaymentFlagRequest));
            logger.info("calling hp-exp-service Update Payment Flag srId:{}, SrNo:{}", k2UpdatePaymentFlagRequest.getSrid(), k2UpdatePaymentFlagRequest.getSrNo());

            HttpHeaders headersUpdatePaymentFlag = new HttpHeaders();
            headersUpdatePaymentFlag.set(HEADER_CORRELATION_ID, correlationId);
            headersUpdatePaymentFlag.set(HEADER_CRM_ID, crmId);

            var oneAppResponse = hpExpServiceFeignClient.updatePaymentFlag(headersUpdatePaymentFlag, k2UpdatePaymentFlagRequest);
            logger.info("calling hp-exp-service Update Payment Flag return status code:{} srId:{}, SrNo:{}", k2AddServiceResponse.getCode(), k2UpdatePaymentFlagRequest.getSrid(), k2UpdatePaymentFlagRequest.getSrNo());

            K2UpdatePaymentFlagResponse updatePaymentResponse = oneAppResponse.getData();
            String k2StatusResponse = updatePaymentResponse.getCode();

            if (!AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE.equalsIgnoreCase(k2StatusResponse)) {
                failedReasons.add("Cannot update k2 payment status");
            }
        } catch (FeignException e) {
            logger.info("hp-exp-service Update Payment Flag service unavailable: {}", e);
            failedReasons.add("hp-exp-service down for update payment flag");
        } catch (JsonProcessingException e) {
            logger.info("hp-exp-service Update Payment Flag service unknown error with json: {}", e);
            failedReasons.add("Unexpected error");
        }

        paramsActLog.put(FAIL_REASON, String.join(",", failedReasons));
        performAutoLoanActivityLog(paramsActLog, headers, deepLinkRequest, draftCache);
    }

    private void performAutoLoanActivityLog(Map<String, String> paramsActLog, HttpHeaders headers, DeepLinkRequestInCache deepLinkRequest, CommonPaymentDraftCache draftCache) {
        String correlationId = paramsActLog.get(HEADER_CORRELATION_ID);
        ActivityAutoLoanBillPayServiceEvent activityAutoLoanServiceRequest = new ActivityAutoLoanBillPayServiceEvent(headers, paramsActLog, deepLinkRequest, draftCache);
        activityAutoLoanServiceRequest.setCorrelationId(correlationId);
        if (!StringUtils.isEmpty(paramsActLog.get(FAIL_REASON))) {
            activityAutoLoanServiceRequest.setActivityStatus(ACTIVITY_FAILURE);
            activityAutoLoanServiceRequest.setFailReason(paramsActLog.get(FAIL_REASON));
        } else {
            activityAutoLoanServiceRequest.setActivityStatus(ACTIVITY_SUCCESS);
        }
        logEventPublisherService.saveActivityLog(activityAutoLoanServiceRequest);
    }
}
