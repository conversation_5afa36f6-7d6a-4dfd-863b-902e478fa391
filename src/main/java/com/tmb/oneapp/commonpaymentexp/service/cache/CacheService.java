package com.tmb.oneapp.commonpaymentexp.service.cache;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;

public interface CacheService {
    void set(String key, Object value, int ttlSecond) throws JsonProcessingException;

    void set(String key, Object value) throws JsonProcessingException;

    void set(String key, String hashKey, Object value, int ttlSecond) throws JsonProcessingException;

    void set(String key, String hashKey, Object value) throws JsonProcessingException;

    String get(String key) throws TMBCommonException;

    <T> T get(String key, Class<T> typeValue) throws TMBCommonException;

    <T> T get(String key, String hashKey, Class<T> typeValue) throws TMBCommonException;

    boolean delete(String key);

    boolean delete(String key, String hashKey);

    boolean putIfAbsent(String key, String hashKey, Object value);
}
