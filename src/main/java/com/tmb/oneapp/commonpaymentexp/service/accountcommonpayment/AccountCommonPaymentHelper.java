package com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.client.CustomerAccountBizClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.AccountSaving;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.LoanAccount;
import com.tmb.oneapp.commonpaymentexp.model.billpayaccount.AccountBalanceRequest;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import feign.FeignException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;


@Component
public class AccountCommonPaymentHelper {
    public static final TMBLogger<AccountCommonPaymentHelper> logger = new TMBLogger<>(AccountCommonPaymentHelper.class);
    public static final String RELATION_SHIP_CODE_PRIIND = "PRIIND";
    public static final String ACCOUNT_STATUS_INACTIVE = "INACTIVE";
    public static final String ACCOUNT_STATUS_ACTIVE = "ACTIVE";
    public static final String ACCOUNT_STATUS_CLOSE = "CLOSE";
    public static final String ACCOUNT_STATUS_DORMANT = "DORMANT";
    protected static final Predicate<DepositAccount> IS_PRIIND = e -> StringUtils.equalsIgnoreCase(RELATION_SHIP_CODE_PRIIND, e.getRelationshipCode());
    protected static final Predicate<DepositAccount> IS_ALLOW_FROM_FOR_BILL_PAY_TOP_UP_E_PAYMENT_ONE = e -> StringUtils.equalsIgnoreCase("1", e.getAllowFromForBillPayTopUpEpayment());
    protected static final Predicate<DepositAccount> IS_ALLOW_TO_D_STATEMENT_DIRECT_DEBIT_FEE = e -> StringUtils.equalsIgnoreCase("1", e.getAllowToDstatementDirectdebitFee());
    protected static final Predicate<DepositAccount> IS_ALLOW_DIRECT_DEBIT_ISSUE_DEBIT_CARD_FEE = e -> StringUtils.equalsIgnoreCase("1", e.getAllowDirectDebitIssuedebitcardFee());
    protected static final Predicate<DepositAccount> IS_ACCOUNT_STATUS_INACTIVE_OR_ACTIVE = e -> (StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_INACTIVE, e.getAccountStatus())) || StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_ACTIVE, e.getAccountStatus());

    public static final Predicate<DepositAccount> isNotViewOnlyAccount = d -> !d.isViewOnlyFlag();

    private final CustomerAccountBizClient customerAccountBizClient;
    private final FeignClientHelper feignClientHelper;

    public AccountCommonPaymentHelper(CustomerAccountBizClient customerAccountBizClient, FeignClientHelper feignClientHelper) {
        this.customerAccountBizClient = customerAccountBizClient;
        this.feignClientHelper = feignClientHelper;
    }

    public List<DepositAccount> fetchDepositAccountList(String correlationId, String crmId) throws TMBCommonException {
        AccountSaving accountSaving = feignClientHelper.executeRequest(() -> customerAccountBizClient.getAccountList(correlationId, crmId));
        if (CollectionUtils.isEmpty(accountSaving.getDepositAccountLists())) {
            logger.error("fetchDepositAccountList Deposit account list is empty after filter by type condition. [ crmId = {}]", crmId);
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR);
        }
        return accountSaving.getDepositAccountLists();

    }

    public List<DepositAccount> setAvailableBalance(List<DepositAccount> depositAccountList, String correlationId, String crmId) throws TMBCommonException {
        if (CollectionUtils.isEmpty(depositAccountList)) {
            logger.error("setAvailableBalance Deposit account list is empty after filter by type condition. [ crmId = {}]", crmId);
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR);
        }

        setAvailableBalanceForEligibleAccount(depositAccountList, correlationId, crmId);

        depositAccountList = filterOutCloseAndDormantAccountAfterSyncStatus(depositAccountList);

        if (CollectionUtils.isEmpty(depositAccountList)) {
            logger.error("Deposit account list is empty after sync with CoreBank. [ crmId = {}]", crmId);
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR);
        }

        return depositAccountList;
    }

    private List<DepositAccount> filterOutCloseAndDormantAccountAfterSyncStatus(List<DepositAccount> depositAccounts) {
        Predicate<DepositAccount> isNotDormantStatus = d -> !StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_DORMANT, d.getAccountStatus());
        Predicate<DepositAccount> isNotCloseStatus = d -> !StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_CLOSE, d.getAccountStatus());

        return depositAccounts.stream()
                .filter(isNotCloseStatus.and(isNotDormantStatus))
                .toList();
    }

    private void setAvailableBalanceForEligibleAccount(List<DepositAccount> depositAccountLists, String correlationId, String crmId) throws TMBCommonException {
        HttpHeaders requestBalanceHeaders = new HttpHeaders();
        requestBalanceHeaders.set(HEADER_CRM_ID, crmId);
        requestBalanceHeaders.set(HEADER_CORRELATION_ID, correlationId);

        for (DepositAccount d : depositAccountLists) {
            boolean isShouldNotGetBalance = d.getAvailableBalance() != null || d.isHideAccountFlag();
            if (isShouldNotGetBalance) {
                logger.info("Account already have available balance. [accountNumber = {}]", d.getAccountNumber());
                break;
            }

            DepositAccount accountBalance = this.getDepositAccountWithBalance(d.getAccountNumber(), requestBalanceHeaders);

            d.setAvailableBalance(accountBalance.getAvailableBalance());
            d.setLinkedAccount(accountBalance.getLinkedAccount());
            d.setAccountStatus(accountBalance.getAccountStatus());
            d.setAccountName(accountBalance.getAccountName());
            String accountStatusFromCBS = accountBalance.getAccountStatus();
            if (isActiveOrInactiveAccount(accountStatusFromCBS)) {
                break;
            }
        }
    }

    private boolean isActiveOrInactiveAccount(String accountStatus) {
        return StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_ACTIVE, accountStatus) || StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_INACTIVE, accountStatus);
    }

    public DepositAccount getDepositAccountWithBalance(final String accountNumber, HttpHeaders headers) throws TMBCommonException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String accountType = TMBUtils.getAccountType(accountNumber);

        AccountBalanceRequest accountBalanceRequest = new AccountBalanceRequest()
                .setAccountNumber(accountNumber)
                .setAccountType(accountType)
                .setFinancialId(null);
        try {
            TmbOneServiceResponse<List<DepositAccount>> responseEntity = Objects.requireNonNull(customerAccountBizClient.getAccountBalance(crmId, correlationId, List.of(accountBalanceRequest)).getBody());

            boolean isCloseAccount = null == responseEntity.getData() || responseEntity.getData().isEmpty();
            if (isCloseAccount) {
                logger.info("Got empty or null from customerAccountBizClient.getAccountBalance is mean the account is closed or cannot found in CBS. [crmId = {}, request = {}]", crmId, accountBalanceRequest);
                return buildCloseAccount(accountNumber);
            }

            DepositAccount response = responseEntity.getData().get(0);
            logger.info("Get customerAccountBizClient.getAccountBalance : Success. [response.availableBalance = {}, response.accountStatus = {}, response.linkedAccount = {}]", response.getAvailableBalance(), response.getAccountStatus(), response.getLinkedAccount());
            return response;

        } catch (FeignException e) {
            logger.error("Error process customerAccountBizClient.getAccountBalance. [correlationId = {}, request = {}]", correlationId, accountBalanceRequest, e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Error when fetch to get account balance");
        }
    }

    private DepositAccount buildCloseAccount(String accountNumber) {
        return new DepositAccount()
                .setAccountNumber(accountNumber)
                .setAccountStatus(ACCOUNT_STATUS_CLOSE)
                .setAvailableBalance(new BigDecimal("0.00"))
                .setLinkedAccount(null);
    }

    public List<LoanAccount> fetchHpAccountList(String correlationId, String crmId) throws TMBCommonException {
        AccountSaving accountSaving = feignClientHelper.executeRequest(() -> customerAccountBizClient.getAccountList(correlationId, crmId));
        return accountSaving.getHpAccounts();
    }

}
