package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_ISSUE_DEBIT_CARD;

@Service
@RequiredArgsConstructor
public class IssueDebitCardConfirmServiceProcessor implements ConfirmationCommonPaymentProcessor {
    private static final TMBLogger<IssueDebitCardConfirmServiceProcessor> logger = new TMBLogger<>(IssueDebitCardConfirmServiceProcessor.class);

    @Override
    public String getProcessorType() {
        return COMMON_PAYMENT_TRANSACTION_TYPE_ISSUE_DEBIT_CARD;
    }

    @Override
    public ConfirmationCommonPaymentResponse executeConfirm(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache) throws TMBCommonException {
        return new ConfirmationCommonPaymentResponse();
    }
}
