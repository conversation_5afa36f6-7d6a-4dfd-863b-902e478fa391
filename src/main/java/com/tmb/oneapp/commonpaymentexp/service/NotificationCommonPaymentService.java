package com.tmb.oneapp.commonpaymentexp.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.request.notification.EmailChannel;
import com.tmb.common.model.request.notification.NotificationRecord;
import com.tmb.common.model.request.notification.NotificationRequest;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.model.notification.ENotificationSettingResponse;
import com.tmb.oneapp.commonpaymentexp.model.notification.Notification;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.NotificationService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class NotificationCommonPaymentService {
    private static final TMBLogger<NotificationCommonPaymentService> logger = new TMBLogger<>(NotificationCommonPaymentService.class);

    private final CustomerService customerService;
    private final NotificationService notificationService;

    @Value("${notification-service.e-noti.default.channel.en}")
    private String defaultChannelNameEN;

    @Value("${notification-service.e-noti.default.channel.th}")
    private String defaultChannelNameTH;

    public void sendENotification(Notification notificationCommonPayment) {
        boolean sendNotificationStatus = getENotificationStatus(notificationCommonPayment.getXCorrelationId(), notificationCommonPayment.getCrmId(), notificationCommonPayment.getTemplateName());
        logger.info("sendENotification sendNotificationStatus : {}", sendNotificationStatus);
        if (!sendNotificationStatus) {
            return;
        }
        try {
            CustomerKYCResponse customerKYCResponse = customerService.getCustomerKYC(notificationCommonPayment.getXCorrelationId(), notificationCommonPayment.getCrmId());
            notificationCommonPayment.setCustomerName(
                    customerKYCResponse.getCustomerFirstNameEn(),
                    customerKYCResponse.getCustomerLastNameEn(),
                    customerKYCResponse.getCustomerFirstNameTh(),
                    customerKYCResponse.getCustomerLastNameTh()
            );
            notificationCommonPayment.setChannelName(defaultChannelNameEN, defaultChannelNameTH);

            NotificationRequest notificationRequest = setUpNotificationRequest(notificationCommonPayment, customerKYCResponse.getEmail());

            notificationService.sendMessage(notificationCommonPayment.getXCorrelationId(), notificationRequest);

        } catch (Exception e) {
            logger.error("got error while sending e notification [crmId: {}, notificationRequest: {}]", notificationCommonPayment.getCrmId(), notificationCommonPayment);
        }
    }

    private boolean getENotificationStatus(String correlationId, String crmId, String templateName) {
        boolean isSkipCheckNotificationSetting = getListTemplateSkipCheckNotificationSetting().contains(templateName);
        if (isSkipCheckNotificationSetting) {
            return true;
        }
        ENotificationSettingResponse eNotificationSettingResponse = customerService.getENotificationSetting(crmId, correlationId);
        boolean sendNotificationStatus = Optional.ofNullable(eNotificationSettingResponse)
                .map(ENotificationSettingResponse::isTransactionNotification)
                .orElse(false);

        logger.info("getENotificationSetting, [sendNotificationStatus = {}]", sendNotificationStatus);
        return sendNotificationStatus;
    }

    private List<String> getListTemplateSkipCheckNotificationSetting() {
        return new ArrayList<>(
                List.of(
                        CommonPaymentExpConstant.SCHEDULE_TRANSFER_TEMPLATE_VALUE,
                        CommonPaymentExpConstant.SCHEDULE_TOPUP_TEMPLATE_VALUE
                )
        );
    }

    private NotificationRequest setUpNotificationRequest(Notification notificationPayment, String email) {
        ObjectMapper mapper = new ObjectMapper();
        Map<String, Object> params = mapper.convertValue(notificationPayment, Map.class);

        EmailChannel emailChannel = new EmailChannel();
        emailChannel.setEmailEndpoint(email);
        emailChannel.setEmailSearch(false);

        NotificationRecord notificationRecord = new NotificationRecord();
        notificationRecord.setParams(params);
        notificationRecord.setCrmId(notificationPayment.getCrmId());
        notificationRecord.setLanguage(CommonPaymentExpConstant.LOCALE_TH);
        notificationRecord.setEmail(emailChannel);

        NotificationRequest notificationRequest = new NotificationRequest();
        notificationRequest.setRecords(List.of(notificationRecord));
        return notificationRequest;
    }

}
