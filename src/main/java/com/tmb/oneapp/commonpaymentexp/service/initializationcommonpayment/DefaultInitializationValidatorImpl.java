package com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.DeepLinkRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitialPrepareData;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.validator.CommonPaymentConfigValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CALL_FROM_AUTO_LOAN;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNullOrDefault;
import static com.tmb.oneapp.commonpaymentexp.utils.ProcessorSelectorUtils.isTransactionTypeBillPay;

@Primary
@Component
public class DefaultInitializationValidatorImpl implements InitializationValidator {
    private static final TMBLogger<DefaultInitializationValidatorImpl> logger = new TMBLogger<>(DefaultInitializationValidatorImpl.class);
    @Value("${common.payment.initial.enable.check.toggle.allow.common.payment:false}")
    private boolean enableCheckToggleAllowCommonPaymentFromProperty;

    @Override
    public void validateData(InitializationCommonPaymentRequest initializationCommonPaymentRequest, HttpHeaders headers, InitialPrepareData prepareData) throws TMBCommonException {
        final String transactionType = initializationCommonPaymentRequest.getPaymentInformation().getTransactionType();
        final MasterBillerResponse masterBiller = prepareData.getMasterBillerResponse();
        final CommonPaymentConfig commonPaymentConfig = prepareData.getCommonPaymentConfig();
        final DeepLinkRequest deepLinkRequest = prepareData.getDeepLinkRequest();
        final boolean isTransactionFromDeeplink = StringUtils.isNotBlank(initializationCommonPaymentRequest.getPaymentInformation().getDeepLinkTransactionId());

        if (isTransactionTypeBillPay(transactionType)) {
            if (enableCheckToggleAllowCommonPaymentFromProperty && isNotAllowToCallCommonPaymentFromMasterBiller(masterBiller)) {
                logger.error("Master biller not allow call common payment. [ compCode = {}, enableCheckToggleAllowCommonPaymentFromProperty = true]", initializationCommonPaymentRequest.getPaymentInformation().getCompCode());
                throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.NOT_ALLOW_COMMON_PAYMENT_ERROR);
            }

            if (isTransactionFromDeeplink && deepLinkRequest != null && isCallFromAutoLoan(deepLinkRequest)) {
                validateReferenceIdWithDeepLinkRequestForAutoLoan(deepLinkRequest, initializationCommonPaymentRequest);
            }
        }

        this.validateCommonPaymentConfigExpired(commonPaymentConfig);
    }

    private void validateReferenceIdWithDeepLinkRequestForAutoLoan(DeepLinkRequest deepLinkRequest, InitializationCommonPaymentRequest initializationCommonPaymentRequest) throws TMBCommonException {
        if (deepLinkRequest.getRef1() == null || deepLinkRequest.getRef2() == null) {
            logger.error("Deep link request is missing ref1 = {} or ref2 = {}", deepLinkRequest.getRef1(), deepLinkRequest.getRef2());
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.MISSING_REQUIRED_FIELD_ERROR);
        }

        boolean isRef1NotMatch = !StringUtils.equals(StringUtils.trim(deepLinkRequest.getRef1()), StringUtils.trim(initializationCommonPaymentRequest.getPaymentInformation().getProductDetail().getProductRef1()));
        if (isRef1NotMatch) {
            logger.error("Deep link request ref1 is not match with request ref1 [deeplink ref1 = {}, request ref1 = {}]", deepLinkRequest.getRef1(), initializationCommonPaymentRequest.getPaymentInformation().getProductDetail().getProductRef1());
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.INCORRECT_AUTO_LOAN_REF_1);
        }
        boolean isRef2NotMatch = !StringUtils.equals(StringUtils.trim(deepLinkRequest.getRef2()), StringUtils.trim(initializationCommonPaymentRequest.getPaymentInformation().getProductDetail().getProductRef2()));
        if (isRef2NotMatch) {
            logger.error("Deep link request ref2 is not match with request ref2 [deeplink ref2 = {}, request ref2 = {}]", deepLinkRequest.getRef2(), initializationCommonPaymentRequest.getPaymentInformation().getProductDetail().getProductRef2());
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.INCORRECT_AUTO_LOAN_REF_2);
        }
    }

    private void validateCommonPaymentConfigExpired(CommonPaymentConfig commonPaymentConfig) throws TMBCommonException {
        CommonPaymentConfigValidator.validateNotExpired(commonPaymentConfig);
    }

    private static boolean isNotAllowToCallCommonPaymentFromMasterBiller(MasterBillerResponse masterBiller) {
        return !getSafeNullOrDefault(() -> masterBiller.getBillerInfo().isAllowCallCommonPayment(), false);
    }

    private static boolean isCallFromAutoLoan(DeepLinkRequest deepLinkRequest) {
        String callFrom = Optional.ofNullable(deepLinkRequest).map(DeepLinkRequest::getCallFrom).orElse(null);
        return StringUtils.equalsIgnoreCase(BILLER_CALL_FROM_AUTO_LOAN, callFrom);
    }
}
