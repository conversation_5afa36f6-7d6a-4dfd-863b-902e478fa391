package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.CustomerExpServiceClient;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

@Service
@RequiredArgsConstructor
public class CustomerExpService {
    private static final TMBLogger<CustomerExpService> logger = new TMBLogger<>(CustomerExpService.class);
    private final CustomerExpServiceClient customerExpServiceClient;

    public TmbServiceResponse<CreditCardResponse> getCustomerCreditCards(String correlationId, String crmId) {
        logger.debug("Getting credit card accounts for customer: {}", crmId);
        ResponseEntity<TmbServiceResponse<CreditCardResponse>> response =
                customerExpServiceClient.getAccountsCreditCard(correlationId, crmId);
        return response.getBody();
    }

    public CompletableFuture<TmbServiceResponse<CreditCardResponse>> getCustomerCreditCardsAsync(
            String correlationId, String crmId) {
        logger.debug("Getting credit card accounts asynchronously for customer: {}", crmId);
        return CompletableFuture.supplyAsync(() ->
                customerExpServiceClient.getAccountsCreditCard(correlationId, crmId).getBody());
    }

    public void deleteCreditCardCache(
            String correlationId, String acceptLanguage, String appVersion, String crmId) {
        logger.debug("Deleting credit card cache for customer: {}", crmId);
        try {
            customerExpServiceClient.deleteCreditCardCache(correlationId, acceptLanguage, appVersion, crmId);
        } catch (Exception e) {
            logger.error("Error while deleting credit card cache: {}", e.getMessage(), e);
        }
    }
}
