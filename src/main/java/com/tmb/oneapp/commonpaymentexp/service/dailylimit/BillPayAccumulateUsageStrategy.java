package com.tmb.oneapp.commonpaymentexp.service.dailylimit;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.AccumulateUsageRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;

import java.math.BigDecimal;
import java.util.Optional;

public class BillPayAccumulateUsageStrategy implements AccumulateUsageStrategy {
    @Override
    public AccumulateUsageRequest createRequest(CommonPaymentDraftCache draftCache, CustomerCrmProfile profile) {
        AccumulateUsageRequest request = new AccumulateUsageRequest();
        BigDecimal requestAmount = Optional.ofNullable(draftCache)
                .map(CommonPaymentDraftCache::getValidateRequest)
                .map(ValidationCommonPaymentRequest::getDeposit)
                .map(DepositValidationCommonPaymentRequest::getAmount)
                .orElse(BigDecimal.ZERO);

        request.setBillPayAccumulateUsageAmount(
                Optional.ofNullable(profile.getBillpayAccuUsgAmt())
                        .orElse(BigDecimal.ZERO)
                        .add(requestAmount)
        );

        return request;
    }
}
