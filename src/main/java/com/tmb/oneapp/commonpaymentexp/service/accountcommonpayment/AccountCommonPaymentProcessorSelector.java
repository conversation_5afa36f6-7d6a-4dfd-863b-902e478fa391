package com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class AccountCommonPaymentProcessorSelector {

    private static final TMBLogger<AccountCommonPaymentProcessorSelector> logger = new TMBLogger<>(AccountCommonPaymentProcessorSelector.class);
    private final Map<String, AccountCommonPaymentProcessor<List<DepositAccount>>> processorMap = new HashMap<>();

    @Autowired
    public AccountCommonPaymentProcessorSelector(List<AccountCommonPaymentProcessor<List<DepositAccount>>> processors) {
        for (AccountCommonPaymentProcessor<List<DepositAccount>> processor : processors) {
            processorMap.put(processor.getKey(), processor);
        }
    }

    /**
     * Get processor service by filterAccountType match with processor key.
     *
     * @param filterAccountType the filter account type from the request.
     * @return Processor corresponding to the filterAccountType, such as a specific AccountCommonPaymentProcessor.
     */
    public AccountCommonPaymentProcessor<List<DepositAccount>> getProcessor(String filterAccountType) {
        return processorMap.get(filterAccountType.toLowerCase());
    }

    /**
     * This method validates that the filterAccountType matches the key of the processor.
     *
     * @param filterAccountType filterAccountType from Request.
     * @throws TMBCommonException if the filterAccountType does not match the key of the processor, a MISSING_REQUIRED_FIELD_ERROR is thrown.
     */
    public void validateFilterAccountTypeKey(String filterAccountType) throws TMBCommonException {
        boolean isTransactionTypeNotMatch = !processorMap.containsKey(filterAccountType.toLowerCase());
        if (isTransactionTypeNotMatch) {
            logger.error("Error filter_account_type incorrect. [ filter_account_type = {}, [allow_filter_account_type = {}]]", filterAccountType, getAllKeys());
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.MISSING_REQUIRED_FIELD_ERROR, "Please verify request filter_account_type");
        }
    }

    private Set<String> getAllKeys() {
        return processorMap.keySet();
    }
}
