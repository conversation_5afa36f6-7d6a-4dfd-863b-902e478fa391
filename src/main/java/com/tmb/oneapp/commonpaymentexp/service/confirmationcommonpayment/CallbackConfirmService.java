package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PartnerPaymentStatusCallback;
import com.tmb.oneapp.commonpaymentexp.service.PaymentStatusPublisherService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CallbackConfirmService {
    private static final TMBLogger<CallbackConfirmService> logger = new TMBLogger<>(CallbackConfirmService.class);
    private static final String PB_ENTRY_ID = "pb";
    private final PaymentStatusPublisherService paymentStatusPublisherService;

    public void callback(CommonPaymentDraftCache draftCache, PartnerPaymentStatusCallback partnerCallback) {
        if (partnerCallback == null) {
            logger.warn("PartnerPaymentStatusCallback is null, skipping publish.");
            return;
        }

        final String partnerName = draftCache.getPartnerName();
        final String entryId = draftCache.getPaymentInformation().getEntryId();

        if (StringUtils.equalsIgnoreCase(PB_ENTRY_ID, entryId)) {
            logger.debug("bypass kafka cause entry id = pb");
            return;
        }

        final boolean isFromPartner = StringUtils.isNotBlank(partnerName);

        final String identifier = isFromPartner ? partnerName : entryId;

        if (isFromPartner) {
            logger.debug("Publishing partner payment status callback to partner name: {}", partnerName);
            paymentStatusPublisherService.publishPartner(partnerCallback, identifier);
        } else {
            logger.debug("Publishing internal payment status callback");
            paymentStatusPublisherService.publishInternal(partnerCallback, identifier);
        }
    }
}
