package com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Predicate;

import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_ISSUE_DEBIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper.IS_ACCOUNT_STATUS_INACTIVE_OR_ACTIVE;
import static com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper.IS_ALLOW_DIRECT_DEBIT_ISSUE_DEBIT_CARD_FEE;
import static com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper.IS_PRIIND;
import static com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper.isNotViewOnlyAccount;

@Service
public class IssueDebitCardAccountCommonPaymentService implements AccountCommonPaymentProcessor<List<DepositAccount>> {
    public static final TMBLogger<IssueDebitCardAccountCommonPaymentService> logger = new TMBLogger<>(IssueDebitCardAccountCommonPaymentService.class);
    private static final Predicate<DepositAccount> FILTER_FOR_ISSUE_DEBIT_CARD = IS_PRIIND.and(IS_ALLOW_DIRECT_DEBIT_ISSUE_DEBIT_CARD_FEE).and(IS_ACCOUNT_STATUS_INACTIVE_OR_ACTIVE).and(isNotViewOnlyAccount);
    private final AccountCommonPaymentHelper accountCommonPaymentHelper;

    public IssueDebitCardAccountCommonPaymentService(AccountCommonPaymentHelper accountCommonPaymentHelper) {
        this.accountCommonPaymentHelper = accountCommonPaymentHelper;
    }

    @Override
    public String getKey() {
        return COMMON_PAYMENT_TRANSACTION_TYPE_ISSUE_DEBIT_CARD;
    }

    @Override
    public List<DepositAccount> getAccountList(String correlationId, String crmId) throws TMBCommonException {
        var depositAccountList = accountCommonPaymentHelper.fetchDepositAccountList(correlationId, crmId);
        depositAccountList = depositAccountList.stream().filter(FILTER_FOR_ISSUE_DEBIT_CARD).toList();
        depositAccountList = accountCommonPaymentHelper.setAvailableBalance(depositAccountList, correlationId, crmId);
        return depositAccountList;
    }
}
