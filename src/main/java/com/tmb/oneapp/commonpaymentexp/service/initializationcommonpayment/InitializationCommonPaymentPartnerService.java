package com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentResponse;

import org.springframework.beans.factory.annotation.Value;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Base64;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.APP;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.CHANNEL;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_X_IN_APP_WEBVIEW;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_X_PARTNER_NAME;

@Service
public class InitializationCommonPaymentPartnerService {
    private static final TMBLogger<InitializationCommonPaymentPartnerService> logger = new TMBLogger<>(InitializationCommonPaymentPartnerService.class);
    private final InitializationCommonPaymentService initializationCommonPaymentService;
    private final String deeplinkManagerUrl;
    private final String universalLinkUrl;

    public InitializationCommonPaymentPartnerService(
            InitializationServiceFactory factory,
            PartnerInitializationValidatorImpl partnerInitializationValidator,
            @Value("${common.payment.deeplink.manager.url}") String deeplinkManagerUrl,
            @Value("${common.payment.universal.link.url}") String universalLinkUrl
    ) {
        this.initializationCommonPaymentService = factory.create(partnerInitializationValidator);
        this.deeplinkManagerUrl = deeplinkManagerUrl;
        this.universalLinkUrl = universalLinkUrl;
    }

    public InitializationCommonPaymentResponse initialCommonPayment(InitializationCommonPaymentRequest initializationCommonPaymentRequest, HttpHeaders headers) throws TMBCommonException {
        InitializationCommonPaymentResponse response = initializationCommonPaymentService.initialCommonPayment(initializationCommonPaymentRequest, headers);
        
        if (shouldProcessDeeplink(response)) {
            String originalDeeplink = response.getDeeplinkUrl();
            boolean isInAppWebview = Boolean.parseBoolean(headers.getFirst(HEADER_X_IN_APP_WEBVIEW));
            String partnerName = headers.getFirst(HEADER_X_PARTNER_NAME);
            
            String processedDeeplink = isInAppWebview 
                ? createWrappedDeeplink(originalDeeplink, partnerName)
                : createUniversalLink(originalDeeplink);
            
            response.setDeeplinkUrl(processedDeeplink);
            logDeeplinkProcessing(isInAppWebview, originalDeeplink, processedDeeplink);
        }
        
        return response;
    }
    
    private boolean shouldProcessDeeplink(InitializationCommonPaymentResponse response) {
        return !ObjectUtils.isEmpty(response) && StringUtils.hasText(response.getDeeplinkUrl());
    }
    
    private String createWrappedDeeplink(String originalDeeplink, String partnerName) {
        String channelValue = partnerName != null ? partnerName : "";
        String deeplinkWithChannel = UriComponentsBuilder.fromUriString(originalDeeplink)
                .queryParam(CHANNEL, channelValue)
                .toUriString();
        String base64Deeplink = Base64.getEncoder().encodeToString(deeplinkWithChannel.getBytes());
        return UriComponentsBuilder.fromUriString(deeplinkManagerUrl)
                .queryParam(APP, base64Deeplink)
                .toUriString();
    }
    
    private String createUniversalLink(String originalDeeplink) {
        String transactionId = extractTransactionIdFromDeeplink(originalDeeplink);
        return UriComponentsBuilder.fromUriString(universalLinkUrl)
                .queryParam("transaction_id", transactionId)
                .toUriString();
    }
    
    private void logDeeplinkProcessing(boolean isInAppWebview, String originalDeeplink, String processedDeeplink) {
        if (isInAppWebview) {
            logger.debug("Added channel parameter and wrapped deeplink for partner: {} -> {}", originalDeeplink, processedDeeplink);
        } else {
            logger.debug("Set universal link for non-webview: {} -> {}", originalDeeplink, processedDeeplink);
        }
    }
    
    private String extractTransactionIdFromDeeplink(String deeplinkUrl) {
        try {
            String transactionId = UriComponentsBuilder.fromUriString(deeplinkUrl)
                    .build()
                    .getQueryParams()
                    .getFirst("transaction_id");
            return transactionId != null ? transactionId : "";
        } catch (Exception e) {
            logger.warn("Failed to extract transaction_id from deeplink: {}", deeplinkUrl, e);
            return "";
        }
    }

}
