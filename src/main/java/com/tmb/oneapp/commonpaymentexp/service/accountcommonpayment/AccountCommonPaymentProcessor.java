package com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;

import java.util.List;

public interface AccountCommonPaymentProcessor<T extends List<DepositAccount>> {

    String getKey();

    T getAccountList(String correlationId, String crmId) throws TMBCommonException;
}
