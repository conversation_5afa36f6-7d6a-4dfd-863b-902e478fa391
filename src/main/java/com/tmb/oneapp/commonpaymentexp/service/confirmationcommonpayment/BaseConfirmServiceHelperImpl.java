package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableRunnable;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BaseConfirmDataAfterConfirmExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.CallbackInitialRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PaymentStatus;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PartnerPaymentStatusCallback;
import com.tmb.oneapp.commonpaymentexp.service.CommonPaymentPartnerIntegrationService;
import com.tmb.common.util.TMBUtils;
import java.util.Map;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationCommonPayment;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationMapper;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_IS_USED;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;


@Component
@RequiredArgsConstructor
public class BaseConfirmServiceHelperImpl implements BaseConfirmServiceHelper {
    private static final TMBLogger<BaseConfirmServiceHelperImpl> logger = new TMBLogger<>(BaseConfirmServiceHelperImpl.class);
    private final CacheService cacheService;
    private final CustomerService customerService;
    private final CallbackConfirmService callbackConfirmService;
    private final AsyncHelper asyncHelper;
    private final CustomersTransactionService customersTransactionService;
    private final DailyLimitService dailyLimitService;
    private final NotificationCommonPaymentService notificationCommonPaymentService;
    private final CommonPaymentPartnerIntegrationService commonPaymentPartnerIntegrationService;

    @Override
    public void baseClearDraftDataCache(String transactionId) {
        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + transactionId;
        cacheService.delete(cacheKey);
    }

    @Override
    public void baseUpdatePinFreeCountWithCondition(String crmId, String correlationId, CustomerCrmProfile customerCrmProfile, boolean isRequireCommonAuth) {
        boolean isNotRequirePin = !isRequireCommonAuth;
        boolean shouldUpdatePinFreeCount = isNotRequirePin && StringUtils.equals(customerCrmProfile.getPinFreeSeetingFlag(), "Y");
        if (shouldUpdatePinFreeCount) {
            this.executeMethodSafelyVoid(() -> customerService.updatePinFreeCount(crmId, customerCrmProfile.getPinFreeTxnCount() + 1, correlationId));
        }

    }

    @Override
    public void baseExecuteCallbackIfConfiguredAsync(CommonPaymentDraftCache draftCache, String transactionTime, String transactionId) {
        logger.debug("process callback to 3rd party api");
        PaymentStatus paymentStatus = createPaymentStatus(draftCache, transactionTime, transactionId);
        
        PartnerPaymentStatusCallback partnerCallback = createPartnerCallback(draftCache, paymentStatus);
        executeCallbackIfPresent(draftCache, partnerCallback);
    }

    @Override
    public TMBCommonException baseHandleException(ConfirmationCommonPaymentRequest request, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String transactionId = Optional.ofNullable(request).map(ConfirmationCommonPaymentRequest::getTransactionId).orElse(null);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> this.clearStampTransactionUsed(transactionId));

        logger.error("ERROR ConfirmationService. : {}", e.getMessage(), e);
        if (e instanceof TMBCommonException ex) {
            throw ex;
        } else if (e instanceof TMBCommonExceptionWithResponse ex) {
            throw ex;
        } else {
            logger.error("ERROR Unhandled exception. Please verify the exception. : {}", e.getMessage(), e);
            return CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
        }
    }

    @Override
    public BasePrepareDataConfirm getBasePrepareDataConfirm(HttpHeaders headers, final String transactionTimeFromFinLog) throws TMBCommonException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        CustomerCrmProfile customerCrmProfile = customerService.getCustomerCrmProfile(correlationId, crmId);

        return new BasePrepareDataConfirm().setTransactionTime(transactionTimeFromFinLog).setCustomerCrmProfile(customerCrmProfile);
    }

    @Override
    public @Nullable BaseConfirmDataAfterConfirmExternal baseConfirmDataAfterConfirmExternal(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) {
        final String transactionId = request.getTransactionId();
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final boolean isRequireCommonAuth = draftCache.getValidateDraftCache().isRequireCommonAuthen();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final String transactionTime = prepareData.getTransactionTime();
        final BigDecimal amountFromValidateRequest = draftCache.getValidateRequest().getAmount();
        final String ePayCode = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getEpayCode();

        this.baseClearDraftDataCache(transactionId);
        customersTransactionService.clearDepositCache(correlationId, crmId);

        this.baseUpdatePinFreeCountWithCondition(crmId, correlationId, customerCrmProfile, isRequireCommonAuth);
        dailyLimitService.updateAccumulateUsage(draftCache, customerCrmProfile, crmId, correlationId);
        this.baseExecuteCallbackIfConfiguredAsync(draftCache, transactionTime, transactionId);

        NotificationCommonPayment notificationRequest = NotificationMapper.INSTANCE.toCommonPaymentNotification(draftCache, ePayCode, crmId, correlationId, transactionTime);
        asyncHelper.executeMethodAsyncSafelyVoid(() -> notificationCommonPaymentService.sendENotification(notificationRequest));

        return null;
    }

    private void clearStampTransactionUsed(String transactionId) {
        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + transactionId;
        cacheService.delete(cacheKey, COMMON_PAYMENT_HASH_KEY_IS_USED);
    }

    private PaymentStatus createPaymentStatus(CommonPaymentDraftCache draftCache, String transactionTime, String transactionId) {
        return PaymentStatus.builder()
                .transactionId(transactionId)
                .statusCode(ResponseCode.SUCCESS.getCode())
                .codeDescription(ResponseCode.SUCCESS.getMessage())
                .timestamp(transactionTime)
                .build();
    }

    private PartnerPaymentStatusCallback createPartnerCallback(CommonPaymentDraftCache draftCache, PaymentStatus paymentStatus) {
        return Optional.ofNullable(draftCache.getPaymentInformation())
                .map(PaymentInformation::getCallback)
                .filter(CallbackInitialRequest::isEnableCallbackFlag)
                .map(callbackInitialRequest -> createPartnerCallbackFromRequest(callbackInitialRequest, paymentStatus, draftCache.getPartnerName()))
                .orElse(null);
    }

    private PartnerPaymentStatusCallback createPartnerCallbackFromRequest(CallbackInitialRequest callbackRequest, PaymentStatus paymentStatus, String partnerName) {
        try {
            Map<String, Object> paymentStatusMap = TMBUtils.getObjectMapper().convertValue(paymentStatus, Map.class);
            String signedPayload = commonPaymentPartnerIntegrationService.signData(partnerName, paymentStatusMap);

            PartnerPaymentStatusCallback.DataPayload dataPayload = PartnerPaymentStatusCallback.DataPayload.builder()
                    .payload(signedPayload)
                    .build();

            return PartnerPaymentStatusCallback.builder()
                    .url(callbackRequest.getCallbackUrl())
                    .data(dataPayload)
                    .build();
        } catch (Exception e) {
            logger.error("Error creating partner payment status callback", e);
            return null;
        }
    }

    private void executeCallbackIfPresent(CommonPaymentDraftCache draftCache, PartnerPaymentStatusCallback partnerCallback) {
        boolean hasValidCallback = partnerCallback != null;

        if (hasValidCallback) {
            callbackConfirmService.callback(draftCache, partnerCallback);
        }
    }

    private void executeMethodSafelyVoid(ThrowableRunnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            logger.error("executeMethodSafelyVoid got UnhandledException.", e);
        }
    }
}
