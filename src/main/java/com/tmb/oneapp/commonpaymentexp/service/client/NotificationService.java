package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.request.notification.NotificationRequest;
import com.tmb.oneapp.commonpaymentexp.client.NotificationServiceClient;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class NotificationService {
    private static final TMBLogger<NotificationService> logger = new TMBLogger<>(NotificationService.class);

    private final NotificationServiceClient notificationServiceClient;
    private final FeignClientHelper feignClientHelper;

    public void sendMessage(String correlationId, NotificationRequest notificationRequest) {
        logger.info(">>> call send message from notification service <<<");
        logger.debug(">>> correlation id : {} notification request : {} <<<", correlationId, notificationRequest);
        feignClientHelper.executeRequestSafely(() -> notificationServiceClient.sendMessage(correlationId, notificationRequest));
    }

}
