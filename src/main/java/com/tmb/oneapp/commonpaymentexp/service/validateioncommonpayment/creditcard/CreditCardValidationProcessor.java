package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.creditcard;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.cache.Transaction;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.creditcard.CreditCardDetail;
import com.tmb.common.model.creditcard.SilverlakeCustomerDetail;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayMapper;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayValidationEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.AdditionalParamCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.BillPaymentCreditCard;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.PayeeCard;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.PayerAccount;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CreditCardService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ValidationProcessingTemplate;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.regex.Pattern;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.DATETIME_FORMAT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ONLINE_TRANS_REF_SEQUENCE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TOP_UP_REF_SEQUENCE_DIGIT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_CREDIT_CARD;

@Service
@RequiredArgsConstructor
public class CreditCardValidationProcessor extends ValidationProcessingTemplate<CreditCardPrepareDataValidate, CreditCardExternalValidateResponse, CreditCardValidateDataAfterCallExternal, ActivityBillPayValidationEvent> {
    protected static final TMBLogger<CreditCardValidationProcessor> logger = new TMBLogger<>(CreditCardValidationProcessor.class);

    private static final String CUSTOMER_KYC_KEY = "customerKYC";
    private static final String MASTER_BILLER_KEY = "masterBiller";
    private static final String CUSTOMER_PROFILE_KEY = "customerProfile";
    private static final String DEPOSIT_ACCOUNT_KEY = "depositAccount";
    private static final String TARGET_CREDIT_CARD_DETAIL_KEY = "targetCreditCardDetail";

    private final PaymentService paymentService;
    private final CustomerService customerService;
    private final BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;
    private final LogEventPublisherService logEventPublisherService;
    private final CustomerServiceClient customerServiceClient;
    private final CacheService cacheService;
    private final AsyncHelper asyncHelper;
    private final DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    private final DailyLimitService dailyLimitService;
    private final CreditCardService creditCardService;
    private final BaseBillPayValidator baseBillPayValidator;
    private final CacheMapper cacheMapper;

    private static final Pattern NUMERIC_PATTERN = Pattern.compile("^[0-9]+$");

    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_CREDIT_CARD;
    }

    @Override
    protected ActivityBillPayValidationEvent initialActivityLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) {
        return ActivityBillPayMapper.INSTANCE.toActivityBillPayValidation(headers, request, cache);
    }

    @Override
    protected CreditCardPrepareDataValidate prepareData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException {

        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String compCode = cache.getPaymentInformation().getCompCode();

        CreditCardPrepareDataValidate prepareData = new CreditCardPrepareDataValidate();

        try {
            Map<String, CompletableFuture<?>> futures = fetchRequiredData(correlationId, crmId, compCode, request, headers, cache);

            CompletableFuture.allOf(futures.values().toArray(new CompletableFuture[0])).join();

            populatePrepareData(prepareData, futures);

        } catch (Exception e) {
            handlePrepareDataException(e);
        }

        return prepareData;
    }

    private Map<String, CompletableFuture<?>> fetchRequiredData(String correlationId, String crmId, String compCode, ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException {

        Map<String, CompletableFuture<?>> futures = new HashMap<>();

        futures.put(CUSTOMER_KYC_KEY, fetchCustomerKYC(correlationId, crmId));
        futures.put(MASTER_BILLER_KEY, fetchMasterBiller(correlationId, compCode));
        futures.put(CUSTOMER_PROFILE_KEY, fetchCustomerProfile(correlationId, crmId));
        futures.put(DEPOSIT_ACCOUNT_KEY, fetchDepositAccount(request, headers));

        futures.put(TARGET_CREDIT_CARD_DETAIL_KEY, fetchTargetCreditCardDetail(correlationId, cache));

        return futures;
    }

    private CompletableFuture<CustomerKYCResponse> fetchCustomerKYC(String correlationId, String crmId) throws TMBCommonException {
        return asyncHelper.executeMethodAsync(() -> customerService.getCustomerKYC(correlationId, crmId));
    }

    private CompletableFuture<MasterBillerResponse> fetchMasterBiller(String correlationId, String compCode) throws TMBCommonException {
        return asyncHelper.executeMethodAsync(() -> paymentService.getMasterBiller(correlationId, compCode));
    }

    private CompletableFuture<CustomerCrmProfile> fetchCustomerProfile(String correlationId, String crmId) throws TMBCommonException {
        return asyncHelper.executeRequestAsync(() -> customerServiceClient.fetchCustomerCrmProfile(correlationId, crmId));
    }

    private CompletableFuture<DepositAccount> fetchDepositAccount(ValidationCommonPaymentRequest request, HttpHeaders headers) throws TMBCommonException {
        return asyncHelper.executeMethodAsync(() -> billPayAccountCommonPaymentService.getAccountByAccountNumber(request.getDeposit().getAccountNumber(), headers));
    }

    private CompletableFuture<CreditCardDetail> fetchTargetCreditCardDetail(String correlationId, CommonPaymentDraftCache cache) {

        return getTransIdFromCache(cache).map(transId -> fetchCreditCardByAccountId(correlationId, transId)).orElseGet(() -> fetchCreditCardDetailFromReference(correlationId, cache));
    }

    private CompletableFuture<CreditCardDetail> fetchCreditCardDetailFromReference(String correlationId, CommonPaymentDraftCache cache) {

        try {
            CreditCardDetail creditCardDetail = getCreditCardDetailFromReference(correlationId, cache);
            return CompletableFuture.completedFuture(creditCardDetail);
        } catch (TMBCommonException e) {
            logger.error("Error fetching credit card from reference: {}", e.getMessage());
            return CompletableFuture.failedFuture(new RuntimeException(e));
        }
    }

    private Optional<String> getTransIdFromCache(CommonPaymentDraftCache cache) {
        return Optional.ofNullable(cache)
                .map(CommonPaymentDraftCache::getDeepLinkRequest)
                .map(DeepLinkRequestInCache::getTransId)
                .filter(StringUtils::isNotBlank);
    }

    private CompletableFuture<CreditCardDetail> fetchCreditCardByAccountId(String correlationId, String accountId) {
        try {
            return asyncHelper.executeMethodAsync(() -> creditCardService.getCreditCardDetailByAccountId(accountId, correlationId));
        } catch (TMBCommonException e) {
            logger.error("Error fetching credit card by account ID: {}", e.getMessage(), e);
            return CompletableFuture.failedFuture(e);
        }
    }

    private void populatePrepareData(CreditCardPrepareDataValidate prepareData, Map<String, CompletableFuture<?>> futures) {

        Optional.ofNullable(futures.get(CUSTOMER_KYC_KEY))
                .map(CompletableFuture::join)
                .map(CustomerKYCResponse.class::cast)
                .ifPresent(prepareData::setCustomerKYCResponse);

        Optional.ofNullable(futures.get(MASTER_BILLER_KEY))
                .map(CompletableFuture::join)
                .map(MasterBillerResponse.class::cast)
                .ifPresent(prepareData::setMasterBillerResponse);

        Optional.ofNullable(futures.get(CUSTOMER_PROFILE_KEY))
                .map(CompletableFuture::join)
                .map(CustomerCrmProfile.class::cast)
                .ifPresent(prepareData::setCustomerCrmProfile);

        Optional.ofNullable(futures.get(DEPOSIT_ACCOUNT_KEY))
                .map(CompletableFuture::join)
                .map(DepositAccount.class::cast)
                .ifPresent(prepareData::setFromDepositAccount);

        Optional.ofNullable(futures.get(TARGET_CREDIT_CARD_DETAIL_KEY))
                .map(CompletableFuture::join)
                .map(CreditCardDetail.class::cast)
                .ifPresent(prepareData::setTargetCreditCardDetail);
    }

    private void handlePrepareDataException(Exception e) throws TMBCommonException {
        if (e instanceof ExecutionException && e.getCause() instanceof TMBCommonException ex) {
            logger.error("Error ExecutionException TMBCommonException in method PrepareData: {}", ex.getMessage(), e);
            throw ex;
        }

        if (e instanceof InterruptedException) {
            Thread.currentThread().interrupt();
        }

        logger.error("Error in method PrepareData", e);
        throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Error execution exception get prepare data. Please verify prepareData method");
    }

    private CreditCardDetail getCreditCardDetailFromReference(String correlationId, CommonPaymentDraftCache draftCache) throws TMBCommonException {
        if (draftCache == null || draftCache.getPaymentInformation() == null || draftCache.getPaymentInformation().getProductDetail() == null) {
            throw CommonServiceUtils.getUnhandledTmbCommonException(
                    ResponseCode.FAILED_V2, "Cannot get product detail from cache");
        }
        ProductDetail productDetail = draftCache.getPaymentInformation().getProductDetail();
        String productRef1 = productDetail.getProductRef1();
        if (isValidCreditCardNumber(productRef1)) {
            logger.info("Using Reference1 as card ID for credit card detail");
            return creditCardService.getCreditCardDetailByCardId(productRef1, correlationId);
        } else {
            logger.info("Using Reference2 as account ID for credit card detail");
            return creditCardService.getCreditCardDetailByAccountId(productDetail.getProductRef2(), correlationId);
        }
    }

    private boolean isValidCreditCardNumber(String ref) {
        return Optional.ofNullable(ref)
                .filter(r -> !r.isEmpty())
                .filter(this::isNumeric)
                .isPresent();
    }

    private boolean isNumeric(String text) {
        return NUMERIC_PATTERN.matcher(text).matches();
    }

    @Override
    protected CreditCardPrepareDataValidate validateData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, CreditCardPrepareDataValidate prepareData) throws TMBCommonException {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final CreditCardDetail targetCreditCardDetail = prepareData.getTargetCreditCardDetail();
        final boolean isOwn = this.isPayBillBySelfCreditCard(targetCreditCardDetail, customerCrmProfile.getCrmId());

        this.validateBillerExpiration(masterBillerResponse);

        this.checkServiceHours(masterBillerResponse);

        if (!isOwn) {
            this.validateDailyLimitExceeded(request, masterBillerResponse, customerCrmProfile);
        }

        return prepareData;
    }

    private void validateBillerExpiration(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateBillerExpiration(masterBillerResponse);
    }

    private void checkServiceHours(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateServiceHours(masterBillerResponse);
    }

    private void validateDailyLimitExceeded(ValidationCommonPaymentRequest request, MasterBillerResponse masterBiller, CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        BigDecimal amount = request.getDeposit().getAmount();
        dailyLimitService.validateDailyLimitExceeded(masterBiller.getBillerInfo().getBillerGroupType(), customerCrmProfile, amount);
    }

    @Override
    protected CreditCardExternalValidateResponse validateWithExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, CreditCardPrepareDataValidate prepareData) {
        // Do nothing
        return new CreditCardExternalValidateResponse();
    }

    @Override
    protected CreditCardValidateDataAfterCallExternal validateAfterCallExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, CreditCardPrepareDataValidate prepareData, CreditCardExternalValidateResponse externalResponse) throws TMBCommonException {
        final DepositAccount depositAccount = prepareData.getFromDepositAccount();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final CreditCardDetail targetCreditCardDetail = prepareData.getTargetCreditCardDetail();
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();

        CreditCardValidateDataAfterCallExternal validateDataAfterCallExternal = new CreditCardValidateDataAfterCallExternal();

        final BigDecimal fee = calculateFee(masterBillerResponse.getBillerInfo().getFee(), depositAccount);
        final BigDecimal amount = request.getAmount();

        this.validateInsufficientFund(request, fromDepositAccount, fee);

        boolean isOwn = this.isPayBillBySelfCreditCard(targetCreditCardDetail, customerCrmProfile.getCrmId());
        CommonAuthenResult commonAuthenResult = this.isRequireCommonAuthen(request, headers, customerCrmProfile, isOwn);

        CommonAuthenticationValidationCommonPaymentResponse commonAuthenResponse = null;
        if (commonAuthenResult.isRequireCommonAuthen()) {
            commonAuthenResponse = new CommonAuthenticationValidationCommonPaymentResponse().setTotalPaymentAccumulateUsage(amount.add(customerCrmProfile.getPaymentAccuUsgAmt())).setFlowName(COMMON_AUTH_BILL_FLOW_NAME).setFeatureId(COMMON_PAYMENT_BILL_PAY_FEATURE_ID).setBillerCompCode(cache.getPaymentInformation().getCompCode());
        }

        validateDataAfterCallExternal.setRequireCommonAuthen(commonAuthenResult.isRequireCommonAuthen());
        validateDataAfterCallExternal.setCommonAuthentication(commonAuthenResponse);
        validateDataAfterCallExternal.setTotalAmount(amount.add(fee));
        validateDataAfterCallExternal.setFeeAfterCalculated(fee);
        validateDataAfterCallExternal.setCommonAuthenResult(commonAuthenResult);
        validateDataAfterCallExternal.setPayByOwner(isOwn);

        return validateDataAfterCallExternal;
    }

    protected BigDecimal calculateFee(BigDecimal fee, DepositAccount depositAccount) {
        boolean shouldWaiveFee = Optional.ofNullable(depositAccount)
                .map(DepositAccount::getWaiveFeeForBillpay)
                .filter("1"::equals)
                .isPresent();

        return shouldWaiveFee ? new BigDecimal("0.00") : fee;
    }

    protected void validateInsufficientFund(ValidationCommonPaymentRequest request, DepositAccount fromDepositAccount, BigDecimal fee) throws TMBCommonException {
        baseBillPayValidator.validateInsufficientFund(request, fromDepositAccount, fee);
    }

    private boolean isPayBillBySelfCreditCard(CreditCardDetail targetCreditCardDetail, String crmId) {
        return Optional.ofNullable(targetCreditCardDetail)
                .map(CreditCardDetail::getCustomer)
                .map(SilverlakeCustomerDetail::getRmId)
                .filter(rmId -> rmId.equals(crmId))
                .isPresent();
    }

    protected CommonAuthenResult isRequireCommonAuthen(ValidationCommonPaymentRequest request, HttpHeaders headers, CustomerCrmProfile customerCrmProfile, boolean isOwn) throws TMBCommonException {
        BigDecimal amount = request.getAmount();
        return dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(headers, amount, isOwn, customerCrmProfile);
    }

    @Override
    protected void saveSuccessLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, CreditCardPrepareDataValidate prepareData, CreditCardExternalValidateResponse externalResponse, CreditCardValidateDataAfterCallExternal validateDataAfterCallExternal, ActivityBillPayValidationEvent activityEvent) {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final CreditCardSupplementary creditCardDetail = prepareData.getFromCreditCardDetail();

        ActivityBillPayValidationEvent activityEventData = activityEvent;
        activityEventData = ActivityBillPayMapper.INSTANCE.updateActivityCreditCardBillPayValidation(activityEventData, masterBillerResponse, cache, request, creditCardDetail, validateDataAfterCallExternal);

        logEventPublisherService.saveActivityLog(activityEventData);
    }

    @Override
    protected void updateCache(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, CreditCardPrepareDataValidate prepareData, CreditCardExternalValidateResponse externalResponse, CreditCardValidateDataAfterCallExternal validateDataAfterCallExternal) throws JsonProcessingException {
        final CreditCardDetail payeeCreditCardDetail = prepareData.getTargetCreditCardDetail();
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final CommonAuthenticationValidationCommonPaymentResponse commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final String crmId = headers.getFirst(HEADER_CRM_ID);

        CreditCardConfirmRequest confirmRequestBody = this.initialCreditCardConfirmRequest(request, payeeCreditCardDetail, cache, masterBillerResponse, validateDataAfterCallExternal);


        var validateDraftCache = new ValidationCommonPaymentDraftCache()
                .setExternalConfirmRequest(new ExternalConfirmRequest()
                        .setCreditCardConfirmRequest(confirmRequestBody))
                .setRequireCommonAuthen(validateDataAfterCallExternal.isRequireCommonAuthen())
                .setFromDepositAccount(cacheMapper.toDepositAccountInCache(prepareData.getFromDepositAccount()))
                .setFromCreditCardDetail(cacheMapper.toCreditCardSupplementaryInCache(prepareData.getFromCreditCardDetail()))
                .setMasterBillerResponse(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse))
                .setCommonAuthentication(commonAuthentication)
                .setFeeCalculated(validateDataAfterCallExternal.getFeeAfterCalculated())
                .setTotalAmount(validateDataAfterCallExternal.getTotalAmount())
                .setAdditionalParam(new AdditionalParamCommonPaymentDraftCache()
                        .setPayByOwner(validateDataAfterCallExternal.isPayByOwner())
                );

        cache.setCrmId(crmId);
        cache.setValidateRequest(request);
        cache.setValidateDraftCache(validateDraftCache);

        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + request.getTransactionId();
        cacheService.set(cacheKey, COMMON_PAYMENT_HASH_KEY_CACHE, cache);
    }

    private CreditCardConfirmRequest initialCreditCardConfirmRequest(ValidationCommonPaymentRequest request, CreditCardDetail creditCardDetail, CommonPaymentDraftCache cache, MasterBillerResponse masterBillerResponse, CreditCardValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final BigDecimal feeAfterCalculated = validateDataAfterCallExternal.getFeeAfterCalculated();

        String paymentId = this.getSequencePaymentId();
        String ePayCode = getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT);

        CreditCardConfirmRequest creditCardConfirmRequest = new CreditCardConfirmRequest();
        BillPaymentCreditCard billPayment = new BillPaymentCreditCard();

        billPayment.setAmount(String.valueOf(request.getDeposit().getAmount()));
        billPayment.setCompCode(masterBillerResponse.getBillerInfo().getBillerCompCode());
        billPayment.setPaymentId(paymentId);
        billPayment.setBankRefId(paymentId);
        billPayment.setRef1(creditCardDetail.getCardId());
        billPayment.setEpayCode(ePayCode);
        billPayment.setPayerAccount(new PayerAccount());
        billPayment.getPayerAccount().setId(request.getDeposit().getAccountNumber());
        billPayment.getPayerAccount().setType(TMBUtils.getAccountType(request.getDeposit().getAccountNumber()));
        billPayment.setFee(new CreditCardFee(feeAfterCalculated));

        PayeeCard payeeCard = new PayeeCard();
        payeeCard.setAccountId(creditCardDetail.getAccountId());
        payeeCard.setCardId(cache.getPaymentInformation().getProductDetail().getProductRef1());
        payeeCard.setCardEmbossingName(creditCardDetail.getCardInfo().getCardEmbossingName1());
        payeeCard.setExpiryDate(creditCardDetail.getCardInfo().getExpiredBy());
        payeeCard.setProductId(creditCardDetail.getCardStatus().getCardPloanFlag());
        payeeCard.setProductGroupId(creditCardDetail.getProductId());
        billPayment.setPayeeCard(payeeCard);

        creditCardConfirmRequest.setBillPayment(billPayment);
        return creditCardConfirmRequest;
    }

    public String getSequencePaymentId() {
        String transactionId = getTransactionId(ONLINE_TRANS_REF_SEQUENCE, 5);
        transactionId = transactionId.substring(transactionId.length() - 5);

        SimpleDateFormat dateFormat = new SimpleDateFormat(DATETIME_FORMAT);
        String requestDate = dateFormat.format(new Date(System.currentTimeMillis()));
        return requestDate + transactionId;
    }

    public String getTransactionId(String key, int digits) {
        return Transaction.getTransactionId(key, digits);
    }

    @Override
    protected ValidationCommonPaymentResponse mappingResponse(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, CreditCardPrepareDataValidate prepareData, CreditCardExternalValidateResponse externalResponse, CreditCardValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final boolean isRequireCommonAuthen = validateDataAfterCallExternal.isRequireCommonAuthen();
        final BigDecimal fee = validateDataAfterCallExternal.getFeeAfterCalculated();
        final CommonAuthenticationValidationCommonPaymentResponse commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final BigDecimal amount = request.getAmount();

        ValidationCommonPaymentResponse responseData = new ValidationCommonPaymentResponse();
        responseData.setTransactionId(request.getTransactionId());
        responseData.setFee(fee);
        responseData.setAmount(amount);
        responseData.setTotalAmount(amount.add(fee));
        responseData.setIsRequireCommonAuthen(isRequireCommonAuthen);

        if (isRequireCommonAuthen) {
            responseData.setCommonAuthenticationInformation(commonAuthentication);
        }

        return responseData;
    }

    @Override
    protected TMBCommonException handleException(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, CreditCardPrepareDataValidate prepareData, CreditCardExternalValidateResponse externalResponse, CreditCardValidateDataAfterCallExternal validateDataAfterCallExternal, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process CreditCardValidation. : {}", e.getMessage(), e);
        if (e instanceof TMBCommonException ex) {
            throw ex;
        } else if (e instanceof TMBCommonExceptionWithResponse ex) {
            throw ex;
        } else {
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
        }
    }

    @Override
    protected void saveFailedLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, ActivityBillPayValidationEvent activityEvent, CreditCardPrepareDataValidate prepareData, Exception e) {
        final MasterBillerResponse masterBiller = Optional.ofNullable(prepareData)
                .map(CreditCardPrepareDataValidate::getMasterBillerResponse)
                .orElse(null);

        activityEvent = ActivityBillPayMapper.INSTANCE.updateActivityBillPayValidationFailed(activityEvent, masterBiller, cache, request, e);
        logEventPublisherService.saveActivityLog(activityEvent);
    }
}