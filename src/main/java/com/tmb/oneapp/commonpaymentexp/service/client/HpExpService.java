package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.client.HpExpServiceFeignClient;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.CacheResponse;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class HpExpService {
    private static final TMBLogger<HpExpService> logger = new TMBLogger<>(HpExpService.class);

    private final HpExpServiceFeignClient hpExpServiceFeignClient;
    private final FeignClientHelper feignClientHelper;

    public CacheResponse getAldxFeeCache(String correlationId , String acceptLanguage , String appVersion, String crmId, String objectiveId, String hpAccountNo) throws TMBCommonException {
        return feignClientHelper.executeRequest(() -> hpExpServiceFeignClient.getAldxFeeCache(correlationId, acceptLanguage, appVersion, crmId, objectiveId, hpAccountNo));
    }
}
