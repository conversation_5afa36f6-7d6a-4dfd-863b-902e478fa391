package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.client.AccountServiceClient;
import com.tmb.oneapp.commonpaymentexp.model.loanaccount.AccountLoanDetailRequest;
import com.tmb.oneapp.commonpaymentexp.model.loanaccount.HomeLoanFullInfoResponse;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Service for account-related operations
 */
@Service
@RequiredArgsConstructor
public class AccountService {
    private static final TMBLogger<AccountService> logger = new TMBLogger<>(AccountService.class);
    private final AccountServiceClient accountServiceClient;
    private final FeignClientHelper feignClientHelper;

    public HomeLoanFullInfoResponse fetchLoanAccountByLoanAccountID(String correlationId, String loanAccountID) throws TMBCommonException {
        return feignClientHelper.executeRequest(() -> accountServiceClient.getAccountLoanDetail(correlationId, new AccountLoanDetailRequest().setAccountId(loanAccountID)));
    }
}
