package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CommonTime;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.cache.BillerInfoResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRule;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CommonAuthenticationService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.ServiceHoursUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_IS_USED;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_IP_ADDRESS;

@Component
@RequiredArgsConstructor
public class CommonValidateConfirmationService { //TODO please move this class to other packege
    private static final TMBLogger<CommonValidateConfirmationService> logger = new TMBLogger<>(CommonValidateConfirmationService.class);

    private final CacheService cacheService;
    private final CommonAuthenticationService commonAuthenticationService;
    private final DailyLimitService dailyLimitService;

    public void validateServiceHours(CommonPaymentDraftCache cache) throws TMBCommonException {
        BillerInfoResponseInCache billerInfo = cache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo();

        CommonTime serviceHours = new CommonTime();
        serviceHours.setStart(billerInfo.getStartTime());
        serviceHours.setEnd(billerInfo.getEndTime());

        boolean isDuringServiceHours = ServiceHoursUtils.isDuringServiceHours(new Date(), serviceHours);

        if (!isDuringServiceHours) {
            logger.error("Transaction attempted outside service hours. [serviceHour.start = {}, serviceHour.end = {}]", serviceHours.getStart(), serviceHours.getEnd());
            String messageError = String.format(ResponseCode.UNAVAILABLE_SERVICE_HOUR.getMessage(), serviceHours.getStart() + "-" + serviceHours.getEnd());
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.UNAVAILABLE_SERVICE_HOUR.getCode(), messageError);
        }
    }

    public void verifyAuthentication(String transactionId, CommonPaymentDraftCache cache, HttpHeaders headers) throws TMBCommonException {
        boolean isRequireCommonAuthen = cache.getValidateDraftCache().isRequireCommonAuthen();
        boolean isNotRequireConfirmPin = !isRequireCommonAuthen;
        if (isNotRequireConfirmPin) {
            return;
        }

        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String ipAddress = headers.getFirst(HEADER_IP_ADDRESS);

        CommonAuthenVerifyRefRequest authRequest = new CommonAuthenVerifyRefRequest();
        authRequest.setRefId(transactionId);
        authRequest.setFlowName(cache.getValidateDraftCache().getCommonAuthentication().getFlowName());

        var commonAuthentication = cache.getValidateDraftCache().getCommonAuthentication();
        String requestAmount = String.valueOf(cache.getValidateRequest().getAmount());

        var rule = new CommonAuthenVerifyRule()
                .setDailyAmount(String.valueOf(commonAuthentication.getTotalPaymentAccumulateUsage()))
                .setFeatureId(commonAuthentication.getFeatureId())
                .setAmount(requestAmount)
                .setCompCode(commonAuthentication.getBillerCompCode());

        commonAuthenticationService.verifyReferenceId(correlationId, crmId, ipAddress, authRequest, rule);
    }

    public void validateTransactionByTransactionId(String transactionId) throws TMBCommonException {
        this.validateTransaction(COMMON_PAYMENT_CACHE_PREFIX + transactionId);
    }

    public void validateTransaction(String cacheKey) throws TMBCommonException {
        boolean isDuplicateTransaction = !cacheService.putIfAbsent(cacheKey, COMMON_PAYMENT_HASH_KEY_IS_USED, "true");

        if (isDuplicateTransaction) {
            logger.error("Duplicate transaction detected for cache key: {}", cacheKey);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.TRANSACTION_DUPLICATE_ERROR);
        }
    }

    public void validateDailyLimitExceeded(CommonPaymentDraftCache draftCache, CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        boolean isNotPayWithCreditCard = !draftCache.getValidateRequest().getCreditCard().isPayWithCreditCardFlag();

        if (isNotPayWithCreditCard) {
            BigDecimal amountFromValidateRequest = draftCache.getValidateRequest().getDeposit().getAmount();
            dailyLimitService.validateDailyLimitExceeded(draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType(), customerCrmProfile, amountFromValidateRequest);
        }
    }

    public void baseValidateData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException {
        final String transactionId = request.getTransactionId();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();

        this.validateServiceHours(draftCache);
        this.verifyAuthentication(transactionId, draftCache, headers);
        this.validateTransactionByTransactionId(transactionId);
        this.validateDailyLimitExceeded(draftCache, customerCrmProfile);
    }
}
