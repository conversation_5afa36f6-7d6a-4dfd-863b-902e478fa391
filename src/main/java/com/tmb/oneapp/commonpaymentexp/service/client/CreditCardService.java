package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.creditcard.CreditCardDetail;
import com.tmb.oneapp.commonpaymentexp.client.CreditCardServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.GetCardResponse;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CreditCardService {
    private static final TMBLogger<CreditCardService> logger = new TMBLogger<>(CreditCardService.class);
    private final CreditCardServiceClient creditCardServiceClient;

    public CreditCardDetail getCreditCardDetailByAccountId(String accountId, String correlationId) throws TMBCommonException {
        try {
            return Optional.ofNullable(creditCardServiceClient.getCreditCardDetailsByAccountId(accountId, correlationId))
                    .map(ResponseEntity::getBody)
                    .map(GetCardResponse::getCreditCard)
                    .orElseThrow(this::cardNotFoundException);
        } catch (FeignException e) {
            logger.error("Error getCreditCardDetailByAccountId FeignException : {}", e.getMessage(), e);
            throw cardNotFoundException();
        }
    }

    public CreditCardDetail getCreditCardDetailByCardId(String cardId, String correlationId) throws TMBCommonException {
        try {
            return Optional.ofNullable(creditCardServiceClient.getCreditCardDetailsByCardId(cardId, correlationId))
                    .map(ResponseEntity::getBody)
                    .map(GetCardResponse::getCreditCard)
                    .orElseThrow(this::cardNotFoundException);
        } catch (FeignException e) {
            logger.error("Error getCreditCardDetailByCardId FeignException : {}", e.getMessage(), e);
            throw cardNotFoundException();
        }
    }

    public void deleteCreditCardCache(String correlationId, String crmId) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("correlationId", correlationId);
        try {
            creditCardServiceClient.deleteCreditCardAccountSummaryResCache(headers, crmId);
        } catch (FeignException e) {
            logger.error("Error deleteCreditCardCache FeignException : {}", e.getMessage(), e);
        }
    }

    public TMBCommonException cardNotFoundException() {
        return CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR);
    }
}
