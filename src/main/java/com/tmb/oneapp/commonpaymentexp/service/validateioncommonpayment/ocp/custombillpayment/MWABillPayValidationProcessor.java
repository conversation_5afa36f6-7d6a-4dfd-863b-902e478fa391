package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp.custombillpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBalance;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.BillPayValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.MWABill;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.MWAValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponseMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp.OCPValidationHelper;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_MWA;

@Service
public class MWABillPayValidationProcessor extends CustomBillPayValidationProcessor {
    private static final TMBLogger<MWABillPayValidationProcessor> logger = new TMBLogger<>(MWABillPayValidationProcessor.class);
    private static final String BILL_PAYMENT_ADDITIONAL_PARAM_MESSAGE = "Message";

    public MWABillPayValidationProcessor(PaymentService paymentService, LogEventPublisherService logEventPublisherService, CacheService cacheService, DailyLimitPinFreeValidator dailyLimitPinFreeValidator, BaseBillPayValidator baseBillPayValidator, OCPValidationHelper ocpValidationHelper, CacheMapper cacheMapper, OCPBillRequestMapper ocpBillRequestMapper) {
        super(paymentService, logEventPublisherService, cacheService, dailyLimitPinFreeValidator, baseBillPayValidator, ocpValidationHelper, cacheMapper, ocpBillRequestMapper);
    }


    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_MWA;
    }

    @Override
    protected OCPBillRequest createOCPBillPaymentValidateRequest(ValidationCommonPaymentRequest request, DepositAccount fromDepositAccount, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, CreditCardSupplementary creditCardDetail) {
        return ocpBillRequestMapper.toOCPBillRequestForValidateMWAOCPBillPayment(request, fromDepositAccount, cache, creditCardDetail);
    }

    @Override
    protected void validateAmount(ValidationCommonPaymentRequest request, OCPBillPayment ocpDataResponse) throws TMBCommonException {
        BigDecimal mwaAmount = Optional.ofNullable(ocpDataResponse.getBalance())
                .map(OCPBalance::getMax)
                .map(BigDecimal::new).orElseThrow(() -> CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.MWA_AMOUNT_NOT_FOUND_ERROR, "mwa amount is null. Please verify additional params from OCP response"));

        BigDecimal requestAmount = request.getAmount();
        if (mwaAmount.compareTo(requestAmount) != 0) {
            logger.error("Error amount not match. Please verify amount in request and response. [request.amount = {}, mwa.amount = {}]", requestAmount, mwaAmount);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.MWA_AMOUNT_NOT_FOUND_ERROR);
        }
    }

    @Override
    protected ValidationCommonPaymentResponse mappingResponse(ValidationCommonPaymentRequest request,
                                                              HttpHeaders headers,
                                                              CommonPaymentDraftCache cache,
                                                              BillPayPrepareDataValidate prepareData,
                                                              BillPayExternalValidateResponse externalResponse,
                                                              BillPayValidateDataAfterCallExternal validateDataAfterCallExternal) {
        ValidationCommonPaymentResponse response = ValidationCommonPaymentResponseMapper.INSTANCE.mapToValidationCommonPaymentResponseForMWA(request, cache, externalResponse, validateDataAfterCallExternal);

        MWAValidationCommonPaymentResponse mwaResponse = buildMWAResponse(externalResponse.getOcpDataResponse());
        response.setMwaValidationCommonPaymentResponse(mwaResponse);

        return response;
    }

    private MWAValidationCommonPaymentResponse buildMWAResponse(OCPBillPayment ocpDataResponse) {
        Map<String, String> params = ocpDataResponse.getAdditionalParams().stream()
                .collect(Collectors.toMap(
                        AdditionalParam::getName,
                        param -> String.valueOf(param.getValue()),
                        (existing, replacement) -> existing
                ));

        String msg = params.getOrDefault(BILL_PAYMENT_ADDITIONAL_PARAM_MESSAGE, null);

        return new MWAValidationCommonPaymentResponse()
                .setBill(getBillPerMonth(msg));
    }

    private List<MWABill> getBillPerMonth(String billOfBiller) {
        String[] bills = billOfBiller.split("\\|");
        int months = Integer.parseInt(bills[0]);

        List<MWABill> billPerMonths = new ArrayList<>();
        for (int i = 0; i < months; i++) {
            MWABill billPerMonth = new MWABill();
            String billNumber = bills[i * 5 + 2];
            billPerMonth.setBillNumber(billNumber);

            String vat = bills[i * 5 + 3];
            BigDecimal vatDouble = new BigDecimal(vat);
            billPerMonth.setVat(vatDouble.setScale(2, RoundingMode.HALF_UP).toString());

            String amount = bills[i * 5 + 4];
            BigDecimal amountDouble = new BigDecimal(amount);
            billPerMonth.setAmount(amountDouble.setScale(2, RoundingMode.HALF_UP).toString());

            billPerMonths.add(billPerMonth);
        }

        return billPerMonths;
    }
}
