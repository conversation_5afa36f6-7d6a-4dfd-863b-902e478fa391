package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ocp.custombillpayment;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialCustomBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialMWABillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.client.LoyaltyBizService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.BaseConfirmServiceHelper;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.CommonValidateConfirmationService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_MWA;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;

/**
 * Service processor for MWA bill payment confirmation
 */
@Service
public class MWABillPaymentConfirmServiceProcessor extends CustomBillPaymentConfirmServiceProcessor {

    public MWABillPaymentConfirmServiceProcessor(BaseConfirmServiceHelper baseConfirmServiceHelper, CommonValidateConfirmationService commonValidateConfirmationService, PaymentService paymentService, AsyncHelper asyncHelper, LogEventPublisherService logEventPublisherService, LoyaltyBizService loyaltyBizService) {
        super(baseConfirmServiceHelper, commonValidateConfirmationService, paymentService, asyncHelper, logEventPublisherService, loyaltyBizService);
    }

    @Override
    protected FinancialCustomBillPayActivityLog initialFinancialLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String transactionTime = String.valueOf(System.currentTimeMillis());
        final String ePayCode = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getEpayCode();

        return new FinancialMWABillPayActivityLog(crmId, ePayCode, draftCache, correlationId, transactionTime);
    }

    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_MWA;
    }
}