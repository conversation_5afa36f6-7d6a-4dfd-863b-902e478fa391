package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PartnerPaymentStatusCallback;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class PaymentStatusPublisherService {
    private static final TMBLogger<PaymentStatusPublisherService> logger = new TMBLogger<>(PaymentStatusPublisherService.class);
    private final KafkaProducerService kafkaProducerService;
    private final String paymentStatusTopic;
    private final String partnerPaymentStatusTopic;


    public PaymentStatusPublisherService(KafkaProducerService kafkaProducerService,
                                         @Value("${com.tmb.oneapp.common.payment.exp.payment.status}") final String paymentStatusTopic,
                                         @Value("${com.tmb.oneapp.common.payment.exp.partner.payment.status}") final String partnerPaymentStatusTopic) {
        this.kafkaProducerService = kafkaProducerService;
        this.paymentStatusTopic = paymentStatusTopic;
        this.partnerPaymentStatusTopic = partnerPaymentStatusTopic;
    }

    public void publishInternal(PartnerPaymentStatusCallback partnerCallback, String identifier) {
        try {
            String payload = TMBUtils.convertJavaObjectToString(partnerCallback);
            Map<String, String> headers = new HashMap<>();
            headers.put("entry_id", identifier);
            logger.debug("Publishing internal payment status callback to kafka topic: {} with headers: {} and payload: {}", paymentStatusTopic, headers, payload);
            kafkaProducerService.sendMessageAsync(paymentStatusTopic, "", payload, headers);
            logger.debug("Successfully published internal payment status callback to URL: {}", partnerCallback.getUrl());
        } catch (Exception e) {
            logger.error("Error publishing internal payment status callback to URL: {}", partnerCallback.getUrl(), e);
        }
    }

    public void publishPartner(PartnerPaymentStatusCallback partnerCallback, String identifier) {
        try {
            String payload = TMBUtils.convertJavaObjectToString(partnerCallback);
            Map<String, String> headers = new HashMap<>();
            headers.put("entry_id", identifier);
            logger.debug("Publishing partner payment status callback to kafka topic: {} with headers: {} and payload: {}", partnerPaymentStatusTopic, headers, payload);
            kafkaProducerService.sendMessageAsync(partnerPaymentStatusTopic, "", payload, headers);
            logger.debug("Successfully published partner payment status callback to URL: {}", partnerCallback.getUrl());
        } catch (Exception e) {
            logger.error("Error publishing partner payment status callback to URL: {}", partnerCallback.getUrl(), e);
        }
    }
}