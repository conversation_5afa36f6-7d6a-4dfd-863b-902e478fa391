package com.tmb.oneapp.commonpaymentexp.service.dailylimit;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.DailyUsageLimit;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;

import java.math.BigDecimal;

public class TopUpDailyLimitStrategy implements DailyLimitStrategy {
    @Override
    public DailyUsageLimit calculateLimit(CustomerCrmProfile profile) {
        return new DailyUsageLimit(
                BigDecimal.valueOf(profile.getEbAccuUsgAmtDaily()),
                BigDecimal.valueOf(profile.getEbMaxLimitAmtCurrent())
        );
    }
}
