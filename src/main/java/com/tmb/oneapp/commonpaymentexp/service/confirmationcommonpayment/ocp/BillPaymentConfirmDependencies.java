package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ocp;

import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.CallbackConfirmService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Getter
public class BillPaymentConfirmDependencies {
    private final DailyLimitService dailyLimitService;
    private final CustomersTransactionService customersTransactionService;
    private final CustomerService customerService;
    private final LogEventPublisherService logEventPublisherService;
    private final NotificationCommonPaymentService notificationCommonPaymentService;
    private final AsyncHelper asyncHelper;
    private final PaymentService paymentService;
    private final CallbackConfirmService callbackConfirmService;
    private final CacheService cacheService;
}