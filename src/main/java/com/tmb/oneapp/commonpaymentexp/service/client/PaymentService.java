package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.PaymentServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AutoLoanOCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.ExternalErrorCustomDescription;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanOCPResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETERequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.creditcard.CreditCardConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpaymenttoggle.CommonPaymentToggle;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;


@Service
@RequiredArgsConstructor
public class PaymentService {
    private static final TMBLogger<PaymentService> logger = new TMBLogger<>(PaymentService.class);
    private static final String LOG_EXCEPTION_PAYMENT_SERVICE_ERROR_MESSAGE_PATTERN = "Exception encountered while fetching data from {} in method {}.";
    private static final String LOG_FEIGN_EXCEPTION_PAYMENT_SERVICE_ERROR_MESSAGE_PATTERN = "FeignException encountered while fetching data from {} in method {}.";
    private final PaymentServiceClient paymentServiceClient;
    private final FeignClientHelper feignClientHelper;
    private final ErrorMappingHelper errorMappingHelper;


    public CommonPaymentConfig getCommonPaymentConfig(String correlationId, String entryId) throws TMBCommonException {
        logger.info(">>>> fetch common payment config from payment service");
        return feignClientHelper.executeRequestOrElseThrow(() -> paymentServiceClient.fetchCommonPaymentConfig(correlationId, entryId), () -> new TMBCommonException("data not found"));
    }

    public BillPayConfiguration getBillPayConfig(String correlationId) throws TMBCommonException {
        return feignClientHelper.executeRequest(() -> paymentServiceClient.fetchBillPayConfig(correlationId));
    }

    public MasterBillerResponse getMasterBiller(String correlationId, String compCode) throws TMBCommonException {
        return feignClientHelper.executeRequest(() -> paymentServiceClient.fetchMasterBillerByCompCode(correlationId, compCode));
    }

    public MasterBillerResponse getMasterBillerOrElseThrow(String correlationId, String compCode, String osVersion, String clientVersion, Supplier<TMBCommonException> exceptionSupplier) throws TMBCommonException {
        return feignClientHelper.executeRequestOrElseThrow(() -> paymentServiceClient.fetchMasterBillerByCompCode(correlationId, compCode, osVersion, clientVersion), exceptionSupplier);
    }

    public OCPBillPaymentResponse validateOCPBillPayment(String correlationId, String crmId, OCPBillRequest ocpBillRequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeRequest(() -> paymentServiceClient.validateOcpPayment(correlationId, crmId, ocpBillRequest));
    }

    public CommonPaymentToggle getCommonPaymentToggle(String correlationId) throws TMBCommonException {
        return feignClientHelper.executeRequest(() -> paymentServiceClient.getCommonPaymentToggle(correlationId));
    }

    public OCPBillPaymentResponse confirmOCPBillPayment(String correlationId, String crmId, String transactionId, OCPBillRequest ocpBillRequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeRequest(() -> paymentServiceClient.confirmOcpPayment(correlationId, crmId, transactionId, ocpBillRequest));
    }

    public OCPBillPaymentResponse confirmOCPBillWowPointPayment(String correlationId, String crmId, String transactionId, OCPBillRequest ocpBillRequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        try {
            return this.executeRequest(() -> paymentServiceClient.confirmOcpWowPointPayment(correlationId, crmId, transactionId, ocpBillRequest));
        } catch (TMBCommonExceptionWithResponse e) {
            if (e.getErrorMessage().contains("wow")) {
                throw new TMBCommonException(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), e.getErrorMessage(), ResponseCode.FAILED_V2.getService(), HttpStatus.OK, null);
            }
            throw e;
        }
    }

    public OCPBillPaymentResponse confirmOCPBillWowPointHomeLoanPayment(String correlationId, String crmId, String transactionId, OCPBillRequest ocpBillRequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeWowPointRequest(() -> paymentServiceClient.confirmOcpWowPointHomeLoanPayment(correlationId, crmId, transactionId, ocpBillRequest));
    }

    public TopUpETEResponse validateAutoLoanPayment(String correlationId, String crmId, TopUpETEPaymentRequest topUpETEPaymentRequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeRequest(() -> paymentServiceClient.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest));
    }

    public TopUpETEResponse confirmAutoLoanPayment(String correlationId, String crmId, String transactionId, TopUpETEPaymentRequest topUpETEPaymentRequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeRequest(() -> paymentServiceClient.confirmAutoLoanPayment(correlationId, crmId, transactionId, topUpETEPaymentRequest));
    }

    public AutoLoanOCPResponse confirmOCPBillWowPointAutoLoanPayment(String correlationId, String crmId, String transactionId, AutoLoanOCPBillRequest autoLoanOCPBillRequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeWowPointRequest(() -> paymentServiceClient.confirmOcpWowPointAutoLoanPayment(correlationId, crmId, transactionId, autoLoanOCPBillRequest));
    }

    public EWalletETEResponse validateEWalletPayment(String correlationId, String crmId, EWalletETERequest EWalletETERequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeRequest(() -> paymentServiceClient.validateEWalletPayment(correlationId, crmId, EWalletETERequest));
    }

    public EWalletETEResponse confirmEWalletPayment(String correlationId, String crmId, String transactionId, EWalletETERequest EWalletETERequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeRequest(() -> paymentServiceClient.confirmEWalletPayment(correlationId, crmId, transactionId, EWalletETERequest));
    }

    public CreditCardConfirmResponse confirmCreditCardPayment(String correlationId, String crmId, String transactionId, CreditCardConfirmRequest request) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeRequest(() -> paymentServiceClient.confirmCreditCardPayment(correlationId, crmId, transactionId, request));
    }

    public PromptPayETEValidateResponse validateBillPromptPayPayment(String correlationId, String crmId, PromptPayETEValidateRequest promptPayETEValidateRequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeRequest(() -> paymentServiceClient.validateBillPromptPayPayment(correlationId, crmId, promptPayETEValidateRequest));
    }

    public PromptPayETEValidateResponse validateBillPromptPayISO20022Payment(String correlationId, String crmId, PromptPayETEValidateRequest promptPayETEValidateRequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeRequest(() -> paymentServiceClient.validateBillPromptPayISO20022Payment(correlationId, crmId, promptPayETEValidateRequest));
    }

    public PromptPayETEConfirmResponse confirmBillPromptPayPayment(String correlationId, String crmId, String transactionId, PromptPayETEConfirmRequest promptPayETEConfirmRequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeRequest(() -> paymentServiceClient.confirmBillPromptPayPayment(correlationId, crmId, transactionId, promptPayETEConfirmRequest));
    }

    public PromptPayETEConfirmResponse confirmBillPromptPayISO20022Payment(String correlationId, String crmId, String transactionId, PromptPayETEConfirmRequest promptPayETEConfirmRequest) throws TMBCommonException, TMBCommonExceptionWithResponse {
        return this.executeRequest(() -> paymentServiceClient.confirmBillPromptPayISO20022Payment(correlationId, crmId, transactionId, promptPayETEConfirmRequest));
    }

    private <T> T executeRequest(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplierPayment) throws TMBCommonException, TMBCommonExceptionWithResponse {
        try {
            ResponseEntity<TmbServiceResponse<T>> responseEntity = supplierPayment.get();
            Status status = Objects.requireNonNull(responseEntity.getBody()).getStatus();
            boolean isStatusSuccess = status.getCode().equals(ResponseCode.SUCCESS_V2.getCode()) || status.getCode().equals(ResponseCode.SUCCESS.getCode());
            if (responseEntity.getStatusCode().is2xxSuccessful() && isStatusSuccess) {
                return Optional.ofNullable(responseEntity.getBody())
                        .map(TmbServiceResponse::getData)
                        .orElseThrow(() -> {
                            logger.error("Error got null responseEntity. Pleaser verify api paymentServiceClient.");
                            return CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
                        });
            }

            throw throwSpecificETEErrorCode(status);

        } catch (TMBCommonException | TMBCommonExceptionWithResponse e) {
            throw e;
        } catch (FeignException e) {
            logger.error(LOG_FEIGN_EXCEPTION_PAYMENT_SERVICE_ERROR_MESSAGE_PATTERN, "paymentServiceClient", "validateOcpPayment", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "FeignException occurred while fetching data");
        } catch (Exception e) {
            logger.error(LOG_EXCEPTION_PAYMENT_SERVICE_ERROR_MESSAGE_PATTERN, "paymentServiceClient", "validateOcpPayment", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Exception occurred while fetching data");
        }
    }

    private TMBCommonExceptionWithResponse throwSpecificETEErrorCode(Status errorStatus) {
        final String replaceErrorCode = ResponseCode.ETE_ERROR.getCode();
        final String message = String.format("%s : %s", errorStatus.getCode(), errorStatus.getMessage());

        final String titleKey = String.format("%s_title", errorStatus.getCode());
        final String descriptionKey = String.format("%s_desc", errorStatus.getCode());
        var externalErrorTitle = new ExternalErrorCustomDescription()
                .setCustomDescription(errorMappingHelper.get(descriptionKey))
                .setTitle(errorMappingHelper.get(titleKey));
        return new TMBCommonExceptionWithResponse(replaceErrorCode, message, ResponseCode.FAILED_V2.getService(), HttpStatus.OK, null, externalErrorTitle);
    }

    private <T> T executeWowPointRequest(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) throws TMBCommonException, TMBCommonExceptionWithResponse {
        try {
            return this.executeRequest(supplier);
        } catch (TMBCommonExceptionWithResponse e) {
            if (e.getErrorMessage().contains("wow")) {
                throw new TMBCommonException(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), e.getErrorMessage(), ResponseCode.FAILED_V2.getService(), HttpStatus.OK, null);
            }
            throw e;
        }
    }
}
