package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentRule;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.ProcessBillPayDataTemp;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.DEFAULT_PAYMENT_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.DEFAULT_PAYMENT_DEPOSIT;

@Component
public class BillPayPaymentMethodCommonPaymentHelper {
    public static final TMBLogger<BillPayPaymentMethodCommonPaymentHelper> logger = new TMBLogger<>(BillPayPaymentMethodCommonPaymentHelper.class);
    private final AccountCreditCardService accountCreditCardService;
    private final BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;

    public BillPayPaymentMethodCommonPaymentHelper(AccountCreditCardService accountCreditCardService, BillPayAccountCommonPaymentService billPayAccountCommonPaymentService) {
        this.accountCreditCardService = accountCreditCardService;
        this.billPayAccountCommonPaymentService = billPayAccountCommonPaymentService;
    }

    public ProcessBillPayDataTemp processBillPay(CommonPaymentRule commonPaymentRule, MasterBillerResponse masterBillerResponse, HttpHeaders headers) throws TMBCommonException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        validateDataProcessBillPay(commonPaymentRule, masterBillerResponse);

        List<String> defineDefaultPaymentForBillPay = getDefineDefaultPaymentForBillPay(commonPaymentRule, masterBillerResponse);

        ProcessBillPayDataTemp response = new ProcessBillPayDataTemp();
        for (String defaultPayment : defineDefaultPaymentForBillPay) {
            try {
                processGetAccountBillPayOrCreditCard(defaultPayment, correlationId, crmId, response);
                return response;
            } catch (TMBCommonException e) {
                logger.error("Error get account for bill pay. [default_payment = {}, defineDefaultPaymentForBillPay = {}]", defaultPayment, defineDefaultPaymentForBillPay);
            }
        }

        logger.error("Error cannot processBillPay got error all default_payment. [default_payment_from_common_payment_rule = {}, define_default_payment_for_bill_pay = {}]", commonPaymentRule.getDefaultPayment(), defineDefaultPaymentForBillPay.toString());
        return response;
    }

    private void processGetAccountBillPayOrCreditCard(String defaultPayment, String correlationId, String crmId, ProcessBillPayDataTemp response) throws TMBCommonException {
        if (isDepositDefaultPayment(defaultPayment)) {
            List<DepositAccount> billPayDepositAccountList = billPayAccountCommonPaymentService.getAccountList(correlationId, crmId);
            response.setDepositAccountList(billPayDepositAccountList);
        } else if (isCreditCardDefaultPayment(defaultPayment)) {
            List<CreditCardSupplementary> creditCardAccountList = accountCreditCardService.getCreditCardAccounts(correlationId, crmId);
            response.setCreditCardList(creditCardAccountList);
        } else {
            logger.error("Request type not match with default_payment, Please verify type or default_payment. [ type = is not define, default_payment = {}]", defaultPayment);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.FAILED_V2, "request type incorrect. please verify type or default_payment");
        }
    }

    private void validateDataProcessBillPay(CommonPaymentRule commonPaymentRule, MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        boolean isMissingMandatoryFields = ObjectUtils.anyNull(commonPaymentRule, commonPaymentRule.getDefaultPayment(), masterBillerResponse, masterBillerResponse.getBillerInfo());
        if (isMissingMandatoryFields) {
            logger.error("Error Missing mandatory field. Please verify common_payment_rule or master-biller. [CommonPaymentRule = {}, MasterBillerResponse = {]]", commonPaymentRule, masterBillerResponse);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.MISSING_CONFIGURATION_ERROR, "Missing mandatory field. Please verify config");
        }
    }

    private List<String> getDefineDefaultPaymentForBillPay(CommonPaymentRule commonPaymentRule, MasterBillerResponse masterBillerResponse) {
        String defaultPaymentFromRule = commonPaymentRule.getDefaultPayment();
        boolean isNotAllowToPayWithCreditCard = !masterBillerResponse.getBillerInfo().isCreditCardFlag();
        List<String> response;

        List<String> defineDefaultPaymentForBillPay = new ArrayList<>(List.of(DEFAULT_PAYMENT_DEPOSIT, DEFAULT_PAYMENT_CREDIT_CARD));

        response = moveDefaultPaymentFromRuleToFirstIndex(defineDefaultPaymentForBillPay, defaultPaymentFromRule);

        if (isNotAllowToPayWithCreditCard) {
            response.remove(DEFAULT_PAYMENT_CREDIT_CARD);
        }

        return response;
    }

    private List<String> moveDefaultPaymentFromRuleToFirstIndex(List<String> defineDefaultPaymentForBillPay, String defaultPaymentFromRule) {
        defineDefaultPaymentForBillPay.remove(defaultPaymentFromRule);
        defineDefaultPaymentForBillPay.add(0, defaultPaymentFromRule);

        return defineDefaultPaymentForBillPay;
    }

    private static boolean isDepositDefaultPayment(String defaultPayment) {
        return StringUtils.equalsAnyIgnoreCase(DEFAULT_PAYMENT_DEPOSIT, defaultPayment);
    }

    private static boolean isCreditCardDefaultPayment(String defaultPayment) {
        return StringUtils.equalsAnyIgnoreCase(DEFAULT_PAYMENT_CREDIT_CARD, defaultPayment);
    }
}
