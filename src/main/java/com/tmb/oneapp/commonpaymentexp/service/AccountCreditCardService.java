package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.client.RetailLendingBizClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CreditCardPointMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.CreditCardPoint;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardAccount;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardFormatedResponse;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.util.Calendar;
import java.util.Comparator;
import java.util.List;
import java.util.function.Predicate;

@Service
public class AccountCreditCardService {
    public static final TMBLogger<AccountCreditCardService> logger = new TMBLogger<>(AccountCreditCardService.class);
    public static final String DATE_INVALID_EMPTY = "0000-00-00";
    private static final Predicate<CreditCardAccount> isCartTypeNotSUP = c -> !StringUtils.equalsIgnoreCase("SUP", c.getCardType());
    private static final Predicate<CreditCardAccount> isAllowFromForBillPayTopUpEPaymentIsOne = e -> StringUtils.equals("1", e.getAllowFromForBillPayTopUpEpayment());
    private final FeignClientHelper feignClientHelper;
    private final RetailLendingBizClient retailLendingBizClient;
    private final CreditCardPointMapper creditCardPointMapper;

    public AccountCreditCardService(FeignClientHelper feignClientHelper, RetailLendingBizClient retailLendingBizClient, CreditCardPointMapper creditCardPointMapper) {
        this.feignClientHelper = feignClientHelper;
        this.retailLendingBizClient = retailLendingBizClient;
        this.creditCardPointMapper = creditCardPointMapper;
    }

    public List<CreditCardSupplementary> getCreditCardAccounts(String correlationId, String crmId) throws TMBCommonException {
        CreditCardFormatedResponse creditCardResponse = feignClientHelper.executeRequest(() -> retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null));

        if (creditCardResponse == null || creditCardResponse.getCreditCards() == null || creditCardResponse.getCreditCards().isEmpty()) {
            logger.error("Error Credit card account list is empty. [ crmId = {}]", crmId);
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.NO_ELIGIBLE_CREDIT_CARD_ACCOUNT_ERROR);
        }

        List<CreditCardSupplementary> response = creditCardResponse.getCreditCards().stream()
                .filter(isCartTypeNotSUP.and(isAllowFromForBillPayTopUpEPaymentIsOne).and(isCardActivated()))
                .sorted(sortCreditCard())
                .toList();

        if (CollectionUtils.isEmpty(response)) {
            logger.error("Error Credit card account list is empty after filter by credit-card condition. [ crmId = {}]", crmId);
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.NO_ELIGIBLE_CREDIT_CARD_ACCOUNT_ERROR);
        }

        return response;
    }

    public CreditCardSupplementary getCreditCardAccountWithAccountId(String correlationId, String crmId, String accountId) throws TMBCommonException {
        CreditCardFormatedResponse creditCardResponse = feignClientHelper.executeRequestOrElseThrow(
                () -> retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null),
                () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR)
        );

        Predicate<CreditCardAccount> isMatchingAccountId = e -> StringUtils.equals(accountId, e.getAccountId());

        return creditCardResponse.getCreditCards().stream()
                .filter(isMatchingAccountId.and(isCartTypeNotSUP).and(isAllowFromForBillPayTopUpEPaymentIsOne).and(isCardActivated()))
                .findFirst()
                .orElseThrow(() -> {
                    logger.error("Error Credit card account not matched. please verify crmId and accountId [ crmId = {}, accountId = {}]", crmId, accountId);
                    return CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR);
                });
    }

    public List<CreditCardPoint> getCardPointCreditCard(String correlationId, String crmId) throws TMBCommonException {
        List<CreditCardSupplementary> creditCardAccountList = getCreditCardAccounts(correlationId, crmId);
        return creditCardPointMapper.mapToCreditCardPointList(creditCardAccountList);

    }

    private Comparator<CreditCardSupplementary> sortCreditCard() {
        return (a1, a2) -> {
            String s1 = a1.getProductOrder() + "-" + a1.getCardNo();
            String s2 = a2.getProductOrder() + "-" + a2.getCardNo();
            return s1.compareTo(s2);
        };
    }

    private Predicate<CreditCardAccount> isCardActivated() {
        Predicate<CreditCardAccount> isCardStatusActive = c -> c.getCardStatus().getCardActiveFlag().equals("ACTIVE") && c.getCardStatus().getAccountStatus().equals("000");
        Predicate<CreditCardAccount> isPreviousExpiryDate = c -> {
            if (c.getCardStatus().getActivatedDate() == null || !c.getCardStatus().getActivatedDate().equals(DATE_INVALID_EMPTY)) {
                return true;
            }
            String chkYYMM = c.getCardStatus().getPreviousExpiryDate();
            Calendar cal = Calendar.getInstance();
            DecimalFormat mFormat = new DecimalFormat("00");
            int currNumberYYMM = Integer.parseInt(mFormat.format(Double.valueOf(cal.get(Calendar.YEAR))).substring(2, 4) + mFormat.format(Double.valueOf(cal.get(Calendar.MONTH))));
            currNumberYYMM += 1;
            int chkNumberYYMM = Integer.parseInt(chkYYMM);
            int result = chkNumberYYMM - currNumberYYMM;
            return result > 0;
        };

        return isCardStatusActive.and(isPreviousExpiryDate);
    }
}
