package com.tmb.oneapp.commonpaymentexp.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.KeyUse;
import com.nimbusds.jose.jwk.RSAKey;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.PublicKeyResponse;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.JoseUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
@RequiredArgsConstructor
public class CommonPaymentPartnerIntegrationService {

    private final JwkSetProvider jwkSetProvider;
    private final ObjectMapper objectMapper;
    private static final TMBLogger<CommonPaymentPartnerIntegrationService> logger = new TMBLogger<>(CommonPaymentPartnerIntegrationService.class);

    /**
     * Retrieves the public JWK set for a given partner and returns it as a JSON string.
     *
     * @param partnerName The identifier for the partner.
     * @return A {@link PublicKeyResponse} containing the JWK set as a JSON string.
     * @throws TMBCommonException if the JWKSet for the partner is not found or cannot be serialized.
     */
    public PublicKeyResponse getPublicKey(String partnerName) throws TMBCommonException {
        JWKSet jwkSet = jwkSetProvider.getJwkSet(partnerName);
        Map<String, Object> publicJwkSetMap = jwkSet.toJSONObject(true);
        return new PublicKeyResponse(publicJwkSetMap);
    }

    /**
     * Decrypts the JWE payload and deserializes it into an InitializationCommonPaymentRequest.
     *
     * @param encryptedPayload The JWE string.
     * @param partnerName      The identifier for the partner to find the correct decryption key.
     * @return The deserialized {@link InitializationCommonPaymentRequest}.
     * @throws TMBCommonException if decryption or deserialization fails.
     */
    public InitializationCommonPaymentRequest decryptInitialPayload(String encryptedPayload, String partnerName) throws TMBCommonException {
        JWKSet jwkSet = jwkSetProvider.getJwkSet(partnerName);
        String decryptedJson = JoseUtils.decrypt(encryptedPayload, jwkSet);
        try {
            return TMBUtils.convertStringToJavaObjWithTypeReference(decryptedJson, new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            throw CommonServiceUtils.getUnhandledTmbCommonException(
                    ResponseCode.PAYLOAD_DESERIALIZATION_FAILED,
                    "Partner sent incorrect payload format (Model initial request)"
            );
        }
    }
    /**
     * Signs the given payload using the partner's private key.
     *
     * @param partnerName The identifier for the partner.
     * @param payload     The payload to sign.
     * @return The JWS Compact Serialization string.
     * @throws TMBCommonException if signing fails or key is not found.
     */
    public String signData(String partnerName, Map<String, Object> payload) throws TMBCommonException {
        RSAKey privateKey = getPrivateKeyForSigning(partnerName);
        return JoseUtils.generateJws(privateKey, payload);
    }

    /**
     * Signs the TmbServiceResponse object using the partner's private key.
     *
     * @param partnerName The identifier for the partner.
     * @param response    The TmbServiceResponse object to sign.
     * @return The JWS Compact Serialization string.
     * @throws TMBCommonException if signing fails or key is not found.
     */
    public String signServiceResponse(String partnerName, TmbServiceResponse<?> response) throws TMBCommonException {
        try {
            Map<String, Object> responseMap = objectMapper.convertValue(response, new TypeReference<>() {});
            return signData(partnerName, responseMap);
        } catch (IllegalArgumentException e) {
            logger.error("Failed to convert TmbServiceResponse to Map for signing: {}", e.getMessage());
            throw CommonServiceUtils.getUnhandledTmbCommonException(
                    ResponseCode.PAYLOAD_SERIALIZATION_FAILED,
                    "Failed to sign response data"
            );
        }
    }

    /**
     * Retrieves the private RSA key for signing for a given partner.
     *
     * @param partnerName The identifier for the partner.
     * @return The RSAKey containing the private key.
     * @throws TMBCommonException if no suitable private key is found.
     */
    private RSAKey getPrivateKeyForSigning(String partnerName) throws TMBCommonException {
        JWKSet jwkSet = jwkSetProvider.getJwkSet(partnerName);

        java.util.List<RSAKey> privateKeys = jwkSet.getKeys().stream()
                .filter(JWK::isPrivate)
                .filter(k -> KeyUse.SIGNATURE.equals(k.getKeyUse()))
                .filter(k -> k instanceof RSAKey)
                .map(k -> (RSAKey) k)
                .toList();

        if (privateKeys.size() != 1) {
            String errorMessage = "Expected exactly one private RSA key for partner '" + partnerName + "', but found " + privateKeys.size();
            logger.error(errorMessage);
            throw CommonServiceUtils.getUnhandledTmbCommonException(
                    ResponseCode.JWK_KEY_NOT_FOUND,
                    errorMessage
            );
        }

        return privateKeys.get(0);
    }
}
