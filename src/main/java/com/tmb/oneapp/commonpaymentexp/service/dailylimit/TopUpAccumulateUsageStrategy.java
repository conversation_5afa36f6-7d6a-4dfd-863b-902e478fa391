package com.tmb.oneapp.commonpaymentexp.service.dailylimit;

import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.AccumulateUsageRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;

import java.math.BigDecimal;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNullOrDefault;

public class TopUpAccumulateUsageStrategy implements AccumulateUsageStrategy {
    private static final TMBLogger<TopUpAccumulateUsageStrategy> logger = new TMBLogger<>(TopUpAccumulateUsageStrategy.class);
    private final BigDecimal frPaymentAccumulateAmountLimit;


    public TopUpAccumulateUsageStrategy(BigDecimal frPaymentAccumulateAmountLimit) {
        this.frPaymentAccumulateAmountLimit = frPaymentAccumulateAmountLimit;
    }

    @Override
    public AccumulateUsageRequest createRequest(CommonPaymentDraftCache draftCache, CustomerCrmProfile profile) {

        AccumulateUsageRequest request = new AccumulateUsageRequest();

        BigDecimal totalAmount = getSafeNullOrDefault(() -> draftCache.getValidateRequest().getDeposit().getAmount(), BigDecimal.ZERO);

        BigDecimal totalPaymentAccumulateUsage = Optional.ofNullable(profile.getPaymentAccuUsgAmt())
                .orElse(BigDecimal.ZERO)
                .add(totalAmount);

        boolean isExceedLimitMustSetZero = totalPaymentAccumulateUsage.compareTo(frPaymentAccumulateAmountLimit) >= 0;
        if (isExceedLimitMustSetZero) {
            logger.info("Reset payment accumulate to 0: totalAmount={}, fr payment accumulate amount limit={}", totalPaymentAccumulateUsage, frPaymentAccumulateAmountLimit);
            request.setPaymentAccumulateUsageAmount(BigDecimal.ZERO);
        } else {
            request.setPaymentAccumulateUsageAmount(totalPaymentAccumulateUsage);
        }
        request.setDailyAccumulateUsageAmount(BigDecimal.valueOf(profile.getEbAccuUsgAmtDaily()).add(totalAmount));

        return request;
    }
}
