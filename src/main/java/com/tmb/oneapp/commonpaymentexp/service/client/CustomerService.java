package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.customer.AccumulateUsageRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.model.customer.PinFreeCountData;
import com.tmb.oneapp.commonpaymentexp.model.notification.ENotificationSettingResponse;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CustomerService {
    private static final TMBLogger<CustomerService> logger = new TMBLogger<>(CustomerService.class);
    private final CustomerServiceClient customerServiceClient;
    private final FeignClientHelper feignClientHelper;

    public CustomerCrmProfile getCustomerCrmProfile(String correlationId, String crmId) throws TMBCommonException {
        logger.info(">>>> fetch crm profile from customer service");
        return feignClientHelper.executeRequestOrElseThrow(() -> customerServiceClient.fetchCustomerCrmProfile(correlationId, crmId), () -> CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2));
    }

    public String updateUsageAccumulation(String correlationId, String crmId, AccumulateUsageRequest accumulateUsageRequest) throws TMBCommonException {
        logger.info(">>>> update usage accumulation from customer service");
        logger.debug("correlation id : [{}] crm id : [{}] accumulate Usage Request : [{}]", correlationId, crmId, accumulateUsageRequest);
        return feignClientHelper.executeRequestOrElseThrow(() -> customerServiceClient.updateUsageAccumulation(correlationId, crmId, accumulateUsageRequest), () -> CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2));
    }

    public void updatePinFreeCount(String crmId, int count, String correlationId) throws TMBCommonException {
        logger.info(">>>> update update pin free count from customer service");
        logger.debug("correlation id : [{}] crm id : [{}] count : [{}]", correlationId, crmId, count);
        feignClientHelper.executeRequestOrElseThrow(() -> customerServiceClient.updatePinFreeCount(crmId, correlationId, new PinFreeCountData(count)), () -> CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2));
    }

    public ENotificationSettingResponse getENotificationSetting(String crmId, String correlationId) {
        logger.info(">>> get e notification setting from customer service <<<");
        logger.debug("correlation id : [{}] crm id : [{}]", correlationId, crmId);
        return feignClientHelper.executeRequestSafely(() -> customerServiceClient.getENotificationSetting(correlationId, crmId));
    }

    public CustomerKYCResponse getCustomerKYC(String correlationId, String crmId) throws TMBCommonException {
        logger.info(">>> get customer kyc from customer service <<<");
        logger.debug("correlation id : [{}] crm id : [{}]", correlationId, crmId);
        return feignClientHelper.executeRequestOrElseThrow(() -> customerServiceClient.fetchCustomerKYC(correlationId, crmId), () -> CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2));
    }
}
