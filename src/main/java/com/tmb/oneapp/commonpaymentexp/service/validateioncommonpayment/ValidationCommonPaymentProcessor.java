package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import org.springframework.http.HttpHeaders;

public interface ValidationCommonPaymentProcessor {

    String getProcessorType();

    ValidationCommonPaymentResponse executeValidate(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException, TMBCommonExceptionWithResponse;
}