package com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitialPrepareData;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils;
import com.tmb.oneapp.commonpaymentexp.validator.CommonPaymentConfigValidator;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNullOrDefault;

@Component("partnerInitializationValidatorImpl")
@RequiredArgsConstructor
public class PartnerInitializationValidatorImpl implements InitializationValidator {
    private static final TMBLogger<PartnerInitializationValidatorImpl> logger = new TMBLogger<>(PartnerInitializationValidatorImpl.class);

    @Override
    public void validateData(InitializationCommonPaymentRequest initializationCommonPaymentRequest, HttpHeaders headers, InitialPrepareData prepareData) throws TMBCommonException {
        final CommonPaymentConfig commonPaymentConfig = NullSafeUtils.requireNonNull(prepareData::getCommonPaymentConfig, "common_payment_config should not be null, Please verify common_payment_config", HttpStatus.INTERNAL_SERVER_ERROR);
        final boolean enableCallbackUrl = getSafeNullOrDefault(() -> initializationCommonPaymentRequest.getPaymentInformation().getCallback().isEnableCallbackFlag(), false);
        logger.debug("Partner validateData. [enableCallbackUrl = {}, requestCallback = {}]", enableCallbackUrl, initializationCommonPaymentRequest.getPaymentInformation().getCallback());

        CommonPaymentConfigValidator.validateNotExpired(commonPaymentConfig);

        if (enableCallbackUrl) {
            validateCallbackUrl(initializationCommonPaymentRequest, commonPaymentConfig);
        }
    }

    private void validateCallbackUrl(InitializationCommonPaymentRequest initializationCommonPaymentRequest, CommonPaymentConfig commonPaymentConfig) throws TMBCommonException {
        final List<String> configCallbackUrls = NullSafeUtils.requireNonNull(commonPaymentConfig::getCallbackUrls, "configCallbackUrls should not be null when enableCallbackUrl is true", HttpStatus.INTERNAL_SERVER_ERROR);
        final String callbackUrlRequest = NullSafeUtils.requireNonNull(() -> initializationCommonPaymentRequest.getPaymentInformation().getCallback().getCallbackUrl(), "callbackUrlRequest should not be null when enableCallbackUrl is true");

        boolean isCorrectCallbackUrl = configCallbackUrls.contains(callbackUrlRequest);
        boolean isNotCorrectCallbackUrl = !isCorrectCallbackUrl;

        if (isNotCorrectCallbackUrl) {
            logger.error("Invalid request callback url, Not contains in config [ request.callbackUrl = {}, compCode = {}, allowedCallbackUrls = {}]", callbackUrlRequest, initializationCommonPaymentRequest.getPaymentInformation().getCompCode(), configCallbackUrls);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.INVALID_CALLBACK_URL_ERROR);
        }
    }
}
