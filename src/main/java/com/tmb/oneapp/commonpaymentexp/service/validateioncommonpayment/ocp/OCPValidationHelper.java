package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.AccountCreditCardService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;

@Component
@RequiredArgsConstructor
public class OCPValidationHelper {
    public static final TMBLogger<OCPValidationHelper> logger = new TMBLogger<>(OCPValidationHelper.class);
    private final AsyncHelper asyncHelper;
    private final PaymentService paymentService;
    private final AccountCreditCardService accountCreditCardService;
    private final CustomerService customerService;
    private final BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;

    public BillPayPrepareDataValidate basePrepareData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String compCode = cache.getPaymentInformation().getCompCode();
        final boolean isPayWithCreditCard = request.getCreditCard().isPayWithCreditCardFlag();
        BillPayPrepareDataValidate prepareData = new BillPayPrepareDataValidate();

        CompletableFuture<BillPayConfiguration> billPayConfigFuture = asyncHelper.executeMethodAsync(() -> paymentService.getBillPayConfig(correlationId));
        CompletableFuture<MasterBillerResponse> masterBillerFuture = asyncHelper.executeMethodAsync(() -> paymentService.getMasterBiller(correlationId, compCode));
        CompletableFuture<CustomerCrmProfile> customerCrmProfileFuture = asyncHelper.executeMethodAsync(() -> customerService.getCustomerCrmProfile(correlationId, crmId));
        CompletableFuture<CreditCardSupplementary> creditCardDetailFuture = CompletableFuture.completedFuture(null);
        CompletableFuture<DepositAccount> depositAccountFuture = CompletableFuture.completedFuture(null);
        if (isPayWithCreditCard) {
            creditCardDetailFuture = asyncHelper.executeMethodAsync(() -> accountCreditCardService.getCreditCardAccountWithAccountId(correlationId, crmId, request.getCreditCard().getAccountId()));
        } else {
            depositAccountFuture = asyncHelper.executeMethodAsync(() -> billPayAccountCommonPaymentService.getAccountByAccountNumber(request.getDeposit().getAccountNumber(), headers));
        }

        CompletableFuture.allOf(billPayConfigFuture, masterBillerFuture, depositAccountFuture, creditCardDetailFuture);
        try {
            prepareData.setBillPayConfiguration(billPayConfigFuture.get());
            prepareData.setMasterBillerResponse(masterBillerFuture.get());
            prepareData.setCustomerCrmProfile(customerCrmProfileFuture.get());
            if (isPayWithCreditCard) {
                prepareData.setFromCreditCardDetail(creditCardDetailFuture.get());
            } else {
                prepareData.setFromDepositAccount(depositAccountFuture.get());
            }
        } catch (ExecutionException | InterruptedException | ThreadDeath e) {
            if (e.getCause() instanceof TMBCommonException ex) {
                logger.error("Error ExecutionException TMBCommonException in method PrepareData : {}", ex.getMessage(), e);
                throw ex;
            } else {
                if (e instanceof ExecutionException && e.getCause() instanceof InterruptedException) {
                    Thread.currentThread().interrupt();
                }
            }

            logger.error("Error ExecutionException in method PrepareData", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Error execution exception get prepare data. Please verify prepareData method");
        }

        return prepareData;
    }
}
