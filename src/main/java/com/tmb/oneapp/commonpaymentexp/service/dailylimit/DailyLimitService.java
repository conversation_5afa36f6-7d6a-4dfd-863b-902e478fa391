package com.tmb.oneapp.commonpaymentexp.service.dailylimit;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BillerGroupTypeEnum;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.DailyUsageLimit;
import com.tmb.oneapp.commonpaymentexp.model.customer.AccumulateUsageRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class DailyLimitService implements DailyLimitValidator, AccumulateUsageUpdater {
    private static final TMBLogger<DailyLimitService> logger = new TMBLogger<>(DailyLimitService.class);
    private final CustomerService customerService;
    @Value("${fr.payment.accumulate.usage.limit:200000}")
    private Integer frPaymentAccumulateAmountLimit;
    private Map<BillerGroupTypeEnum, DailyLimitStrategy> dailyLimitStrategies;
    private Map<BillerGroupTypeEnum, AccumulateUsageStrategy> accumulateUsageStrategies;

    @PostConstruct
    public void init() {
        dailyLimitStrategies = Map.of(
                BillerGroupTypeEnum.TOPUP, new TopUpDailyLimitStrategy(),
                BillerGroupTypeEnum.BILL_PAY, new BillPayDailyLimitStrategy()
        );

        accumulateUsageStrategies = Map.of(
                BillerGroupTypeEnum.TOPUP, new TopUpAccumulateUsageStrategy(BigDecimal.valueOf(frPaymentAccumulateAmountLimit)),
                BillerGroupTypeEnum.BILL_PAY, new BillPayAccumulateUsageStrategy()
        );
    }

    @Override
    public void validateDailyLimitExceeded(String billerGroupType, CustomerCrmProfile profile, BigDecimal amount) throws TMBCommonException {
        try {
            BillerGroupTypeEnum billerGroupTypeEnum = BillerGroupTypeEnum.fromValue(Integer.parseInt(billerGroupType));
            DailyLimitStrategy strategy = getDailyLimitStrategy(billerGroupTypeEnum);

            DailyUsageLimit limit = strategy.calculateLimit(profile);
            BigDecimal totalUsage = calculateTotalUsage(amount, limit.getCurrentUsage());

            validateLimitNotExceeded(totalUsage, limit.getMaxLimit());

            logger.info("Daily limit validation passed. Entry: {}, Total: {}, Max: {}",
                    billerGroupTypeEnum, totalUsage, limit.getMaxLimit());

        } catch (TMBCommonException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error validating daily limit", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
        }
    }

    @Override
    public void updateAccumulateUsage(CommonPaymentDraftCache draftCache, CustomerCrmProfile profile, String crmId, String correlationId) {
        if (isNotEligibleForAccumulation(draftCache)) {
            logger.debug("Pay with credit card skip update accumulate usage!!");
            return;
        }

        try {
            BillerGroupTypeEnum billerGroupTypeEnum = BillerGroupTypeEnum.fromValue(Integer.parseInt(draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType()));
            AccumulateUsageStrategy strategy = getAccumulateUsageStrategy(billerGroupTypeEnum);

            AccumulateUsageRequest request = strategy.createRequest(draftCache, profile);
            customerService.updateUsageAccumulation(correlationId, crmId, request);

            logger.info("Successfully updated accumulate usage for entry: {}", billerGroupTypeEnum);
        } catch (Exception e) {
            logger.error("Failed to update accumulate usage", e);
        }
    }

    private DailyLimitStrategy getDailyLimitStrategy(BillerGroupTypeEnum billerGroupTypeEnum) {
        return Optional.ofNullable(dailyLimitStrategies.get(billerGroupTypeEnum))
                .orElseThrow(() -> new IllegalArgumentException("No strategy found for: " + billerGroupTypeEnum));
    }

    private AccumulateUsageStrategy getAccumulateUsageStrategy(BillerGroupTypeEnum billerGroupTypeEnum) {
        return Optional.ofNullable(accumulateUsageStrategies.get(billerGroupTypeEnum))
                .orElseThrow(() -> new IllegalArgumentException("No strategy found for: " + billerGroupTypeEnum));
    }

    private void validateLimitNotExceeded(BigDecimal totalUsage, BigDecimal maxLimit) throws TMBCommonException {
        if (totalUsage.compareTo(maxLimit) > 0) {
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.DAILY_LIMIT_EXCEEDED);
        }
    }

    private boolean isNotEligibleForAccumulation(CommonPaymentDraftCache draftCache) {
        return draftCache.getValidateRequest().getCreditCard().isPayWithCreditCardFlag();
    }

    private BigDecimal calculateTotalUsage(BigDecimal amount, BigDecimal currentUsage) {
        return amount.add(currentUsage);
    }
}

