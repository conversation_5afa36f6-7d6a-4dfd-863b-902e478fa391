package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.client.CustomersTransactionFeignClient;
import com.tmb.oneapp.commonpaymentexp.model.customertransaction.TriggerCacheRequest;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CustomersTransactionService {
    private static final TMBLogger<CustomersTransactionService> logger = new TMBLogger<>(CustomersTransactionService.class);
    private final CustomersTransactionFeignClient customersTransactionFeignClient;
    public static final String CHANNEL_NAME_PB = "pb";
    public static final String PRODUCT_GROUP_DEPOSIT = "deposit";

    public boolean clearDepositCache(String correlationId, String crmId) {
        TriggerCacheRequest triggerCacheRequest = new TriggerCacheRequest();
        try {
            triggerCacheRequest.setCrmId(crmId);
            triggerCacheRequest.setChannelName(CHANNEL_NAME_PB);
            triggerCacheRequest.setProductGroup(PRODUCT_GROUP_DEPOSIT);
            customersTransactionFeignClient.clearCache(correlationId, triggerCacheRequest);
            logger.info("Clear deposit cache : Success. [crmId = {}]", crmId);
            return true;
        } catch (FeignException e) {
            logger.error("Clear deposit cache : Failed, customersTransactionFeignClient.clearCache. [crmId = {}, request = {}]", crmId, triggerCacheRequest, e);
        }
        return false;
    }
}
