package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ocp;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityBillPayOCPConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BaseConfirmDataAfterConfirmExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponseMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.OCPLogsConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialOCPBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityOCPBillPay;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.client.LoyaltyBizService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.BaseConfirmServiceHelper;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.CommonValidateConfirmationService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ConfirmationProcessingTemplate;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.DateUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.UUID;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_TOP_UP;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_TOP_UP;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNull;

@Service
@RequiredArgsConstructor
public class OnlineOfflineTopUpOCPBillPaymentConfirmServiceProcessor extends ConfirmationProcessingTemplate<
        OCPBillPayment,
        BasePrepareDataConfirm,
        BaseConfirmDataAfterConfirmExternal,
        OCPLogsConfirm> {
    private static final TMBLogger<OnlineOfflineTopUpOCPBillPaymentConfirmServiceProcessor> logger = new TMBLogger<>(OnlineOfflineTopUpOCPBillPaymentConfirmServiceProcessor.class);
    private final CommonValidateConfirmationService commonValidateConfirmationService;
    private final PaymentService paymentService;
    private final BaseConfirmServiceHelper baseConfirmServiceHelper;
    private final AsyncHelper asyncHelper;
    private final LogEventPublisherService logEventPublisherService;
    private final LoyaltyBizService loyaltyBizService;

    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP;
    }

    @Override
    protected OCPLogsConfirm initialLogs(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache) {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionTime = String.valueOf(System.currentTimeMillis());
        final String ePayCode = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getEpayCode();

        return new OCPLogsConfirm()
                .setActivityBillPayOCPConfirmationEvent(new ActivityBillPayOCPConfirmationEvent(ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID, headers, draftCache))
                .setFinancialOCPBillPayActivityLog(new FinancialOCPBillPayActivityLog(crmId, ePayCode, draftCache, correlationId, transactionTime))
                .setTransactionActivityOCPBillPay(new TransactionActivityOCPBillPay(ePayCode, crmId, draftCache, transactionTime))
                .setActivityCustomSlipCompleteEvent(new ActivityCustomSlipCompleteEvent(headers, request.getCustomSlip(), isTopUpTransaction(draftCache) ? CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_TOP_UP : CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_BILL));
    }

    private boolean isTopUpTransaction(CommonPaymentDraftCache draftCache) {
        var masterBiller = draftCache.getValidateDraftCache().getMasterBillerResponse();

        return StringUtils.equalsAnyIgnoreCase(BILLER_GROUP_TYPE_TOP_UP, getSafeNull(() -> masterBiller.getBillerInfo().getBillerGroupType()));
    }

    @Override
    protected BasePrepareDataConfirm prepareData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, OCPLogsConfirm logEvents) throws TMBCommonException {
        return baseConfirmServiceHelper.getBasePrepareDataConfirm(headers, logEvents.getFinancialOCPBillPayActivityLog().getTxnDt());
    }

    @Override
    protected BasePrepareDataConfirm validateData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException {
        commonValidateConfirmationService.baseValidateData(request, headers, draftCache, prepareData);

        return prepareData;
    }

    @Override
    protected OCPBillPayment confirmWithExternalService(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionId = request.getTransactionId();
        final String transactionTimeFromFinLog = prepareData.getTransactionTime();
        final String overrideRequestDateTime = DateUtils.formatTimestampToISO(transactionTimeFromFinLog);
        final String overrideUUID = UUID.randomUUID().toString();
        final OCPBillRequest originalOcpBillConfirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest();
        OCPBillPaymentResponse ocpBillPaymentResponse;

        OCPBillRequest modifyOcpBillConfirmRequest = originalOcpBillConfirmRequest.toBuilder()
                .requestId(overrideUUID)
                .requestDateTime(overrideRequestDateTime)
                .build();
        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            WowPointRedeemConfirmRequest redeemRequest = draftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest();
            loyaltyBizService.redeemPoint(headers, redeemRequest);

            ocpBillPaymentResponse = paymentService.confirmOCPBillWowPointPayment(correlationId, crmId, transactionId, modifyOcpBillConfirmRequest);
        } else {
            ocpBillPaymentResponse = paymentService.confirmOCPBillPayment(correlationId, crmId, transactionId, modifyOcpBillConfirmRequest);
        }
        return ocpBillPaymentResponse.getData();
    }

    @Override
    protected BaseConfirmDataAfterConfirmExternal processAfterConfirmWithExternal(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPBillPayment externalResponse) {
        return baseConfirmServiceHelper.baseConfirmDataAfterConfirmExternal(request, headers, draftCache, prepareData);
    }

    @Override
    protected void saveSuccessLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPBillPayment externalResponse, OCPLogsConfirm logEvents, BaseConfirmDataAfterConfirmExternal processAfterConfirmResult) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final ActivityBillPayOCPConfirmationEvent activityLog = logEvents.getActivityBillPayOCPConfirmationEvent();
        final ActivityCustomSlipCompleteEvent customSlipLog = logEvents.getActivityCustomSlipCompleteEvent();
        final String ledgerBal = externalResponse.getAccount().getLedgerBal();
        final TransactionActivityOCPBillPay transactionActivityLog = logEvents.getTransactionActivityOCPBillPay();

        FinancialOCPBillPayActivityLog financialLog = logEvents.getFinancialOCPBillPayActivityLog();
        financialLog.setTxnBal(ledgerBal);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog, customSlipLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));
    }

    @Override
    protected ConfirmationCommonPaymentResponse mappingResponse(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPBillPayment externalResponse) {
        return ConfirmationCommonPaymentResponseMapper.INSTANCE.mapToOCPConfirmationCommonPaymentResponse(draftCache, prepareData, externalResponse);
    }

    @Override
    protected TMBCommonException handleException(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPLogsConfirm logEvents, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process OnlineOfflineTopUpOCPConfirmation. : {}", e.getMessage());
        return baseConfirmServiceHelper.baseHandleException(request, e);
    }

    @Override
    protected void saveFailedLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPLogsConfirm logEvents, Exception e) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        var financialLog = logEvents.getFinancialOCPBillPayActivityLog();
        var transactionActivityLog = logEvents.getTransactionActivityOCPBillPay();
        var activityLog = logEvents.getActivityBillPayOCPConfirmationEvent();
        financialLog.setFailureStatusWithErrorCodeFromException(e);
        transactionActivityLog.setTransactionStatus(ACTIVITY_FAILURE);
        activityLog.setFailureStatusWithReasonFromException(e);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
    }
}