package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp.custombillpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.BillPayValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.PEABill;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.PEAValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponseMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp.OCPValidationHelper;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.Y_ALPHABET;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_PEA;

@Service
public class PEABillPayValidationProcessor extends CustomBillPayValidationProcessor {
    private static final TMBLogger<PEABillPayValidationProcessor> logger = new TMBLogger<>(PEABillPayValidationProcessor.class);
    private static final String BILL_PAYMENT_ADDITIONAL_PARAM_GRAND_AMT = "GrandAmt";
    private static final String BILL_PAYMENT_ADDITIONAL_PARAM_OVERDUE = "OverDue";
    private static final String BILL_PAYMENT_ADDITIONAL_PARAM_MSG = "Msg";

    public PEABillPayValidationProcessor(PaymentService paymentService, LogEventPublisherService logEventPublisherService, CacheService cacheService, DailyLimitPinFreeValidator dailyLimitPinFreeValidator, BaseBillPayValidator baseBillPayValidator, OCPValidationHelper ocpValidationHelper, CacheMapper cacheMapper, OCPBillRequestMapper ocpBillRequestMapper) {
        super(paymentService, logEventPublisherService, cacheService, dailyLimitPinFreeValidator, baseBillPayValidator, ocpValidationHelper, cacheMapper, ocpBillRequestMapper);
    }


    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_PEA;
    }

    @Override
    protected OCPBillRequest createOCPBillPaymentValidateRequest(ValidationCommonPaymentRequest request, DepositAccount fromDepositAccount, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, CreditCardSupplementary creditCardDetail) {
        return ocpBillRequestMapper.toOCPBillRequestForValidatePEAOCPBillPayment(request, fromDepositAccount, cache, creditCardDetail);
    }

    @Override
    protected void validateAmount(ValidationCommonPaymentRequest request, OCPBillPayment ocpDataResponse) throws TMBCommonException {
        Map<String, String> params = ocpDataResponse
                .getAdditionalParams()
                .stream()
                .collect(Collectors.toMap(
                        AdditionalParam::getName,
                        param -> String.valueOf(param.getValue()),
                        (existing, replacement) -> existing
                ));
        String grandAmtStr = params.get(BILL_PAYMENT_ADDITIONAL_PARAM_GRAND_AMT);
        if (grandAmtStr == null) {
            logger.error("Error grand amount is null. Please verify additional params from OCP response. [params = {}]", params);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.GRAND_AMOUNT_NOT_FOUND_ERROR, "grand amount is null. Please verify additional params from OCP response");
        }
        BigDecimal grandAmt = new BigDecimal(grandAmtStr);
        BigDecimal requestAmount = request.getAmount();
        if (grandAmt.compareTo(requestAmount) != 0) {
            logger.error("Error amount not match. Please verify amount in request and response. [request.amount = {}, grand.amount = {}]", requestAmount, grandAmt);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.PEA_AMOUNT_NOT_MATCH_ERROR);
        }
    }

    @Override
    protected ValidationCommonPaymentResponse mappingResponse(ValidationCommonPaymentRequest request,
                                                              HttpHeaders headers,
                                                              CommonPaymentDraftCache cache,
                                                              BillPayPrepareDataValidate prepareData,
                                                              BillPayExternalValidateResponse externalResponse,
                                                              BillPayValidateDataAfterCallExternal validateDataAfterCallExternal) {

        ValidationCommonPaymentResponse response = ValidationCommonPaymentResponseMapper.INSTANCE.mapToValidationCommonPaymentResponseForPEA(request, cache, externalResponse, validateDataAfterCallExternal);

        PEAValidationCommonPaymentResponse peaResponse = buildPEAResponse(externalResponse.getOcpDataResponse());

        response.setPeaValidationCommonPaymentResponse(peaResponse);
        return response;
    }

    private PEAValidationCommonPaymentResponse buildPEAResponse(OCPBillPayment ocpDataResponse) {
        Map<String, String> params = ocpDataResponse.getAdditionalParams().stream()
                .collect(Collectors.toMap(
                        AdditionalParam::getName,
                        param -> String.valueOf(param.getValue()),
                        (existing, replacement) -> existing
                ));

        boolean isOverDue = StringUtils.equalsAnyIgnoreCase(
                params.getOrDefault(BILL_PAYMENT_ADDITIONAL_PARAM_OVERDUE, null),
                Y_ALPHABET);

        String msg = params.getOrDefault(BILL_PAYMENT_ADDITIONAL_PARAM_MSG, null);

        return new PEAValidationCommonPaymentResponse()
                .setOverDue(isOverDue)
                .setBill(getBillPerMonth(msg));
    }

    private List<PEABill> getBillPerMonth(String billOfBiller) {
        String[] bills = billOfBiller.split("\\|");
        int months = Integer.parseInt(bills[0]);

        List<PEABill> billPerMonths = new ArrayList<>();
        for (int i = 0; i < months; i++) {
            PEABill billPerMonth = new PEABill();
            String month = bills[i * 4 + 2];
            String eletricityOf = "";
            if (!month.isBlank()) {
                eletricityOf = month.substring(4) + "/" + month.substring(0, 4);
            }

            billPerMonth.setEletricityOf(eletricityOf);

            String vat = bills[i * 4 + 3];
            BigDecimal vatDouble = new BigDecimal(vat);
            billPerMonth.setVat(vatDouble.setScale(2, RoundingMode.HALF_UP).toString());

            String amount = bills[i * 4 + 4];
            BigDecimal amountDouble = new BigDecimal(amount);
            billPerMonth.setAmount(amountDouble.setScale(2, RoundingMode.HALF_UP).toString());

            billPerMonths.add(billPerMonth);
        }

        return billPerMonths;
    }
}
