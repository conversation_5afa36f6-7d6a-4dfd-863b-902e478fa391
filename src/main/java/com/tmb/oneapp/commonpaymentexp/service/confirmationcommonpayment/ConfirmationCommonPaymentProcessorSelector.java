package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
public class ConfirmationCommonPaymentProcessorSelector {

    private static final TMBLogger<ConfirmationCommonPaymentProcessorSelector> logger = new TMBLogger<>(ConfirmationCommonPaymentProcessorSelector.class);
    private final Map<String, ConfirmationCommonPaymentProcessor> processorMap = new HashMap<>();

    @Autowired
    public ConfirmationCommonPaymentProcessorSelector(List<ConfirmationCommonPaymentProcessor> processors) {
        for (ConfirmationCommonPaymentProcessor processor : processors) {
            processorMap.put(processor.getProcessorType().toLowerCase(), processor);
        }
    }

    /**
     * Get processor service by processorType match with processor key
     *
     * @param processorType value in cache. (PaymentInformation.processorType)
     * @return Processor of processorType service like BillPayValidationProcessor (when processorType = biller_payment_online_offline_topup)
     */
    public ConfirmationCommonPaymentProcessor getProcessor(String processorType) {
        return processorMap.get(processorType.toLowerCase());
    }

    /**
     * This processor allows transactions to be matched with the key:
     * <pre>
     *     COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY = "bill_pay"
     * </pre>
     * The method validates that the transaction type matches the key of the processor.
     *
     * @param transactionType the value in the cache (PaymentInformation.transactionType).
     * @throws TMBCommonException if the transactionType does not match the key of the processor, a MISSING_REQUIRED_FIELD_ERROR is thrown.
     */
    public void validateTransactionType(String transactionType) throws TMBCommonException {
        boolean isTransactionTypeNotMatch = !processorMap.containsKey(transactionType.toLowerCase());
        if (isTransactionTypeNotMatch) {
            logger.error("Error transaction_type incorrect. [ transactionType = {}, [allow_transaction_type = {}]]", transactionType, getAllKeys());
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.MISSING_REQUIRED_FIELD_ERROR, "Please verify request transaction_type when Initial process");
        }
    }

    private Set<String> getAllKeys() {
        return processorMap.keySet();
    }
}
