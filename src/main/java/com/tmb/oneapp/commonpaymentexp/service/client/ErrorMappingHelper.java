package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.model.Description;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ErrorMappingHelper {
    private final Map<String, Description> errorMapping; // Bean created on Start service by Common-utility

    public ErrorMappingHelper(@Qualifier("errorMapping") Map<String, Description> errorMapping) {
        this.errorMapping = errorMapping;
    }

    public Description get(String key) {
        return errorMapping.get(key);
    }
}
