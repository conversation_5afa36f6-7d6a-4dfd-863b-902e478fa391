package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.BaseEvent;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.TransactionActivities;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


@Service
public class LogEventPublisherService {
    private static final TMBLogger<LogEventPublisherService> logger = new TMBLogger<>(LogEventPublisherService.class);
    private final KafkaProducerService kafkaProducerService;
    private final String topicNameActivity;
    private final String topicNameFinancial;
    private final String topicNameTransaction;

    public LogEventPublisherService(KafkaProducerService kafkaProducerService,
                                    @Value("${com.tmb.oneapp.common.payment.exp.activity}") final String topicNameActivity,
                                    @Value("${com.tmb.oneapp.common.payment.exp.financial}") final String topicNameFinancial,
                                    @Value("${com.tmb.oneapp.common.payment.exp.transaction}") final String topicNameTransaction) {
        this.kafkaProducerService = kafkaProducerService;
        this.topicNameActivity = topicNameActivity;
        this.topicNameFinancial = topicNameFinancial;
        this.topicNameTransaction = topicNameTransaction;
    }

    @LogAround
    public void saveActivityLog(BaseEvent... events) {
        for (BaseEvent event : events) {
            processSaveActivityLog(event);
        }
    }

    private void processSaveActivityLog(BaseEvent event) {
        try {
            String eventString = TMBUtils.convertJavaObjectToString(event);
            logger.debug("=== START save Activity log. [payload = {}]", eventString);
            kafkaProducerService.sendMessageAsync(topicNameActivity, eventString);
            logger.debug("Save activity log - Success", System.currentTimeMillis());
        } catch (Exception e) {
            logger.debug("Error save Activity log", e);
        } finally {
            logger.debug("=== END save Activity log data posted to kafka. {time = {}}", System.currentTimeMillis());

        }
    }

    @LogAround
    public void saveFinancialLog(String correlationId, FinancialRequest financialRequest) {
        try {
            logger.debug("=== START save Financial log. [fin.referenceID = {}, correlationId = {}]", financialRequest.getReferenceID(), correlationId);
            String financialLog = TMBUtils.convertJavaObjectToString(financialRequest);
            kafkaProducerService.sendMessageAsync(topicNameFinancial, financialLog);
            logger.debug("Save Financial log - Success", System.currentTimeMillis());
        } catch (Exception e) {
            logger.debug("Error Save Financial log", e);
        } finally {
            logger.debug("=== END save Financial log data posted to kafka. {time = {}}", System.currentTimeMillis());
        }
    }

    @LogAround
    public void saveTransactionLog(String correlationId, TransactionActivities transactionActivityEvent) {
        try {
            logger.debug("=== START save Transaction log. [transaction.financialTransferRefId = {}, correlationId = {}]", transactionActivityEvent.getFinancialTransferRefId(), correlationId);
            String transactionLog = TMBUtils.convertJavaObjectToString(transactionActivityEvent);
            kafkaProducerService.sendMessageAsync(topicNameTransaction, transactionLog);
            logger.debug("Save Transaction log - Success", System.currentTimeMillis());
        } catch (Exception ex) {
            logger.debug("Error Save Transaction log", ex);
        } finally {
            logger.debug("=== END save Transaction log data posted to kafka. {time = {}}", System.currentTimeMillis());

        }
    }

}
