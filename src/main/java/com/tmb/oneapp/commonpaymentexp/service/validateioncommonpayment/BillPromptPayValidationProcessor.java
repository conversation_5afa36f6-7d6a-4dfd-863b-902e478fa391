package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayMapper;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayValidationEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ValidChannel;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.PromptPayETEMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.promptpay.PromptPayExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.promptpay.PromptPayPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.promptpay.PromptPayValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.MaskingUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.CATEGORY_E_DONATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_BILL_PROMPT_PAY;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNull;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNullOrDefault;

@Service
@RequiredArgsConstructor
public class BillPromptPayValidationProcessor extends ValidationProcessingTemplate<
        PromptPayPrepareDataValidate,
        PromptPayExternalValidateResponse,
        PromptPayValidateDataAfterCallExternal,
        ActivityBillPayValidationEvent> {
    private static final TMBLogger<BillPromptPayValidationProcessor> logger = new TMBLogger<>(BillPromptPayValidationProcessor.class);

    private final AsyncHelper asyncHelper;
    private final PaymentService paymentService;
    private final CustomerService customerService;
    private final BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;
    private final LogEventPublisherService logEventPublisherService;
    private final CacheService cacheService;
    private final DailyLimitService dailyLimitService;
    private final DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    private final BaseBillPayValidator baseBillPayValidator;
    private final CacheMapper cacheMapper;
    private final PromptPayETEMapper promptPayETEMapper;
    @Value("${amlo.amount.validate.for.get.tax.id:700000}")
    private BigDecimal amloAmountValidate;
    @Value("${qr.payment.iso20022.flag:false}")
    private boolean isQRISO20022FlagOn;

    public static final String TTB_BANK_CODE_3DIGIT_ISO20022 = "811";
    public static final String BILLER_CHANNEL_57 = "57";
    public static final String BILLER_CHANNEL_97 = "97";
    public static final String THAI_NATION_ID_TYPE = "CI";

    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_BILL_PROMPT_PAY;
    }

    @Override
    protected ActivityBillPayValidationEvent initialActivityLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) {
        return ActivityBillPayMapper.INSTANCE.toActivityBillPayValidation(headers, request, cache);
    }

    @Override
    protected PromptPayPrepareDataValidate prepareData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String compCode = cache.getPaymentInformation().getCompCode();
        final boolean isAmountExceedAmloThreshold = request.getDeposit().getAmount().compareTo(amloAmountValidate) >= 0;

        CompletableFuture<MasterBillerResponse> masterBillerFuture = asyncHelper.executeMethodAsync(() -> paymentService.getMasterBiller(correlationId, compCode));
        CompletableFuture<BillPayConfiguration> billPayConfigFuture = asyncHelper.executeMethodAsync(() -> paymentService.getBillPayConfig(correlationId));
        CompletableFuture<DepositAccount> depositAccountFuture = asyncHelper.executeMethodAsync(() -> billPayAccountCommonPaymentService.getAccountByAccountNumber(request.getDeposit().getAccountNumber(), headers));
        CompletableFuture<CustomerCrmProfile> customerCrmProfileFuture = asyncHelper.executeMethodAsync(() -> customerService.getCustomerCrmProfile(correlationId, crmId));
        CompletableFuture.allOf(billPayConfigFuture, masterBillerFuture, customerCrmProfileFuture, depositAccountFuture);

        try {
            final MasterBillerResponse masterBiller = masterBillerFuture.get();
            boolean isEDonationTransaction = masterBiller.getBillerInfo().getBillerCategoryCode().equals(CATEGORY_E_DONATION_ID);

            CustomerKYCResponse customerKycResponse = null;
            if (isEDonationTransaction || isAmountExceedAmloThreshold) {
                customerKycResponse = customerService.getCustomerKYC(correlationId, crmId);
            }


            return PromptPayPrepareDataValidate.builder()
                    .billPayConfiguration(billPayConfigFuture.get())
                    .masterBillerResponse(masterBiller)
                    .customerCrmProfile(customerCrmProfileFuture.get())
                    .fromDepositAccount(depositAccountFuture.get())
                    .customerKYC(customerKycResponse)
                    .build();
        } catch (ExecutionException | InterruptedException | ThreadDeath e) {
            if (e.getCause() instanceof TMBCommonException ex) {
                logger.error("Error ExecutionException TMBCommonException in method PrepareData : {}", ex.getMessage(), e);
                throw ex;
            }
            Thread.currentThread().interrupt();
            logger.error("Error ExecutionException in method PrepareData", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Error execution exception get prepare data. Please verify prepareData method");
        }
    }

    @Override
    protected PromptPayPrepareDataValidate validateData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, PromptPayPrepareDataValidate prepareData) throws TMBCommonException {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final BillPayConfiguration billPayConfiguration = prepareData.getBillPayConfiguration();
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();

        this.checkCompCodeInExcludeBillerConfig(billPayConfiguration, compCode);
        this.transformRequestBody(cache.getPaymentInformation().getProductDetail());
        this.checkBillerExpired(masterBillerResponse);
        this.checkServiceHours(masterBillerResponse);
        this.validateDailyLimitExceeded(request, masterBillerResponse, customerCrmProfile);
        this.validateQRChannel(masterBillerResponse);


        return prepareData;
    }

    private void validateDailyLimitExceeded(ValidationCommonPaymentRequest request, MasterBillerResponse masterBiller, CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        BigDecimal amountFromValidateRequest = request.getDeposit().getAmount();
        dailyLimitService.validateDailyLimitExceeded(masterBiller.getBillerInfo().getBillerGroupType(), customerCrmProfile, amountFromValidateRequest);
    }

    protected void transformRequestBody(ProductDetail productDetail) {
        productDetail.setProductRef1(StringUtils.upperCase(productDetail.getProductRef1()));
        productDetail.setProductRef2(StringUtils.upperCase(productDetail.getProductRef2()));
    }

    protected void checkCompCodeInExcludeBillerConfig(BillPayConfiguration configData, String compCode) throws TMBCommonException {
        baseBillPayValidator.validateCompCodeExclusion(configData, compCode);
    }

    protected void checkBillerExpired(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateBillerExpiration(masterBillerResponse);
    }

    private void validateQRChannel(MasterBillerResponse masterBiller) throws TMBCommonException {
        if (isQRISO20022FlagOn) {
            return;
        }

        List<ValidChannel> channelList = getSafeNullOrDefault(() -> masterBiller.getBillerInfo().getValidChannels(), new ArrayList<>());
        boolean hasChannel57 = hasValidChannel(channelList, BILLER_CHANNEL_57);
        boolean hasChannel97 = hasValidChannel(channelList, BILLER_CHANNEL_97);
        if (hasChannel97 && !hasChannel57) {
            logger.error("Failed to verify payment. Biller doesn't have valid channel (57). [hasChannel97 = {}, hasChannel57 = {}]", hasChannel97, hasChannel57);
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.BILL_PROMPT_PAY_SCAN_QR_BILLER_NOT_ALLOW_PAY_WITH_QR);
        }
    }

    protected void checkServiceHours(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateServiceHours(masterBillerResponse);
    }

    private boolean hasValidChannel(List<ValidChannel> validChannels, String channel) {
        if (CollectionUtils.isEmpty(validChannels) || StringUtils.isBlank(channel)) {
            return false;
        }
        return validChannels.stream()
                .anyMatch(ch -> channel.equalsIgnoreCase(ch.getChannelCode()));
    }

    @Override
    protected PromptPayExternalValidateResponse validateWithExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, PromptPayPrepareDataValidate prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final MasterBillerResponse masterBiller = prepareData.getMasterBillerResponse();
        final boolean isShouldCallToETEISO20022 = isQRISO20022FlagOn && hasValidChannel(getSafeNull(() -> masterBiller.getBillerInfo().getValidChannels()), BILLER_CHANNEL_97);
        final boolean isAmountExceedAmloThreshold = request.getDeposit().getAmount().compareTo(amloAmountValidate) >= 0;

        PromptPayETEValidateRequest promptPayETEValidateRequest = promptPayETEMapper.toPromptPayETERequestForValidatePromptPayPayment(request, cache, prepareData, isAmountExceedAmloThreshold, isShouldCallToETEISO20022);

        PromptPayETEValidateResponse eteResponse;
        if (isShouldCallToETEISO20022) {
            eteResponse = paymentService.validateBillPromptPayISO20022Payment(correlationId, crmId, promptPayETEValidateRequest);
        } else {
            eteResponse = paymentService.validateBillPromptPayPayment(correlationId, crmId, promptPayETEValidateRequest);
        }

        return new PromptPayExternalValidateResponse().setEteRequest(promptPayETEValidateRequest).setEteResponse(eteResponse);
    }

    @Override
    protected PromptPayValidateDataAfterCallExternal validateAfterCallExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, PromptPayPrepareDataValidate prepareData, PromptPayExternalValidateResponse externalResponse) throws TMBCommonException {
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();
        final BigDecimal amountFromValidateRequest = request.getDeposit().getAmount();
        final PromptPayETEValidateResponse eteResponse = externalResponse.getEteResponse();

        BigDecimal fee = this.calculateFee(eteResponse, fromDepositAccount);

        CommonAuthenResult commonAuthenResult = this.validateIsRequireCommonAuthen(request, headers, customerCrmProfile);

        CommonAuthenticationValidationCommonPaymentResponse commonAuthenResponse = null;
        if (commonAuthenResult.isRequireCommonAuthen()) {
            commonAuthenResponse = new CommonAuthenticationValidationCommonPaymentResponse()
                    .setTotalPaymentAccumulateUsage(eteResponse.getAmount().add(customerCrmProfile.getPaymentAccuUsgAmt()))
                    .setFlowName(COMMON_AUTH_BILL_FLOW_NAME)
                    .setFeatureId(COMMON_PAYMENT_BILL_PAY_FEATURE_ID)
                    .setBillerCompCode(compCode);
        }

        return new PromptPayValidateDataAfterCallExternal()
                .setRequireCommonAuthen(commonAuthenResult.isRequireCommonAuthen())
                .setCommonAuthentication(commonAuthenResponse)
                .setFeeAfterCalculated(fee)
                .setTotalAmount(amountFromValidateRequest.add(fee))
                .setCommonAuthenResult(commonAuthenResult);
    }

    protected BigDecimal calculateFee(PromptPayETEValidateResponse eteResponse, DepositAccount fromDepositAccount) {
        BigDecimal fee;
        boolean isWaiveFeeFromDepositAccount = StringUtils.equals("1", fromDepositAccount.getWaiveFeeForBillpay());
        if (isWaiveFeeFromDepositAccount) {
            fee = new BigDecimal("0.00");
        } else {
            fee = eteResponse.getFee().setScale(2, RoundingMode.DOWN);
        }
        return fee;
    }

    private CommonAuthenResult validateIsRequireCommonAuthen(ValidationCommonPaymentRequest request, HttpHeaders headers, CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        BigDecimal amount = request.getDeposit().getAmount();

        return dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(headers, amount, false, customerCrmProfile);
    }

    @Override
    protected void saveSuccessLog(ValidationCommonPaymentRequest request,
                                  HttpHeaders headers,
                                  CommonPaymentDraftCache cache,
                                  PromptPayPrepareDataValidate prepareData,
                                  PromptPayExternalValidateResponse externalResponse,
                                  PromptPayValidateDataAfterCallExternal validateDataAfterCallExternal,
                                  ActivityBillPayValidationEvent activityEvent) {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();

        activityEvent = ActivityBillPayMapper.INSTANCE.updateActivityPromptPayValidation(activityEvent, request, masterBillerResponse, cache, validateDataAfterCallExternal, externalResponse.getEteResponse());

        logEventPublisherService.saveActivityLog(activityEvent);
    }

    @Override
    protected void saveFailedLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, ActivityBillPayValidationEvent activityEvent, PromptPayPrepareDataValidate prepareData, Exception e) {
        final MasterBillerResponse masterBiller = Optional.ofNullable(prepareData).map(PromptPayPrepareDataValidate::getMasterBillerResponse).orElse(null);

        activityEvent = ActivityBillPayMapper.INSTANCE.updateActivityPromptPayValidationFailed(activityEvent, masterBiller, cache, request, e);

        logEventPublisherService.saveActivityLog(activityEvent);
    }

    @Override
    protected TMBCommonException handleException(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, PromptPayPrepareDataValidate prepareData, PromptPayExternalValidateResponse externalResponse, PromptPayValidateDataAfterCallExternal validateDataAfterCallExternal, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process BillPromptPayValidation. : {}", e.getMessage(), e);
        if (e instanceof TMBCommonException ex) {
            throw ex;
        } else if (e instanceof TMBCommonExceptionWithResponse ex) {
            throw ex;
        } else {
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
        }
    }

    @Override
    protected void updateCache(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, PromptPayPrepareDataValidate prepareData, PromptPayExternalValidateResponse externalResponse, PromptPayValidateDataAfterCallExternal validateDataAfterCallExternal) throws JsonProcessingException {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final var commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();

        PromptPayETEConfirmRequest promptPayConfirmRequest = promptPayETEMapper.toPromptPayETERequestForConfirmPromptPayPayment(externalResponse);

        var validateDraftCache = new ValidationCommonPaymentDraftCache()
                .setExternalConfirmRequest(new ExternalConfirmRequest()
                        .setPromptPayConfirmRequest(promptPayConfirmRequest))
                .setRequireCommonAuthen(validateDataAfterCallExternal.isRequireCommonAuthen())
                .setFromDepositAccount(cacheMapper.toDepositAccountInCache(fromDepositAccount))
                .setMasterBillerResponse(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse))
                .setCommonAuthentication(commonAuthentication)
                .setFeeCalculated(validateDataAfterCallExternal.getFeeAfterCalculated())
                .setTotalAmount(validateDataAfterCallExternal.getTotalAmount());

        cache.setValidateRequest(request);
        cache.setValidateDraftCache(validateDraftCache);

        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + request.getTransactionId();
        cacheService.set(cacheKey, COMMON_PAYMENT_HASH_KEY_CACHE, cache);
    }

    @Override
    protected ValidationCommonPaymentResponse mappingResponse(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, PromptPayPrepareDataValidate prepareData, PromptPayExternalValidateResponse externalResponse, PromptPayValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final PromptPayETEValidateResponse eteResponse = externalResponse.getEteResponse();
        final boolean isRequireCommonAuthen = validateDataAfterCallExternal.isRequireCommonAuthen();
        final BigDecimal feeAfterCalculated = validateDataAfterCallExternal.getFeeAfterCalculated();
        final BigDecimal totalAmount = validateDataAfterCallExternal.getTotalAmount();
        final var commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final boolean isAmountExceedAmloThreshold = request.getDeposit().getAmount().compareTo(amloAmountValidate) >= 0;
        final CustomerKYCResponse customerKycResponse = prepareData.getCustomerKYC();

        var response = new ValidationCommonPaymentResponse();
        response.setTransactionId(request.getTransactionId());
        response.setAmount(eteResponse.getAmount());
        response.setTotalAmount(totalAmount);
        response.setFee(feeAfterCalculated);
        response.setIsRequireCommonAuthen(isRequireCommonAuthen);

        if (isRequireCommonAuthen) {
            response.setCommonAuthenticationInformation(commonAuthentication);
        }

        if (isAmountExceedAmloThreshold) {
            response.setCitizenId(MaskingUtils.maskCitizenId(customerKycResponse.getIdNo()));
        }

        return response;
    }

}
