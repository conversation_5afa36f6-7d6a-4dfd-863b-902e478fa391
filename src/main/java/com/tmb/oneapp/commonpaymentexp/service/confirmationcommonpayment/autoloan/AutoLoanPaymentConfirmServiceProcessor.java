package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.autoloan;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityLogConfirmMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.AutoLoanExternalConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.AutoLoanLogsConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.AutoLoanPrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BaseConfirmDataAfterConfirmExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AutoLoanOCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanOCPResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseConfirmLogRecord;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialAutoLoanBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialLogMapper;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2AddServiceRequestResponse;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationCommonPayment;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationMapper;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionLogMapper;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.NotificationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomersTransactionService;
import com.tmb.oneapp.commonpaymentexp.service.client.LoyaltyBizService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.BaseConfirmServiceHelper;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.CommonValidateConfirmationService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ConfirmationProcessingTemplate;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.DateUtils;
import com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CALL_FROM_AUTO_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_IS_USED;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ERROR_DESC;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.FROM_ACCOUNT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HP_EXP_SERVICE_ADD_SERVICE_REQUEST_ERROR_CODE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.PAYMENT_STATUS;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_AUTO_LOAN;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialLogMapper.ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNull;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNullOrDefault;

@Service
@RequiredArgsConstructor
public class AutoLoanPaymentConfirmServiceProcessor extends ConfirmationProcessingTemplate<
        AutoLoanExternalConfirmResponse,
        AutoLoanPrepareDataConfirm,
        BaseConfirmDataAfterConfirmExternal,
        AutoLoanLogsConfirm> {
    private static final TMBLogger<AutoLoanPaymentConfirmServiceProcessor> logger = new TMBLogger<>(AutoLoanPaymentConfirmServiceProcessor.class);
    private final CommonValidateConfirmationService commonValidateConfirmationService;
    private final BaseConfirmServiceHelper baseConfirmServiceHelper;
    private final PaymentService paymentService;
    private final AsyncHelper asyncHelper;
    private final CustomersTransactionService customersTransactionService;
    private final LogEventPublisherService logEventPublisherService;
    private final NotificationCommonPaymentService notificationCommonPaymentService;
    private final DailyLimitService dailyLimitService;
    private final CustomerService customerService;
    private final CacheService cacheService;
    private final AutoLoanDeepLinkHelper autoLoanDeepLinkHelper;
    private final LoyaltyBizService loyaltyBizService;

    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_TMB_AUTO_LOAN;
    }

    @Override
    protected AutoLoanLogsConfirm initialLogs(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache) {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionTime = String.valueOf(System.currentTimeMillis());
        String ePayCode;
        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            ePayCode = draftCache.getValidateDraftCache().getExternalConfirmRequest().getAutoLoanOCPBillPaymentConfirmRequest().getEpayCode();
        } else {
            ePayCode = draftCache.getValidateDraftCache().getExternalConfirmRequest().getTopUpETEPaymentRequest().getEpayCode();
        }
        final var baseConfirmLogRecord = new BaseConfirmLogRecord(ePayCode, crmId, correlationId, transactionTime, ACTIVITY_ID_3_DIGITS_FIN_AND_TRANS_BILL_PAY_ONLINE_ID);

        var activityLogAutoLoan = ActivityLogConfirmMapper.INSTANCE.mapToActivityAutoLoanBillPayConfirmationEvent(headers, draftCache);
        var financialLogAutoLoan = FinancialLogMapper.INSTANCE.mapToFinancialAutoLoanBillPayActivityLog(baseConfirmLogRecord, activityLogAutoLoan.getActivityTypeId(), draftCache);
        var transactionLogAutoLoan = TransactionLogMapper.INSTANCE.mapToTransactionActivityAutoLoanBillPay(baseConfirmLogRecord, draftCache);

        return new AutoLoanLogsConfirm()
                .setActivityAutoLoanBillPayConfirmationEvent(activityLogAutoLoan)
                .setFinancialAutoLoanBillPayActivityLog(financialLogAutoLoan)
                .setTransactionActivityAutoLoanBillPay(transactionLogAutoLoan)
                .setActivityCustomSlipCompleteEvent(new ActivityCustomSlipCompleteEvent(headers, request.getCustomSlip(), CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_BILL));
    }

    @Override
    protected AutoLoanPrepareDataConfirm prepareData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, AutoLoanLogsConfirm logEvents) throws TMBCommonException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionTimeFromFinLog = logEvents.getFinancialAutoLoanBillPayActivityLog().getTxnDt();
        final boolean isDeepLinkAndCallFromAutoLoan = StringUtils.equalsIgnoreCase(BILLER_CALL_FROM_AUTO_LOAN, getSafeNull(() -> draftCache.getDeepLinkRequest().getCallFrom()));

        CustomerCrmProfile customerCrmProfile = customerService.getCustomerCrmProfile(correlationId, crmId);

        K2AddServiceRequestResponse k2AddServiceResponse = null;
        if (isDeepLinkAndCallFromAutoLoan) {
            final DeepLinkRequestInCache deepLinkRequest = draftCache.getDeepLinkRequest();
            String accountId;
            if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
                accountId = draftCache.getValidateDraftCache().getExternalConfirmRequest().getAutoLoanOCPBillPaymentConfirmRequest().getFromAccount().getAccountId();
            } else {
                accountId = draftCache.getValidateDraftCache().getExternalConfirmRequest().getTopUpETEPaymentRequest().getFromAccount().getAccountId();
            }

                k2AddServiceResponse = autoLoanDeepLinkHelper.handleServiceRequest(deepLinkRequest, headers, accountId, draftCache);
        }

        return AutoLoanPrepareDataConfirm.builder()
                .transactionTime(transactionTimeFromFinLog)
                .customerCrmProfile(customerCrmProfile)
                .k2AddServiceResponse(k2AddServiceResponse)
                .build();
    }

    @Override
    protected AutoLoanPrepareDataConfirm validateData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, AutoLoanPrepareDataConfirm prepareData) throws TMBCommonException {
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final String transactionId = request.getTransactionId();
        final boolean isPayByOwner = NullSafeUtils.getSafeNullOrDefault(() -> draftCache.getValidateDraftCache().getAdditionalParam().isPayByOwner(), false);

        commonValidateConfirmationService.validateServiceHours(draftCache);
        commonValidateConfirmationService.verifyAuthentication(transactionId, draftCache, headers);
        commonValidateConfirmationService.validateTransactionByTransactionId(transactionId);

        if (!isPayByOwner) {
            String billerGroupType = draftCache.getValidateDraftCache().getMasterBillerResponse().getBillerInfo().getBillerGroupType();
            BigDecimal amountFromValidateRequest = draftCache.getValidateRequest().getDeposit().getAmount();
            dailyLimitService.validateDailyLimitExceeded(billerGroupType, customerCrmProfile, amountFromValidateRequest);
        }

        return prepareData;
    }

    @Override
    protected AutoLoanExternalConfirmResponse confirmWithExternalService(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, AutoLoanPrepareDataConfirm prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            WowPointRedeemConfirmRequest redeemRequest = draftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest();
            loyaltyBizService.redeemPoint(headers, redeemRequest);

            AutoLoanOCPBillRequest ocpBillConfirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getAutoLoanOCPBillPaymentConfirmRequest();
            AutoLoanOCPResponse response = paymentService.confirmOCPBillWowPointAutoLoanPayment(correlationId, crmId, request.getTransactionId(), ocpBillConfirmRequest);

            return new AutoLoanExternalConfirmResponse().setAutoLoanOCPResponse(response);
        } else {
            TopUpETEPaymentRequest paymentRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getTopUpETEPaymentRequest();
            TopUpETEResponse response = paymentService.confirmAutoLoanPayment(correlationId, crmId, request.getTransactionId(), paymentRequest);

            return new AutoLoanExternalConfirmResponse().setTopUpETEResponse(response);
        }
    }

    @Override
    protected BaseConfirmDataAfterConfirmExternal processAfterConfirmWithExternal(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, AutoLoanPrepareDataConfirm prepareData, AutoLoanExternalConfirmResponse externalResponse) {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionId = request.getTransactionId();
        final String transactionTime = prepareData.getTransactionTime();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final K2AddServiceRequestResponse k2AddServiceResponse = prepareData.getK2AddServiceResponse();
        final boolean isPayByOwner = getSafeNullOrDefault(() -> draftCache.getValidateDraftCache().getAdditionalParam().isPayByOwner(), false);
        final boolean isRequireCommonAuth = draftCache.getValidateDraftCache().isRequireCommonAuthen();
        final boolean isDeepLinkAndCallFromAutoLoan = StringUtils.equalsIgnoreCase(BILLER_CALL_FROM_AUTO_LOAN, getSafeNull(() -> draftCache.getDeepLinkRequest().getCallFrom()));
        boolean isShouldUpdateAutoLoanPaymentStatus = isDeepLinkAndCallFromAutoLoan && k2AddServiceResponse != null;

        String amountFromConfirmRequest;
        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            amountFromConfirmRequest = String.valueOf(draftCache.getValidateDraftCache().getExternalConfirmRequest().getAutoLoanOCPBillPaymentConfirmRequest().getAmount());
        } else {
            amountFromConfirmRequest = String.valueOf(draftCache.getValidateDraftCache().getExternalConfirmRequest().getTopUpETEPaymentRequest().getAmount());
        }

        baseConfirmServiceHelper.baseClearDraftDataCache(transactionId);
        customersTransactionService.clearDepositCache(correlationId, crmId);
        baseConfirmServiceHelper.baseExecuteCallbackIfConfiguredAsync(draftCache, transactionTime, amountFromConfirmRequest);

        if (!isPayByOwner) {
            baseConfirmServiceHelper.baseUpdatePinFreeCountWithCondition(crmId, correlationId, customerCrmProfile, isRequireCommonAuth);
            dailyLimitService.updateAccumulateUsage(draftCache, customerCrmProfile, crmId, correlationId);
        }

        if (isShouldUpdateAutoLoanPaymentStatus) {
            autoLoanDeepLinkHelper.updateAutoLoanPaymentStatusSuccess(headers, draftCache, externalResponse, k2AddServiceResponse);
        }

        NotificationCommonPayment notificationRequest = NotificationMapper.INSTANCE.toAutoLoanNotification(draftCache, crmId, correlationId, transactionTime);
        asyncHelper.executeMethodAsyncSafelyVoid(() -> notificationCommonPaymentService.sendENotification(notificationRequest));

        return null;
    }

    @Override
    protected void saveSuccessLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, AutoLoanPrepareDataConfirm prepareData, AutoLoanExternalConfirmResponse externalResponse, AutoLoanLogsConfirm logEvents, BaseConfirmDataAfterConfirmExternal processAfterConfirmResult) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final var activityLog = logEvents.getActivityAutoLoanBillPayConfirmationEvent();
        final var customSlipLog = logEvents.getActivityCustomSlipCompleteEvent();
        final var transactionActivityLog = logEvents.getTransactionActivityAutoLoanBillPay();
        String availableBalance;
        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            availableBalance = externalResponse.getAutoLoanOCPResponse().getAccount().getAvailBal();
        } else {
            availableBalance = externalResponse.getTopUpETEResponse().getTransaction().getFromAccount().getBalances().getAvailable();
        }

        FinancialAutoLoanBillPayActivityLog financialLog = logEvents.getFinancialAutoLoanBillPayActivityLog();
        financialLog.setTxnBal(availableBalance);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog, customSlipLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));
    }

    @Override
    protected ConfirmationCommonPaymentResponse mappingResponse(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, AutoLoanPrepareDataConfirm prepareData, AutoLoanExternalConfirmResponse externalResponse) {
        final CustomerCrmProfile customerProfile = prepareData.getCustomerCrmProfile();
        final String transactionTime = prepareData.getTransactionTime();
        String ePayCode;
        String remainingBalance;
        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            ePayCode = draftCache.getValidateDraftCache().getExternalConfirmRequest().getAutoLoanOCPBillPaymentConfirmRequest().getEpayCode();
            remainingBalance = externalResponse.getAutoLoanOCPResponse().getAccount().getAvailBal();
        } else {
            ePayCode = draftCache.getValidateDraftCache().getExternalConfirmRequest().getTopUpETEPaymentRequest().getEpayCode();
            remainingBalance = externalResponse.getTopUpETEResponse().getTransaction().getFromAccount().getBalances().getAvailable();
        }

        ConfirmationCommonPaymentResponse confirmResponse = new ConfirmationCommonPaymentResponse();
        confirmResponse.setTransactionCreatedDatetime(DateUtils.formatTimestampToISO(transactionTime));
        confirmResponse.setReferenceNo(ePayCode);
        confirmResponse.setRemainingBalance(getSafeNull(() -> new BigDecimal(remainingBalance).setScale(2, RoundingMode.HALF_DOWN)));
        confirmResponse.setCompleteScreenDetail(draftCache.getPaymentInformation().getCompleteScreenDetail());
        confirmResponse.setAutoSaveSlip(StringUtils.equalsAnyIgnoreCase("Y", customerProfile.getAutoSaveSlipMain()));

        return confirmResponse;
    }

    @Override
    protected TMBCommonException handleException(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, AutoLoanPrepareDataConfirm prepareData, AutoLoanLogsConfirm logEvents, Exception e) throws TMBCommonException {
        final K2AddServiceRequestResponse k2AddServiceResponse = getSafeNull(prepareData::getK2AddServiceResponse);
        final boolean isDeepLinkAndCallFromAutoLoan = StringUtils.equalsIgnoreCase(BILLER_CALL_FROM_AUTO_LOAN, getSafeNull(() -> draftCache.getDeepLinkRequest().getCallFrom()));
        final String transactionId = Optional.ofNullable(request).map(ConfirmationCommonPaymentRequest::getTransactionId).orElse(null);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> this.clearStampTransactionUsed(transactionId));

        logger.error("ERROR ConfirmationService. : {}", e.getMessage(), e);
        if (e instanceof TMBCommonException ex) {
            boolean isShouldUpdateAutoLoanPaymentStatus = !HP_EXP_SERVICE_ADD_SERVICE_REQUEST_ERROR_CODE.equalsIgnoreCase(ex.getErrorCode()) && isDeepLinkAndCallFromAutoLoan && k2AddServiceResponse != null;
            this.updateAutoLoanPaymentStatusFailed(isShouldUpdateAutoLoanPaymentStatus, headers, k2AddServiceResponse, draftCache, e);

            throw ex;

        } else {
            logger.error("ERROR Unhandled exception. Please verify the exception. : {}", e.getMessage(), e);

            boolean isShouldUpdateAutoLoanPaymentStatus = isDeepLinkAndCallFromAutoLoan && k2AddServiceResponse != null;
            this.updateAutoLoanPaymentStatusFailed(isShouldUpdateAutoLoanPaymentStatus, headers, k2AddServiceResponse, draftCache, e);

            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
        }
    }

    private void updateAutoLoanPaymentStatusFailed(boolean isShouldUpdateAutoLoanPaymentStatus, HttpHeaders headers, K2AddServiceRequestResponse k2AddServiceResponse, CommonPaymentDraftCache draftCache, Exception e) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final DeepLinkRequestInCache deepLinkRequest = draftCache.getDeepLinkRequest();
        String accountId;
        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            accountId = getSafeNull(() -> draftCache.getValidateDraftCache().getExternalConfirmRequest().getAutoLoanOCPBillPaymentConfirmRequest().getFromAccount().getAccountId());
        } else {
            accountId = getSafeNull(() -> draftCache.getValidateDraftCache().getExternalConfirmRequest().getTopUpETEPaymentRequest().getFromAccount().getAccountId());

        }

        if (isShouldUpdateAutoLoanPaymentStatus) {
            Map<String, String> params = new HashMap<>();
            params.put(FROM_ACCOUNT, accountId);
            params.put(HEADER_CRM_ID, crmId);
            params.put(HEADER_CORRELATION_ID, correlationId);
            params.put(PAYMENT_STATUS, "02");
            params.put(ERROR_DESC, e.getMessage());
            autoLoanDeepLinkHelper.handleUpdatePaymentStatus(params, headers, deepLinkRequest, null, k2AddServiceResponse, draftCache);
        }
    }

    private void clearStampTransactionUsed(String transactionId) {
        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + transactionId;
        cacheService.delete(cacheKey, COMMON_PAYMENT_HASH_KEY_IS_USED);
    }

    @Override
    protected void saveFailedLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, AutoLoanPrepareDataConfirm prepareData, AutoLoanLogsConfirm logEvents, Exception e) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        var financialLog = logEvents.getFinancialAutoLoanBillPayActivityLog();
        var activityLog = logEvents.getActivityAutoLoanBillPayConfirmationEvent();
        var transactionActivityLog = logEvents.getTransactionActivityAutoLoanBillPay();
        financialLog.setFailureStatusWithErrorCodeFromException(e);
        activityLog.setFailureStatusWithReasonFromException(e);
        transactionActivityLog.setTransactionStatus(ACTIVITY_FAILURE);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
    }
}
