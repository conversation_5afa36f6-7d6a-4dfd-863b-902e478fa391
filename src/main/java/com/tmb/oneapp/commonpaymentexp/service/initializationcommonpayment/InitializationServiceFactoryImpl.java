package com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentMapper;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class InitializationServiceFactoryImpl implements InitializationServiceFactory {

    private final CacheService cacheService;
    private final PaymentService paymentService;
    private final CommonPaymentMapper commonPaymentMapper;

    @Value("${common.payment.initial.deeplink.url}")
    private String deeplinkPrefix;

    @Value("${common.payment.initial.cache.expire.second:600}")
    private int cacheExpireSecond;

    @Override
    public InitializationCommonPaymentService create(InitializationValidator validatorDependOnBusiness) {
        return new InitializationCommonPaymentService(cacheService, paymentService, validatorDependOnBusiness, commonPaymentMapper, deeplinkPrefix, cacheExpireSecond);
    }
}
