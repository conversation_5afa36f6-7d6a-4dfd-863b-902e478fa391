package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ewallet;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.TransferConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayMapper;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayValidationEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.EWalletETEMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETERequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ewallet.EWalletPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ewallet.EWalletValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.TransferService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ValidationProcessingTemplate;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_TOP_UP_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_TOP_UP_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TOPUP_E_WALLET;

@Service
@RequiredArgsConstructor
public class EWalletValidationProcessor extends ValidationProcessingTemplate<
        EWalletPrepareDataValidate,
        EWalletETEResponse,
        com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ewallet.EWalletValidateDataAfterCallExternal,
        ActivityBillPayValidationEvent> {
    private static final TMBLogger<EWalletValidationProcessor> logger = new TMBLogger<>(EWalletValidationProcessor.class);

    private final AsyncHelper asyncHelper;
    private final PaymentService paymentService;
    private final CustomerServiceClient customerServiceClient;
    private final CustomerService customerService;
    private final BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;
    private final LogEventPublisherService logEventPublisherService;
    private final CacheService cacheService;
    private final DailyLimitService dailyLimitService;
    private final DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    private final TransferService transferService;
    private final CacheMapper cacheMapper;

    @Value("${amlo.amount.validate.for.get.tax.id:700000}")
    private BigDecimal amloAmountValidate;

    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_TOPUP_E_WALLET;
    }

    @Override
    protected ActivityBillPayValidationEvent initialActivityLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) {
        return ActivityBillPayMapper.INSTANCE.toActivityBillPayValidation(headers, request, cache);
    }

    @Override
    protected EWalletPrepareDataValidate prepareData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String compCode = cache.getPaymentInformation().getCompCode();
        EWalletPrepareDataValidate prepareData = new EWalletPrepareDataValidate();

        CompletableFuture<TransferConfiguration> transferConfigFuture = asyncHelper.executeMethodAsync(() -> transferService.fetchTransferModuleConfig(correlationId));
        CompletableFuture<MasterBillerResponse> masterBillerFuture = asyncHelper.executeMethodAsync(() -> paymentService.getMasterBiller(correlationId, compCode));
        CompletableFuture<CustomerCrmProfile> customerCrmProfileFuture = asyncHelper.executeRequestAsync(() -> customerServiceClient.fetchCustomerCrmProfile(correlationId, crmId));
        CompletableFuture<DepositAccount> depositAccountFuture = asyncHelper.executeMethodAsync(() -> billPayAccountCommonPaymentService.getAccountByAccountNumber(request.getDeposit().getAccountNumber(), headers));
        CompletableFuture<CustomerKYCResponse> customerKycFuture = CompletableFuture.completedFuture(null);

        if (request.getDeposit().getAmount().compareTo(amloAmountValidate) >= 0) {
            customerKycFuture = asyncHelper.executeMethodAsync(() -> customerService.getCustomerKYC(correlationId, crmId));
        }
        CompletableFuture.allOf(transferConfigFuture, masterBillerFuture, customerCrmProfileFuture, depositAccountFuture, customerKycFuture);
        try {
            prepareData.setTransferConfiguration(transferConfigFuture.get());
            prepareData.setMasterBillerResponse(masterBillerFuture.get());
            prepareData.setCustomerCrmProfile(customerCrmProfileFuture.get());
            prepareData.setFromDepositAccount(depositAccountFuture.get());
            prepareData.setCustomerKYC(customerKycFuture.get());
        } catch (ExecutionException | InterruptedException | ThreadDeath e) {
            if (e.getCause() instanceof TMBCommonException ex) {
                logger.error("Error ExecutionException TMBCommonException in method PrepareData : {}", ex.getMessage(), e);
                throw ex;
            }
            Thread.currentThread().interrupt();
            logger.error("Error ExecutionException in method PrepareData", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Error execution exception get prepare data. Please verify prepareData method");
        }

        return prepareData;
    }

    @Override
    protected EWalletPrepareDataValidate validateData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, EWalletPrepareDataValidate prepareData) throws TMBCommonException {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();

        this.validateDailyLimitExceeded(request, masterBillerResponse, customerCrmProfile);

        return prepareData;
    }

    private void validateDailyLimitExceeded(ValidationCommonPaymentRequest request, MasterBillerResponse masterBiller, CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        boolean isNotPayWithCreditCard = !request.getCreditCard().isPayWithCreditCardFlag();
        if (isNotPayWithCreditCard) {
            BigDecimal amount = request.getDeposit().getAmount();
            dailyLimitService.validateDailyLimitExceeded(masterBiller.getBillerInfo().getBillerGroupType(), customerCrmProfile, amount);
        }
    }


    @Override
    protected EWalletETEResponse validateWithExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, EWalletPrepareDataValidate prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        EWalletETERequest eWalletETERequest = EWalletETEMapper.INSTANCE.toEWalletETERequestForValidateEWalletPayment(request, prepareData, cache);

        EWalletETEResponse eteResponse = paymentService.validateEWalletPayment(correlationId, crmId, eWalletETERequest);

        String overrideFromAccountName = eWalletETERequest.getSender().getAccountName();
        eteResponse.getSender().setAccountName(overrideFromAccountName);

        return eteResponse;
    }


    @Override
    protected EWalletValidateDataAfterCallExternal validateAfterCallExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, EWalletPrepareDataValidate prepareData, EWalletETEResponse externalResponse) throws TMBCommonException {
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();

        final BigDecimal fee = this.calculateFee(externalResponse, prepareData.getTransferConfiguration(), fromDepositAccount);

        CommonAuthenResult commonAuthenResult = this.validateIsRequireCommonAuthen(request, headers, customerCrmProfile);

        CommonAuthenticationValidationCommonPaymentResponse commonAuthenResponse = null;
        if (commonAuthenResult.isRequireCommonAuthen()) {
            commonAuthenResponse = new CommonAuthenticationValidationCommonPaymentResponse()
                    .setTotalPaymentAccumulateUsage(externalResponse.getAmount().add(customerCrmProfile.getPaymentAccuUsgAmt()))
                    .setFlowName(COMMON_AUTH_TOP_UP_FLOW_NAME)
                    .setFeatureId(COMMON_PAYMENT_TOP_UP_FEATURE_ID)
                    .setBillerCompCode(compCode);
        }

        BigDecimal amount = request.getDeposit().getAmount();
        return new EWalletValidateDataAfterCallExternal()
                .setRequireCommonAuthen(commonAuthenResult.isRequireCommonAuthen())
                .setCommonAuthentication(commonAuthenResponse)
                .setFeeAfterCalculated(fee)
                .setTotalAmount(amount.add(fee))
                .setCommonAuthenResult(commonAuthenResult);
    }

    private BigDecimal calculateFee(EWalletETEResponse eteResponse, TransferConfiguration transferConfiguration, DepositAccount fromDepositAccount) {
        final BigDecimal feeFromETE = eteResponse.getFee();
        final String waiveFeePromptPayFromDepositAccount = fromDepositAccount.getWaiveFeeForPromptPay();
        final String waiveFeePromptPayFromConfig = transferConfiguration.getPromptPayProxyWaiveFee();
        final BigDecimal defaultFee = new BigDecimal("0.00");

        boolean isPromptPayToOwnAccount = (feeFromETE == null);
        if (isPromptPayToOwnAccount) {
            return defaultFee;
        }

        boolean isWaiveFee = (StringUtils.equals("Y", waiveFeePromptPayFromConfig) || StringUtils.equals("1", waiveFeePromptPayFromDepositAccount));
        if (isWaiveFee) {
            return defaultFee;
        } else {
            return feeFromETE.setScale(2, RoundingMode.DOWN);
        }
    }

    private CommonAuthenResult validateIsRequireCommonAuthen(ValidationCommonPaymentRequest request, HttpHeaders headers, CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        BigDecimal amount = request.getDeposit().getAmount();

        return dailyLimitPinFreeValidator.validateIsRequireCommonAuthForTopUp(headers, amount, false, customerCrmProfile);
    }

    @Override
    protected void saveSuccessLog(ValidationCommonPaymentRequest request,
                                  HttpHeaders headers,
                                  CommonPaymentDraftCache cache,
                                  EWalletPrepareDataValidate prepareData,
                                  EWalletETEResponse externalResponse,
                                  EWalletValidateDataAfterCallExternal validateDataAfterCallExternal,
                                  ActivityBillPayValidationEvent activityEvent) {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();

        ActivityBillPayValidationEvent activityEventData = activityEvent;
        activityEventData = ActivityBillPayMapper.INSTANCE.updateActivityEWalletValidation(activityEventData, masterBillerResponse, cache, request, validateDataAfterCallExternal);

        logEventPublisherService.saveActivityLog(activityEventData);
    }

    @Override
    protected void saveFailedLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, ActivityBillPayValidationEvent activityEvent, EWalletPrepareDataValidate prepareData, Exception e) {
        final MasterBillerResponse masterBiller = Optional.ofNullable(prepareData).map(EWalletPrepareDataValidate::getMasterBillerResponse).orElse(null);

        activityEvent = ActivityBillPayMapper.INSTANCE.updateActivityBillPayValidationFailed(activityEvent, masterBiller, cache, request, e);

        logEventPublisherService.saveActivityLog(activityEvent);
    }

    @Override
    protected TMBCommonException handleException(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, EWalletPrepareDataValidate prepareData, EWalletETEResponse externalResponse, EWalletValidateDataAfterCallExternal validateDataAfterCallExternal, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process EWalletValidation. : {}", e.getMessage(), e);
        if (e instanceof TMBCommonException ex) {
            throw ex;
        } else if (e instanceof TMBCommonExceptionWithResponse ex) {
            throw ex;
        } else {
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
        }
    }

    @Override
    protected void updateCache(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, EWalletPrepareDataValidate prepareData, final EWalletETEResponse externalResponse, EWalletValidateDataAfterCallExternal validateDataAfterCallExternal) throws JsonProcessingException {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final var commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();

        EWalletETERequest eWalletConfirmRequest = EWalletETEMapper.INSTANCE.toEWalletETERequestForConfirmEWalletPayment(externalResponse);

        var validateDraftCache = new ValidationCommonPaymentDraftCache()
                .setExternalConfirmRequest(new ExternalConfirmRequest()
                        .setEWalletConfirmRequest(eWalletConfirmRequest))
                .setRequireCommonAuthen(validateDataAfterCallExternal.isRequireCommonAuthen())
                .setFromDepositAccount(cacheMapper.toDepositAccountInCache(prepareData.getFromDepositAccount()))
                .setMasterBillerResponse(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse))
                .setCommonAuthentication(commonAuthentication)
                .setFeeCalculated(validateDataAfterCallExternal.getFeeAfterCalculated())
                .setTotalAmount(validateDataAfterCallExternal.getTotalAmount());

        cache.setValidateRequest(request);
        cache.setValidateDraftCache(validateDraftCache);

        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + request.getTransactionId();
        cacheService.set(cacheKey, COMMON_PAYMENT_HASH_KEY_CACHE, cache);
    }

    @Override
    protected ValidationCommonPaymentResponse mappingResponse(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, EWalletPrepareDataValidate prepareData, final EWalletETEResponse externalResponse, EWalletValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final boolean isRequireCommonAuthen = validateDataAfterCallExternal.isRequireCommonAuthen();
        final BigDecimal feeAfterCalculated = validateDataAfterCallExternal.getFeeAfterCalculated();
        final var commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();

        ValidationCommonPaymentResponse response = new ValidationCommonPaymentResponse();
        response.setTransactionId(request.getTransactionId());
        response.setFee(feeAfterCalculated);
        response.setAmount(externalResponse.getAmount());
        response.setTotalAmount(externalResponse.getAmount().add(feeAfterCalculated));
        response.setIsRequireCommonAuthen(isRequireCommonAuthen);

        if (isRequireCommonAuthen) {
            response.setCommonAuthenticationInformation(commonAuthentication);
        }

        return response;
    }

}
