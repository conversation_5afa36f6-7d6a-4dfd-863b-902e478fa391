package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;

@Service
@RequiredArgsConstructor
public class ValidationCommonPaymentService {
    private static final TMBLogger<ValidationCommonPaymentService> logger = new TMBLogger<>(ValidationCommonPaymentService.class);
    private final CacheService cacheService;
    private final ValidationCommonPaymentProcessorSelector validationCommonPaymentProcessorSelector;

    public ValidationCommonPaymentResponse validateCommonPayment(ValidationCommonPaymentRequest validationCommonPaymentRequest, HttpHeaders headers) throws TMBCommonException, TMBCommonExceptionWithResponse {
        CommonPaymentDraftCache cache = getPaymentDraftCache(validationCommonPaymentRequest.getTransactionId());
        String processorType = cache.getProcessorType();

        validationCommonPaymentProcessorSelector.validateTransactionType(processorType);

        ValidationCommonPaymentProcessor processor = validationCommonPaymentProcessorSelector.getProcessor(processorType);

        return processor.executeValidate(validationCommonPaymentRequest, headers, cache);
    }

    private CommonPaymentDraftCache getPaymentDraftCache(String transactionId) throws TMBCommonException {
        try {
            logger.info("get payment draft cache transaction id = [{}]", transactionId);
            String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + transactionId;
            return (CommonPaymentDraftCache) Optional.ofNullable(cacheService.get(cacheKey, COMMON_PAYMENT_HASH_KEY_CACHE, CommonPaymentDraftCache.class))
                    .orElseThrow(() -> CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.TRANSACTION_NOT_FOUND_ERROR));

        } catch (Exception e) {
            logger.error("got exception on validateCommonPayment!!", e);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.TRANSACTION_NOT_FOUND_ERROR);
        }
    }
}
