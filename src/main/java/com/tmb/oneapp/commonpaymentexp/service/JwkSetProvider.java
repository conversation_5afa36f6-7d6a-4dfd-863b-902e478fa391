package com.tmb.oneapp.commonpaymentexp.service;

import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class JwkSetProvider {

    private static final TMBLogger<JwkSetProvider> logger = new TMBLogger<>(JwkSetProvider.class);
    private final Map<String, JWKSet> jwkSetMap;

    public JwkSetProvider(@Qualifier("jwkSetMap") Map<String, JWKSet> jwkSetMap) {
        this.jwkSetMap = jwkSetMap;
    }

    /**
     * Get the JWK set for a specific partner.
     *
     * @param partnerName The identifier for the partner.
     * @return The loaded JWKSet for the partner.
     * @throws TMBCommonException if the JWKSet for the partner is not found.
     */
    public JWKSet getJwkSet(String partnerName) throws TMBCommonException {
        JWKSet jwkSet = jwkSetMap.get(partnerName);
        if (jwkSet == null) {
            logger.error("JWKSet for partner '{}' not found.", partnerName);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWK_SET_NOT_FOUND, " Didn’t provide key for new partner: " + partnerName);
        }
        return jwkSet;
    }

    /**
     * Find a specific key by its Key ID (kid) within a specific partner's JWKSet.
     *
     * @param partnerName The partner identifier.
     * @param keyId    The Key ID to search for.
     * @return The JWK corresponding to the given Key ID.
     * @throws TMBCommonException if the key or partner's JWKSet is not found.
     */
    public JWK getKeyById(String partnerName, String keyId) throws TMBCommonException {
        JWKSet jwkSet = getJwkSet(partnerName);
        JWK jwk = jwkSet.getKeyByKeyId(keyId);
        if (jwk == null) {
            logger.error("JWK with kid '{}' not found in JWKSet for partner '{}'.", keyId, partnerName);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.JWK_KEY_NOT_FOUND, "Cannot decrypt because Partner sent incorrect key id: " + keyId);
        }
        return jwk;
    }
}
