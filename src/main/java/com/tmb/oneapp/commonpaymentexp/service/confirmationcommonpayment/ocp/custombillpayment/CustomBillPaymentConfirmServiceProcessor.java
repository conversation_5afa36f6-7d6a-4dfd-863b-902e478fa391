package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ocp.custombillpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomBillPayConfirmationEvent;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityCustomSlipCompleteEvent;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BaseConfirmDataAfterConfirmExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponseMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.CustomLogsConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialCustomBillPayActivityLog;
import com.tmb.oneapp.commonpaymentexp.model.transactionlog.TransactionActivityCustomBillPay;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.client.LoyaltyBizService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.BaseConfirmServiceHelper;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.CommonValidateConfirmationService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ConfirmationProcessingTemplate;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.DateUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;

import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ActivityConstant.CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;

/**
 * Abstract class for custom bill payment confirmation service processors
 */
@RequiredArgsConstructor
public abstract class CustomBillPaymentConfirmServiceProcessor extends ConfirmationProcessingTemplate<
        OCPBillPayment,
        BasePrepareDataConfirm,
        BaseConfirmDataAfterConfirmExternal,
        CustomLogsConfirm> {

    private static final TMBLogger<CustomBillPaymentConfirmServiceProcessor> logger = new TMBLogger<>(CustomBillPaymentConfirmServiceProcessor.class);

    private final BaseConfirmServiceHelper baseConfirmServiceHelper;
    private final CommonValidateConfirmationService commonValidateConfirmationService;
    private final PaymentService paymentService;
    private final AsyncHelper asyncHelper;
    private final LogEventPublisherService logEventPublisherService;
    private final LoyaltyBizService loyaltyBizService;


    protected abstract FinancialCustomBillPayActivityLog initialFinancialLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache);

    @Override
    protected CustomLogsConfirm initialLogs(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache) {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String transactionTime = String.valueOf(System.currentTimeMillis());
        final String ePayCode = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest().getEpayCode();

        final FinancialCustomBillPayActivityLog financialLog = initialFinancialLog(request, headers, draftCache);

        return new CustomLogsConfirm()
                .setActivityCustomBillPayConfirmationEvent(new ActivityCustomBillPayConfirmationEvent(ACTIVITY_LOG_COMMON_PAYMENT_CONFIRM_ID, headers, draftCache))
                .setFinancialCustomBillPayActivityLog(financialLog)
                .setTransactionActivityCustomBillPay(new TransactionActivityCustomBillPay(ePayCode, crmId, draftCache, transactionTime))
                .setActivityCustomSlipCompleteEvent(new ActivityCustomSlipCompleteEvent(headers, request.getCustomSlip(), CUSTOM_SLIP_COMPLETE_BACKGROUND_TNX_TYPE_BILL));
    }


    @Override
    protected BasePrepareDataConfirm prepareData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, CustomLogsConfirm logEvents) throws TMBCommonException {
        return baseConfirmServiceHelper.getBasePrepareDataConfirm(headers, logEvents.getFinancialCustomBillPayActivityLog().getTxnDt());
    }

    @Override
    protected BasePrepareDataConfirm validateData(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException {
        commonValidateConfirmationService.baseValidateData(request, headers, draftCache, prepareData);

        return prepareData;
    }

    @Override
    protected OCPBillPayment confirmWithExternalService(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String transactionId = request.getTransactionId();
        final String transactionTimeFromFinLog = prepareData.getTransactionTime();
        final String overrideRequestDateTime = DateUtils.formatTimestampToISO(transactionTimeFromFinLog);
        final OCPBillRequest originalOCPBillConfirmRequest = draftCache.getValidateDraftCache().getExternalConfirmRequest().getOcpBillPaymentConfirmRequest();
        OCPBillPaymentResponse ocpBillPaymentResponseEntity;

        OCPBillRequest modifyOCPBillConfirmRequest = originalOCPBillConfirmRequest
                .toBuilder()
                .requestDateTime(overrideRequestDateTime)
                .build();

        if (WowPointUtils.isWowPointTransaction(draftCache.getValidateRequest(), draftCache.getCommonPaymentRule())) {
            WowPointRedeemConfirmRequest redeemRequest = draftCache.getValidateDraftCache().getWowPointRedeemConfirmRequest();
            loyaltyBizService.redeemPoint(headers, redeemRequest);

            ocpBillPaymentResponseEntity = paymentService.confirmOCPBillWowPointPayment(correlationId, crmId, transactionId, modifyOCPBillConfirmRequest);
        } else {
            ocpBillPaymentResponseEntity = paymentService.confirmOCPBillPayment(correlationId, crmId, transactionId, modifyOCPBillConfirmRequest);
        }
        return ocpBillPaymentResponseEntity.getData();
    }

    @Override
    protected BaseConfirmDataAfterConfirmExternal processAfterConfirmWithExternal(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPBillPayment externalResponse) {
        return baseConfirmServiceHelper.baseConfirmDataAfterConfirmExternal(request, headers, draftCache, prepareData);
    }

    @Override
    protected void saveSuccessLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPBillPayment externalResponse, CustomLogsConfirm logEvents, BaseConfirmDataAfterConfirmExternal processAfterConfirmResult) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final ActivityCustomBillPayConfirmationEvent activityLog = logEvents.getActivityCustomBillPayConfirmationEvent();
        final var customSlipLog = logEvents.getActivityCustomSlipCompleteEvent();
        final TransactionActivityCustomBillPay transactionActivityLog = logEvents.getTransactionActivityCustomBillPay();
        final String availableBalanceFromExternalResponse = externalResponse.getAccount().getAvailBal();

        FinancialCustomBillPayActivityLog financialLog = logEvents.getFinancialCustomBillPayActivityLog();
        financialLog.setTxnBal(availableBalanceFromExternalResponse);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog, customSlipLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));
    }

    @Override
    protected ConfirmationCommonPaymentResponse mappingResponse(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, OCPBillPayment externalResponse) {
        return ConfirmationCommonPaymentResponseMapper.INSTANCE.mapToOCPConfirmationCommonPaymentResponse(draftCache, prepareData, externalResponse);
    }

    @Override
    protected TMBCommonException handleException(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, CustomLogsConfirm logEvents, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process MEAConfirmation. : {}", e.getMessage());
        return baseConfirmServiceHelper.baseHandleException(request, e);
    }

    @Override
    protected void saveFailedLog(ConfirmationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache draftCache, BasePrepareDataConfirm prepareData, CustomLogsConfirm logEvents, Exception e) {
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        var activityLog = logEvents.getActivityCustomBillPayConfirmationEvent();
        var transactionActivityLog = logEvents.getTransactionActivityCustomBillPay();
        var financialLog = logEvents.getFinancialCustomBillPayActivityLog();

        activityLog.setFailureStatusWithReasonFromException(e);
        transactionActivityLog.setTransactionStatus(ACTIVITY_FAILURE);
        financialLog.setFailureStatusWithErrorCodeFromException(e);

        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivityLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveActivityLog(activityLog));
        asyncHelper.executeMethodAsyncSafelyVoid(() -> logEventPublisherService.saveFinancialLog(correlationId, financialLog));
    }
}