package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityEventForValidate;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.PrepareDataForValidate;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import org.springframework.http.HttpHeaders;

public abstract class ValidationProcessingTemplate<
        P extends PrepareDataForValidate,
        E extends ExternalValidateResponse,
        V extends ValidateDataAfterCallExternal,
        A extends ActivityEventForValidate> implements ValidationCommonPaymentProcessor {

    public ValidationCommonPaymentResponse executeValidate(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException, TMBCommonExceptionWithResponse {
        P prepareData = null;
        E externalResponse = null;
        V validateDataAfterCallExternal = null;

        A activityEvent = initialActivityLog(request, headers, cache);
        try {
            prepareData = prepareData(request, headers, cache);

            prepareData = validateData(request, headers, cache, prepareData);

            externalResponse = validateWithExternalService(request, headers, cache, prepareData);

            validateDataAfterCallExternal = validateAfterCallExternalService(request, headers, cache, prepareData, externalResponse);

            saveSuccessLog(request, headers, cache, prepareData, externalResponse, validateDataAfterCallExternal, activityEvent);

            updateCache(request, headers, cache, prepareData, externalResponse, validateDataAfterCallExternal);

            return mappingResponse(request, headers, cache, prepareData, externalResponse, validateDataAfterCallExternal);
        } catch (Exception e) {

            saveFailedLog(request, headers, cache, activityEvent, prepareData, e);

            throw handleException(request, headers, cache, prepareData, externalResponse, validateDataAfterCallExternal, e);
        }
    }

    protected abstract A initialActivityLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache);

    protected abstract P validateData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, P prepareData) throws TMBCommonException;

    protected abstract P prepareData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException;

    protected abstract E validateWithExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, P prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse;

    protected abstract V validateAfterCallExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, P prepareData, E externalResponse) throws TMBCommonException;

    protected abstract void saveSuccessLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, P prepareData, E externalResponse, V validateDataAfterCallExternal, A activityEvent);

    protected abstract void updateCache(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, P prepareData, E externalResponse, V validateDataAfterCallExternal) throws JsonProcessingException;

    protected abstract ValidationCommonPaymentResponse mappingResponse(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, P prepareData, E externalResponse, V validateDataAfterCallExternal) throws TMBCommonException;

    protected abstract TMBCommonException handleException(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, P prepareData, E externalResponse, V validateDataAfterCallExternal, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse;

    protected abstract void saveFailedLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, A activityEvent, P prepareData, Exception e);

}
