package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp.custombillpayment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.cache.Transaction;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayMapper;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayValidationEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.BillPayValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ValidationProcessingTemplate;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp.OCPValidationHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_TOP_UP_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TOP_UP_REF_SEQUENCE_DIGIT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.TRANSFER_REFERENCE_NUMBER_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialActivityLog.BILLER_GROUP_TOP_UP;

@RequiredArgsConstructor
public abstract class CustomBillPayValidationProcessor extends ValidationProcessingTemplate<
        BillPayPrepareDataValidate,
        BillPayExternalValidateResponse,
        BillPayValidateDataAfterCallExternal,
        ActivityBillPayValidationEvent> {
    private static final TMBLogger<CustomBillPayValidationProcessor> logger = new TMBLogger<>(CustomBillPayValidationProcessor.class);
    private final PaymentService paymentService;
    private final LogEventPublisherService logEventPublisherService;
    private final CacheService cacheService;
    private final DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    private final BaseBillPayValidator baseBillPayValidator;
    private final OCPValidationHelper ocpValidationHelper;
    private final CacheMapper cacheMapper;
    protected final OCPBillRequestMapper ocpBillRequestMapper;

    protected abstract void validateAmount(ValidationCommonPaymentRequest request, OCPBillPayment ocpDataResponse) throws TMBCommonException;

    protected abstract OCPBillRequest createOCPBillPaymentValidateRequest(ValidationCommonPaymentRequest request, DepositAccount fromDepositAccount, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, CreditCardSupplementary creditCardDetail);

    @Override
    protected BillPayPrepareDataValidate validateData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData) throws TMBCommonException {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final boolean isPayWithWowPoint = !ObjectUtils.isEmpty(cache.getCommonPaymentRule())
                && cache.getCommonPaymentRule().isWowPointFlag() && !ObjectUtils.isEmpty(request.getWowPoint());
        final boolean isNotPayWithCreditCard = !request.getCreditCard().isPayWithCreditCardFlag();

        this.checkBillerExpired(masterBillerResponse);
        this.checkServiceHours(masterBillerResponse);
        this.validateDailyLimitExceeded(request, masterBillerResponse, customerCrmProfile);
        if (isPayWithWowPoint && isNotPayWithCreditCard) {
            baseBillPayValidator.validatePostAuthenticateTransaction(headers.getFirst(HEADER_CORRELATION_ID), request.getTransactionId());
            baseBillPayValidator.validateWowPoint(request, cache);
        }

        return prepareData;
    }

    @Override
    protected BillPayExternalValidateResponse validateWithExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();
        final CreditCardSupplementary creditCardDetail = prepareData.getFromCreditCardDetail();
        BillPayExternalValidateResponse externalValidateResponse = new BillPayExternalValidateResponse();

        OCPBillRequest ocpRequest = createOCPBillPaymentValidateRequest(request, fromDepositAccount, prepareData.getMasterBillerResponse(), cache, creditCardDetail);

        OCPBillPaymentResponse ocpDataResponse = paymentService.validateOCPBillPayment(correlationId, crmId, ocpRequest);

        externalValidateResponse.setOcpRequest(ocpRequest);
        externalValidateResponse.setOcpDataResponse(ocpDataResponse.getData());
        return externalValidateResponse;
    }

    @Override
    protected BillPayValidateDataAfterCallExternal validateAfterCallExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData, BillPayExternalValidateResponse externalResponse) throws TMBCommonException {
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final OCPBillPayment ocpDataResponse = externalResponse.getOcpDataResponse();
        final CreditCardSupplementary creditCardDetail = prepareData.getFromCreditCardDetail();
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();
        final boolean isPayWithCreditCard = request.getCreditCard().isPayWithCreditCardFlag();
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();

        final BigDecimal fee = getFee(masterBillerResponse, fromDepositAccount, isPayWithCreditCard);

        final BigDecimal amount = request.getAmount();

        this.validateAmount(request, ocpDataResponse);

        baseBillPayValidator.validateInsufficientFund(request, creditCardDetail, fromDepositAccount, fee);

        CommonAuthenResult commonAuthenResult = this.validateIsRequireCommonAuthen(request, headers, isPayWithCreditCard, customerCrmProfile, masterBillerResponse);

        CommonAuthenticationValidationCommonPaymentResponse commonAuthenResponse = null;
        if (commonAuthenResult.isRequireCommonAuthen()) {
            String flowName = this.getFlowName(masterBillerResponse);
            commonAuthenResponse = new CommonAuthenticationValidationCommonPaymentResponse()
                    .setTotalPaymentAccumulateUsage(new BigDecimal(ocpDataResponse.getAmount()).add(customerCrmProfile.getPaymentAccuUsgAmt()))
                    .setFlowName(flowName)
                    .setFeatureId(COMMON_PAYMENT_BILL_PAY_FEATURE_ID)
                    .setBillerCompCode(cache.getPaymentInformation().getCompCode());
        }

        BigDecimal totalAmount = amount.add(fee);
        if (WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            totalAmount = totalAmount.subtract(request.getWowPoint().getDiscountAmount());
        }
        return new BillPayValidateDataAfterCallExternal()
                .setRequireCommonAuthen(commonAuthenResult.isRequireCommonAuthen())
                .setCommonAuthentication(commonAuthenResponse)
                .setFeeAfterCalculated(fee)
                .setTotalAmount(totalAmount)
                .setCommonAuthenResult(commonAuthenResult);
    }

    protected BigDecimal getFee(MasterBillerResponse masterBillerResponse, DepositAccount fromDepositAccount, boolean isPayWithCreditCard) {
        BigDecimal fee = masterBillerResponse.getBillerInfo().getFee().setScale(2, RoundingMode.HALF_UP);
        if (isPayWithCreditCard || fromDepositAccount.getWaiveFeeForBillpay().equals("1")) {
            fee = new BigDecimal("0.00");
        }
        return fee;
    }

    @Override
    protected void saveSuccessLog(ValidationCommonPaymentRequest request,
                                  HttpHeaders headers,
                                  CommonPaymentDraftCache cache,
                                  BillPayPrepareDataValidate prepareData,
                                  BillPayExternalValidateResponse externalResponse,
                                  BillPayValidateDataAfterCallExternal validateDataAfterCallExternal,
                                  ActivityBillPayValidationEvent activityBillPayValidationEvent) {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final CreditCardSupplementary creditCardDetail = prepareData.getFromCreditCardDetail();

        ActivityBillPayValidationEvent activityEventData = activityBillPayValidationEvent;
        activityEventData = ActivityBillPayMapper.INSTANCE.updateActivityBillPayValidation(activityEventData, masterBillerResponse, cache, request, creditCardDetail, validateDataAfterCallExternal);

        logEventPublisherService.saveActivityLog(activityEventData);
    }

    @Override
    protected ActivityBillPayValidationEvent initialActivityLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) {
        return ActivityBillPayMapper.INSTANCE.toActivityBillPayValidation(headers, request, cache);
    }

    @Override
    protected BillPayPrepareDataValidate prepareData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException {
        return ocpValidationHelper.basePrepareData(request, headers, cache);
    }

    private void checkServiceHours(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateServiceHours(masterBillerResponse);
    }

    private void validateDailyLimitExceeded(ValidationCommonPaymentRequest request, MasterBillerResponse masterBiller, CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        baseBillPayValidator.validateDailyLimit(request, masterBiller, customerCrmProfile);
    }

    private void checkBillerExpired(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateBillerExpiration(masterBillerResponse);
    }

    private String getFlowName(MasterBillerResponse masterBillerResponse) {
        return masterBillerResponse.getBillerInfo().getBillerGroupType().equals(BILLER_GROUP_TOP_UP) ? COMMON_AUTH_TOP_UP_FLOW_NAME : COMMON_AUTH_BILL_FLOW_NAME;
    }

    private CommonAuthenResult validateIsRequireCommonAuthen(ValidationCommonPaymentRequest request, HttpHeaders headers, boolean isPayWithCreditCard, CustomerCrmProfile customerCrmProfile, MasterBillerResponse masterBiller) throws TMBCommonException {
        BigDecimal amount = isPayWithCreditCard ? request.getCreditCard().getAmount() : request.getDeposit().getAmount();

        return dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(headers, amount, false, customerCrmProfile);

    }

    @Override
    protected void saveFailedLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, ActivityBillPayValidationEvent activityEvent, BillPayPrepareDataValidate prepareData, Exception e) {
        final MasterBillerResponse masterBiller = Optional.ofNullable(prepareData).map(BillPayPrepareDataValidate::getMasterBillerResponse).orElse(null);
        final CreditCardSupplementary creditCardDetail = Optional.ofNullable(prepareData).map(BillPayPrepareDataValidate::getFromCreditCardDetail).orElse(null);

        activityEvent = ActivityBillPayMapper.INSTANCE.updateActivityBillPayValidationFailed(activityEvent, masterBiller, cache, request, creditCardDetail, e);

        logEventPublisherService.saveActivityLog(activityEvent);
    }

    @Override
    protected TMBCommonException handleException(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData, BillPayExternalValidateResponse externalResponse, BillPayValidateDataAfterCallExternal validateDataAfterCallExternal, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process CustomBillPayValidation. : {}", e.getMessage(), e);
        if (e instanceof TMBCommonException tx) {
            throw tx;
        } else if (e instanceof TMBCommonExceptionWithResponse twx) {
            throw twx;
        } else {
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
        }
    }

    @Override
    protected void updateCache(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData, BillPayExternalValidateResponse externalResponse, BillPayValidateDataAfterCallExternal validateDataAfterCallExternal) throws JsonProcessingException {
        final CreditCardSupplementary creditCardDetail = prepareData.getFromCreditCardDetail();
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final var commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String compCode = cache.getPaymentInformation().getCompCode();
        final String ePayCode = this.getTransactionId(TRANSFER_REFERENCE_NUMBER_PREFIX, TOP_UP_REF_SEQUENCE_DIGIT);

        WowPointRedeemConfirmRequest wowPointRedeemConfirmRequest = null;
        if (WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            wowPointRedeemConfirmRequest = WowPointRedeemConfirmRequestMapper.INSTANCE.mapToWowPointRedeemConfirmRequestForOCPAndCustomBill(request, compCode, crmId, ePayCode);
        }

        OCPBillRequest ocpBillPaymentConfirmRequest = ocpBillRequestMapper.toOCPBillRequestForConfirmCustomOCPBillPayment(request, masterBillerResponse, cache, externalResponse, prepareData, ePayCode, wowPointRedeemConfirmRequest);

        var validateDraftCache = new ValidationCommonPaymentDraftCache()
                .setExternalConfirmRequest(new ExternalConfirmRequest()
                        .setOcpBillPaymentConfirmRequest(ocpBillPaymentConfirmRequest))
                .setRequireCommonAuthen(validateDataAfterCallExternal.isRequireCommonAuthen())
                .setFromDepositAccount(cacheMapper.toDepositAccountInCache(prepareData.getFromDepositAccount()))
                .setFromCreditCardDetail(cacheMapper.toCreditCardSupplementaryInCache(creditCardDetail))
                .setMasterBillerResponse(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse))
                .setCommonAuthentication(commonAuthentication)
                .setFeeCalculated(validateDataAfterCallExternal.getFeeAfterCalculated())
                .setTotalAmount(validateDataAfterCallExternal.getTotalAmount())
                .setWowPointRedeemConfirmRequest(wowPointRedeemConfirmRequest);

        cache.setCrmId(crmId);
        cache.setValidateRequest(request);
        cache.setValidateDraftCache(validateDraftCache);

        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + request.getTransactionId();
        cacheService.set(cacheKey, COMMON_PAYMENT_HASH_KEY_CACHE, cache);
    }

    protected String getTransactionId(String key, int digits) {
        return Transaction.getTransactionId(key, digits);
    }
}
