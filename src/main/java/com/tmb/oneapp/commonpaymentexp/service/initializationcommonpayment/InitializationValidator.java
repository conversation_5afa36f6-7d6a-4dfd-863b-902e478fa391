package com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitialPrepareData;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import org.springframework.http.HttpHeaders;

@FunctionalInterface
public interface InitializationValidator {
    void validateData(InitializationCommonPaymentRequest initializationCommonPaymentRequest, HttpHeaders headers, InitialPrepareData prepareData) throws TMBCommonException;
}
