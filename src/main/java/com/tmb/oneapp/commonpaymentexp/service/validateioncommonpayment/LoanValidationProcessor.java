package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.BillerConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.LoanAccount;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayMapper;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayValidationEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.AdditionalParamCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentResponseMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.loan.LoanPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.loan.LoanValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.loanaccount.HomeLoanFullInfoResponse;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.AccountService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerAccountBizService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.WowPointUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_LOAN;
import static com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequestMapper.LOAN_ACCOUNT_PREFIX;

@Service
@RequiredArgsConstructor
public class LoanValidationProcessor extends ValidationProcessingTemplate<
        LoanPrepareDataValidate,
        OCPBillPayment,
        LoanValidateDataAfterCallExternal,
        ActivityBillPayValidationEvent> {
    private static final TMBLogger<LoanValidationProcessor> logger = new TMBLogger<>(LoanValidationProcessor.class);

    private final AsyncHelper asyncHelper;
    private final PaymentService paymentService;
    private final BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;
    private final CustomerService customerService;
    private final LogEventPublisherService logEventPublisherService;
    private final DailyLimitService dailyLimitService;
    private final CacheService cacheService;
    private final DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    private final BaseBillPayValidator baseBillPayValidator;
    private final CustomerAccountBizService customerAccountBizService;
    private final AccountService accountService;
    private final CacheMapper cacheMapper;
    private final OCPBillRequestMapper ocpBillRequestMapper;

    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_TMB_LOAN;
    }

    @Override
    protected ActivityBillPayValidationEvent initialActivityLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) {
        return ActivityBillPayMapper.INSTANCE.toActivityBillPayValidation(headers, request, cache);
    }

    @Override
    protected LoanPrepareDataValidate prepareData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache) throws TMBCommonException {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);
        final String compCode = cache.getPaymentInformation().getCompCode();
        final String ref1 = cache.getPaymentInformation().getProductDetail().getProductRef1();
        final String ref2 = cache.getPaymentInformation().getProductDetail().getProductRef2();
        final String loanAccountID = LOAN_ACCOUNT_PREFIX + ref1 + ref2;

        CompletableFuture<BillPayConfiguration> billPayConfigFuture = asyncHelper.executeMethodAsync(() -> paymentService.getBillPayConfig(correlationId));
        CompletableFuture<MasterBillerResponse> masterBillerFuture = asyncHelper.executeMethodAsync(() -> paymentService.getMasterBiller(correlationId, compCode));
        CompletableFuture<CustomerCrmProfile> customerCrmProfileFuture = asyncHelper.executeMethodAsync(() -> customerService.getCustomerCrmProfile(correlationId, crmId));
        CompletableFuture<DepositAccount> depositAccountFuture = asyncHelper.executeMethodAsync(() -> billPayAccountCommonPaymentService.getAccountByAccountNumber(request.getDeposit().getAccountNumber(), headers));
        CompletableFuture<List<LoanAccount>> loanAccountListFuture = asyncHelper.executeMethodAsync(() -> customerAccountBizService.fetchLoanAccount(correlationId, crmId));
        CompletableFuture<HomeLoanFullInfoResponse> homeLoanFuture = asyncHelper.executeMethodAsync(() -> accountService.fetchLoanAccountByLoanAccountID(correlationId, loanAccountID));

        CompletableFuture.allOf(billPayConfigFuture, masterBillerFuture, customerCrmProfileFuture, depositAccountFuture, loanAccountListFuture, homeLoanFuture);
        try {
            return LoanPrepareDataValidate.builder()
                    .billPayConfiguration(billPayConfigFuture.get())
                    .masterBillerResponse(masterBillerFuture.get())
                    .customerCrmProfile(customerCrmProfileFuture.get())
                    .fromDepositAccount(depositAccountFuture.get())
                    .loanAccountList(loanAccountListFuture.get())
                    .homeLoanForValidateDebt(homeLoanFuture.get())
                    .build();
        } catch (ExecutionException | InterruptedException | ThreadDeath e) {
            if (e.getCause() instanceof TMBCommonException ex) {
                logger.error("Error ExecutionException TMBCommonException in method PrepareData : {}", ex.getMessage(), e);
                throw ex;
            }
            Thread.currentThread().interrupt();
            logger.error("Error ExecutionException in method PrepareData", e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Error execution exception get prepare data. Please verify prepareData method");
        }
    }

    @Override
    protected LoanPrepareDataValidate validateData(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, LoanPrepareDataValidate prepareData) throws TMBCommonException {
        final BillPayConfiguration billPayConfiguration = prepareData.getBillPayConfiguration();
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final HomeLoanFullInfoResponse homeLoan = prepareData.getHomeLoanForValidateDebt();
        final String compCode = cache.getPaymentInformation().getCompCode();
        final BigDecimal amount = request.getDeposit().getAmount();
        final String ref1 = cache.getPaymentInformation().getProductDetail().getProductRef1();
        final String ref2 = cache.getPaymentInformation().getProductDetail().getProductRef2();
        final String loanAccountID = LOAN_ACCOUNT_PREFIX + ref1 + ref2;
        final List<LoanAccount> loanAccountList = prepareData.getLoanAccountList();
        final boolean isPayByOwner = this.isIsPayByOwner(loanAccountList, loanAccountID);
        final boolean isPayWithWowPoint = !ObjectUtils.isEmpty(cache.getCommonPaymentRule())
                && cache.getCommonPaymentRule().isWowPointFlag() && !ObjectUtils.isEmpty(request.getWowPoint());

        this.checkCompCodeInExcludeBillerConfig(billPayConfiguration, compCode);
        this.transformRequestBody(cache.getPaymentInformation().getProductDetail());
        this.checkBillerExpired(masterBillerResponse);
        this.checkServiceHours(masterBillerResponse);
        this.validateAccruedDebt(homeLoan, amount);

        if (!isPayByOwner) {
            this.validateDailyLimitExceeded(request, masterBillerResponse, customerCrmProfile);
        }

        if (compCode.equals(BillerConstant.BILL_COMP_CODE_HOME_LOAN) && isPayWithWowPoint) {
            baseBillPayValidator.validatePostAuthenticateTransaction(headers.getFirst(HEADER_CORRELATION_ID), request.getTransactionId());
            baseBillPayValidator.validateWowPoint(request, cache);
        }

        return prepareData;
    }

    private void validateAccruedDebt(HomeLoanFullInfoResponse homeLoanFullInfoResponse, BigDecimal amount) throws TMBCommonException {
        BigDecimal deptPayOff = new BigDecimal(homeLoanFullInfoResponse.getAccount().getBalances().getPayoff());
        boolean isDeptLessThanAmount = deptPayOff.compareTo(amount) < 0;
        if (isDeptLessThanAmount) {
            logger.error("Error Accrued Debt : when deptPayOff less than amount. [deptPayOff = {}, amount = {}]", deptPayOff, amount);
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ACCRUED_DEBT_ERROR);
        }
    }

    private void validateDailyLimitExceeded(ValidationCommonPaymentRequest request, MasterBillerResponse masterBiller, CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        BigDecimal amount = request.getDeposit().getAmount();
        dailyLimitService.validateDailyLimitExceeded(masterBiller.getBillerInfo().getBillerGroupType(), customerCrmProfile, amount);
    }

    protected void checkCompCodeInExcludeBillerConfig(BillPayConfiguration configData, String compCode) throws TMBCommonException {
        baseBillPayValidator.validateCompCodeExclusion(configData, compCode);
    }

    protected void transformRequestBody(ProductDetail productDetail) {
        productDetail.setProductRef1(StringUtils.upperCase(productDetail.getProductRef1()));
        productDetail.setProductRef2(StringUtils.upperCase(productDetail.getProductRef2()));
    }

    protected void checkServiceHours(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateServiceHours(masterBillerResponse);
    }

    protected void checkBillerExpired(MasterBillerResponse masterBillerResponse) throws TMBCommonException {
        baseBillPayValidator.validateBillerExpiration(masterBillerResponse);
    }


    @Override
    protected OCPBillPayment validateWithExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, LoanPrepareDataValidate prepareData) throws TMBCommonException, TMBCommonExceptionWithResponse {
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String correlationId = headers.getFirst(HEADER_CORRELATION_ID);

        OCPBillRequest ocpRequest = ocpBillRequestMapper.toOCPBillRequestForValidateLoanPayment(request, prepareData.getFromDepositAccount(), cache);

        return paymentService.validateOCPBillPayment(correlationId, crmId, ocpRequest).getData();
    }


    @Override
    protected LoanValidateDataAfterCallExternal validateAfterCallExternalService(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, LoanPrepareDataValidate prepareData, final OCPBillPayment externalResponse) throws TMBCommonException {
        final String compCode = cache.getPaymentInformation().getCompCode();
        final CustomerCrmProfile customerCrmProfile = prepareData.getCustomerCrmProfile();
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();
        final List<LoanAccount> loanAccountList = prepareData.getLoanAccountList();
        final String ref1 = cache.getPaymentInformation().getProductDetail().getProductRef1();
        final String ref2 = cache.getPaymentInformation().getProductDetail().getProductRef2();
        final String loanAccountID = LOAN_ACCOUNT_PREFIX + ref1 + ref2;

        BigDecimal fee = this.calculateFee(externalResponse, fromDepositAccount);

        boolean isPayByOwner = this.isIsPayByOwner(loanAccountList, loanAccountID);

        CommonAuthenResult commonAuthenResult = this.validateIsRequireCommonAuthen(request, headers, customerCrmProfile, isPayByOwner);

        CommonAuthenticationValidationCommonPaymentResponse commonAuthenResponse = null;
        if (commonAuthenResult.isRequireCommonAuthen()) {
            commonAuthenResponse = new CommonAuthenticationValidationCommonPaymentResponse()
                    .setTotalPaymentAccumulateUsage(new BigDecimal(externalResponse.getAmount()).add(customerCrmProfile.getPaymentAccuUsgAmt()))
                    .setFlowName(COMMON_AUTH_BILL_FLOW_NAME)
                    .setFeatureId(COMMON_PAYMENT_BILL_PAY_FEATURE_ID)
                    .setBillerCompCode(compCode);
        }

        BigDecimal amount = request.getDeposit().getAmount();
        BigDecimal totalAmount = amount.add(fee);
        if (compCode.equals(BillerConstant.BILL_COMP_CODE_HOME_LOAN) && WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            totalAmount = totalAmount.subtract(request.getWowPoint().getDiscountAmount());
        }

        return new LoanValidateDataAfterCallExternal()
                .setRequireCommonAuthen(commonAuthenResult.isRequireCommonAuthen())
                .setCommonAuthentication(commonAuthenResponse)
                .setFeeAfterCalculated(fee)
                .setTotalAmount(totalAmount)
                .setCommonAuthenResult(commonAuthenResult)
                .setPayByOwner(isPayByOwner);
    }

    private boolean isIsPayByOwner(List<LoanAccount> loanAccountList, String loanAccountID) {
        return loanAccountList.stream().anyMatch(ac -> ac.getAccountNumber().equals(loanAccountID));
    }

    protected BigDecimal calculateFee(OCPBillPayment ocpBillPayment, DepositAccount fromDepositAccount) {
        BigDecimal fee;
        boolean isWaiveFeeFromETE = ocpBillPayment.getWaive() != null && ocpBillPayment.getWaive().getFlag().equals("Y");
        boolean isWaiveFeeFromDepositAccount = StringUtils.equals("1", fromDepositAccount.getWaiveFeeForBillpay());
        if (isWaiveFeeFromETE || isWaiveFeeFromDepositAccount) {
            fee = new BigDecimal("0.00");
        } else if (ocpBillPayment.getFee().getBillPmtFee() != null) {
            fee = new BigDecimal(ocpBillPayment.getFee().getBillPmtFee());
        } else if (ocpBillPayment.getFee().getPaymentFee() != null) {
            fee = new BigDecimal(ocpBillPayment.getFee().getPaymentFee());
        } else {
            fee = new BigDecimal("0.00");
        }
        return fee;
    }

    private CommonAuthenResult validateIsRequireCommonAuthen(ValidationCommonPaymentRequest request, HttpHeaders headers, CustomerCrmProfile customerCrmProfile, boolean isPayByOwner) throws TMBCommonException {
        BigDecimal amount = request.getDeposit().getAmount();

        return dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(headers, amount, isPayByOwner, customerCrmProfile);
    }

    @Override
    protected void saveSuccessLog(ValidationCommonPaymentRequest request,
                                  HttpHeaders headers,
                                  CommonPaymentDraftCache cache,
                                  LoanPrepareDataValidate prepareData,
                                  OCPBillPayment externalResponse,
                                  LoanValidateDataAfterCallExternal validateDataAfterCallExternal,
                                  ActivityBillPayValidationEvent activityEvent) {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();

        ActivityBillPayValidationEvent activityEventData = activityEvent;
        activityEventData = ActivityBillPayMapper.INSTANCE.updateActivityLoanValidation(activityEventData, masterBillerResponse, cache, validateDataAfterCallExternal);

        logEventPublisherService.saveActivityLog(activityEventData);
    }

    @Override
    protected void saveFailedLog(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, ActivityBillPayValidationEvent activityEvent, LoanPrepareDataValidate prepareData, Exception e) {
        final MasterBillerResponse masterBiller = Optional.ofNullable(prepareData).map(LoanPrepareDataValidate::getMasterBillerResponse).orElse(null);

        activityEvent = ActivityBillPayMapper.INSTANCE.updateActivityLoanValidationFailed(activityEvent, masterBiller, cache, e);

        logEventPublisherService.saveActivityLog(activityEvent);
    }

    @Override
    protected TMBCommonException handleException(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, LoanPrepareDataValidate prepareData, OCPBillPayment externalResponse, LoanValidateDataAfterCallExternal validateDataAfterCallExternal, Exception e) throws TMBCommonException, TMBCommonExceptionWithResponse {
        logger.error("ERROR process LoanValidation. : {}", e.getMessage(), e);
        if (e instanceof TMBCommonException ex) {
            throw ex;
        } else if (e instanceof TMBCommonExceptionWithResponse ex) {
            throw ex;
        } else {
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
        }
    }

    @Override
    protected void updateCache(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, LoanPrepareDataValidate prepareData, final OCPBillPayment externalResponse, LoanValidateDataAfterCallExternal validateDataAfterCallExternal) throws JsonProcessingException {
        final MasterBillerResponse masterBillerResponse = prepareData.getMasterBillerResponse();
        final var commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final DepositAccount fromDepositAccount = prepareData.getFromDepositAccount();
        final String crmId = headers.getFirst(HEADER_CRM_ID);
        final String compCode = cache.getPaymentInformation().getCompCode();
        final String ePayCode = externalResponse.getEpayCode();

        WowPointRedeemConfirmRequest wowPointRedeemConfirmRequest = null;
        if (compCode.equals(BillerConstant.BILL_COMP_CODE_HOME_LOAN) && WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            wowPointRedeemConfirmRequest = WowPointRedeemConfirmRequestMapper.INSTANCE.mapToWowPointRedeemConfirmRequestForOCPAndCustomBill(request, compCode, crmId, ePayCode);
        }

        OCPBillRequest ocpBillPaymentConfirmRequest = ocpBillRequestMapper.toOCPBillRequestForConfirmLoanPayment(externalResponse, fromDepositAccount, request, cache, wowPointRedeemConfirmRequest);

        var validateDraftCache = new ValidationCommonPaymentDraftCache()
                .setExternalConfirmRequest(new ExternalConfirmRequest()
                        .setOcpBillPaymentConfirmRequest(ocpBillPaymentConfirmRequest))
                .setRequireCommonAuthen(validateDataAfterCallExternal.isRequireCommonAuthen())
                .setFromDepositAccount(cacheMapper.toDepositAccountInCache(fromDepositAccount))
                .setMasterBillerResponse(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse))
                .setCommonAuthentication(commonAuthentication)
                .setFeeCalculated(validateDataAfterCallExternal.getFeeAfterCalculated())
                .setTotalAmount(validateDataAfterCallExternal.getTotalAmount())
                .setWowPointRedeemConfirmRequest(wowPointRedeemConfirmRequest)
                .setAdditionalParam(new AdditionalParamCommonPaymentDraftCache()
                        .setPayByOwner(validateDataAfterCallExternal.isPayByOwner())
                );

        cache.setValidateRequest(request);
        cache.setValidateDraftCache(validateDraftCache);

        String cacheKey = COMMON_PAYMENT_CACHE_PREFIX + request.getTransactionId();
        cacheService.set(cacheKey, COMMON_PAYMENT_HASH_KEY_CACHE, cache);
    }

    @Override
    protected ValidationCommonPaymentResponse mappingResponse(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, LoanPrepareDataValidate prepareData, final OCPBillPayment externalResponse, LoanValidateDataAfterCallExternal validateDataAfterCallExternal) {
        final boolean isRequireCommonAuthen = validateDataAfterCallExternal.isRequireCommonAuthen();
        final BigDecimal feeAfterCalculated = validateDataAfterCallExternal.getFeeAfterCalculated();
        final BigDecimal totalAmount = validateDataAfterCallExternal.getTotalAmount();
        final var commonAuthentication = validateDataAfterCallExternal.getCommonAuthentication();
        final String compCode = cache.getPaymentInformation().getCompCode();

        ValidationCommonPaymentResponse response = new ValidationCommonPaymentResponse();
        response.setTransactionId(request.getTransactionId());
        response.setFee(feeAfterCalculated);
        response.setAmount(new BigDecimal(externalResponse.getAmount()));
        response.setTotalAmount(totalAmount);
        response.setIsRequireCommonAuthen(isRequireCommonAuthen);

        if (isRequireCommonAuthen) {
            response.setCommonAuthenticationInformation(commonAuthentication);
        }

        if (compCode.equals(BillerConstant.BILL_COMP_CODE_HOME_LOAN) && WowPointUtils.isWowPointTransaction(request, cache.getCommonPaymentRule())) {
            response.setWowPoint(WowPointValidationCommonPaymentResponseMapper.INSTANCE.mapWowPointValidationCommonPaymentResponse(request));
        }

        return response;
    }

}
