package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.client.TransferServiceClient;
import com.tmb.oneapp.commonpaymentexp.model.TransferConfiguration;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TransferService {
    private static final TMBLogger<TransferService> logger = new TMBLogger<>(TransferService.class);

    private final TransferServiceClient transferServiceClient;
    private final FeignClientHelper feignClientHelper;

    public TransferConfiguration fetchTransferModuleConfig(String correlationId) throws TMBCommonException {
        logger.info(">>> call get transfer_module_config from transfer service <<<");
        return feignClientHelper.executeRequest(() -> transferServiceClient.getTransferModuleConfig(correlationId));
    }

}
