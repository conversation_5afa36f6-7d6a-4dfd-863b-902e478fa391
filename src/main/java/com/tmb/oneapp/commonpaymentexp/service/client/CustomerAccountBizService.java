package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.oneapp.commonpaymentexp.client.CustomerAccountBizClient;
import com.tmb.oneapp.commonpaymentexp.model.AccountSaving;
import com.tmb.oneapp.commonpaymentexp.model.LoanAccount;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class CustomerAccountBizService {
    private final CustomerAccountBizClient customerAccountBizClient;
    private final FeignClientHelper feignClientHelper;

    @LogAround
    public List<LoanAccount> fetchLoanAccount(String correlationId, String crmId) throws TMBCommonException {
        AccountSaving accountSaving = feignClientHelper.executeRequest(() -> customerAccountBizClient.getAccountList(correlationId, crmId));
        return accountSaving.getLoanAccounts();
    }
}
