package com.tmb.oneapp.commonpaymentexp.service.dailylimit;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.AccumulateUsageRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;

@FunctionalInterface
public interface AccumulateUsageStrategy {
    AccumulateUsageRequest createRequest(CommonPaymentDraftCache draftCache, CustomerCrmProfile profile);
}
