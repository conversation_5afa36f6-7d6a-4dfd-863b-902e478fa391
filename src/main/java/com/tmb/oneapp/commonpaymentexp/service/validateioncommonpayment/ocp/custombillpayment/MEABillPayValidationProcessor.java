package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp.custombillpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequestMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.BillPayValidateDataAfterCallExternal;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponseMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayExternalValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp.OCPValidationHelper;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_MEA;

@Service
public class MEABillPayValidationProcessor extends CustomBillPayValidationProcessor {
    private static final TMBLogger<MEABillPayValidationProcessor> logger = new TMBLogger<>(MEABillPayValidationProcessor.class);

    public MEABillPayValidationProcessor(PaymentService paymentService, LogEventPublisherService logEventPublisherService, CacheService cacheService, DailyLimitPinFreeValidator dailyLimitPinFreeValidator, BaseBillPayValidator baseBillPayValidator, OCPValidationHelper ocpValidationHelper, CacheMapper cacheMapper, OCPBillRequestMapper ocpBillRequestMapper) {
        super(paymentService, logEventPublisherService, cacheService, dailyLimitPinFreeValidator, baseBillPayValidator, ocpValidationHelper, cacheMapper, ocpBillRequestMapper);
    }


    @Override
    public String getProcessorType() {
        return BILLER_PAYMENT_MEA;
    }

    @Override
    protected OCPBillRequest createOCPBillPaymentValidateRequest(ValidationCommonPaymentRequest request, DepositAccount fromDepositAccount, MasterBillerResponse masterBillerResponse, CommonPaymentDraftCache cache, CreditCardSupplementary creditCardDetail) {
        return ocpBillRequestMapper.toOCPBillRequestForValidateMEAOCPBillPayment(request, fromDepositAccount, cache, creditCardDetail);
    }

    @Override
    protected void validateAmount(ValidationCommonPaymentRequest request, OCPBillPayment ocpDataResponse) throws TMBCommonException {
        BigDecimal meaAmount = new BigDecimal(ocpDataResponse.getAmount());
        BigDecimal requestAmount = request.getAmount();
        if (meaAmount.compareTo(requestAmount) != 0) {
            logger.error("Error amount not match. Please verify amount in request and response. [request.amount = {}, response.amount = {}]", requestAmount, meaAmount);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.MEA_AMOUNT_NOT_MATCH_ERROR);
        }
    }

    @Override
    protected ValidationCommonPaymentResponse mappingResponse(ValidationCommonPaymentRequest request, HttpHeaders headers, CommonPaymentDraftCache cache, BillPayPrepareDataValidate prepareData, BillPayExternalValidateResponse externalResponse, BillPayValidateDataAfterCallExternal validateDataAfterCallExternal) {
        return ValidationCommonPaymentResponseMapper.INSTANCE.mapToValidationCommonPaymentResponseForMEA(request, cache, externalResponse, validateDataAfterCallExternal);
    }
}
