package com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Stream;

import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
import static com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper.ACCOUNT_STATUS_CLOSE;
import static com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper.ACCOUNT_STATUS_DORMANT;
import static com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper.IS_ACCOUNT_STATUS_INACTIVE_OR_ACTIVE;
import static com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper.IS_ALLOW_FROM_FOR_BILL_PAY_TOP_UP_E_PAYMENT_ONE;
import static com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper.IS_PRIIND;
import static com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper.isNotViewOnlyAccount;


@Service
public class BillPayAccountCommonPaymentService implements AccountCommonPaymentProcessor<List<DepositAccount>> {
    public static final TMBLogger<BillPayAccountCommonPaymentService> logger = new TMBLogger<>(BillPayAccountCommonPaymentService.class);
    private static final Predicate<DepositAccount> FILTER_FOR_BILL_PAY = IS_PRIIND.and(IS_ALLOW_FROM_FOR_BILL_PAY_TOP_UP_E_PAYMENT_ONE).and(IS_ACCOUNT_STATUS_INACTIVE_OR_ACTIVE).and(isNotViewOnlyAccount);
    private static final Predicate<DepositAccount> isNotCloseStatus = d -> !StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_CLOSE, d.getAccountStatus());
    private final AccountCommonPaymentHelper accountCommonPaymentHelper;

    public BillPayAccountCommonPaymentService(AccountCommonPaymentHelper accountCommonPaymentHelper) {
        this.accountCommonPaymentHelper = accountCommonPaymentHelper;
    }

    @Override
    public String getKey() {
        return COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
    }

    @Override
    public List<DepositAccount> getAccountList(String correlationId, String crmId) throws TMBCommonException {
        var depositAccountList = accountCommonPaymentHelper.fetchDepositAccountList(correlationId, crmId);
        return filterAndSetAvailableBalance(depositAccountList, correlationId, crmId);
    }

    public DepositAccount getAccountByAccountNumber(String accountNumber, HttpHeaders headers) throws TMBCommonException {
        var depositAccount = accountCommonPaymentHelper.getDepositAccountWithBalance(accountNumber, headers);

        boolean isDormantAccount = StringUtils.equalsIgnoreCase(ACCOUNT_STATUS_DORMANT, depositAccount.getAccountStatus());
        if (isDormantAccount) {
            logger.error("Error getAccountByAccountNumber, The account is Dormant status. Please verify the account [depositAccountNumber = {}]", accountNumber);
            throw CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.DEPOSIT_DORMANT_ACCOUNT_ERROR);
        }

        depositAccount = Stream.of(depositAccount)
                .filter(FILTER_FOR_BILL_PAY.and(isNotViewOnlyAccount).and(isNotCloseStatus))
                .findFirst()
                .orElseThrow(() -> {
                    logger.error("Error getAccountByAccountNumber. After filter account by bill_pay condition. [depositAccountNumber = {}]", accountNumber);
                    return CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR);
                });

        return depositAccount;
    }

    private List<DepositAccount> filterAndSetAvailableBalance(List<DepositAccount> depositAccountList, String correlationId, String crmId) throws TMBCommonException {
        depositAccountList = depositAccountList.stream().filter(FILTER_FOR_BILL_PAY).toList();
        depositAccountList = accountCommonPaymentHelper.setAvailableBalance(depositAccountList, correlationId, crmId);
        return depositAccountList;
    }
}
