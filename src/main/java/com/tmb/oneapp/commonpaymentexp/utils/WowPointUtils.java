package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import lombok.experimental.UtilityClass;

import java.util.Optional;

@UtilityClass
public class WowPointUtils {

    /**
     * Check transaction is pay with wow point.
     *
     * @param validateRequest          ValidationCommonPaymentRequest from api validate
     * @param commonPaymentRuleInCache CommonPaymentRuleInCache from cache save at api get payment method
     * @return boolean
     */
    public static boolean isWowPointTransaction(ValidationCommonPaymentRequest validateRequest, CommonPaymentRuleInCache commonPaymentRuleInCache) {
        return isRequestWowPointNotNull(validateRequest)
                && IsConfigAllowToPayWithWowPoint(commonPaymentRuleInCache)
                && isNotPayWithCreditCard(validateRequest);
    }

    private static boolean isNotPayWithCreditCard(ValidationCommonPaymentRequest validateRequest) {
        return !validateRequest.getCreditCard().isPayWithCreditCardFlag();
    }

    private static boolean isRequestWowPointNotNull(ValidationCommonPaymentRequest validateRequest) {
        return validateRequest.getWowPoint() != null;
    }

    private static Boolean IsConfigAllowToPayWithWowPoint(CommonPaymentRuleInCache commonPaymentRuleInCache) {
        return Optional.ofNullable(commonPaymentRuleInCache).map(CommonPaymentRuleInCache::isWowPointFlag).orElse(false);
    }
}
