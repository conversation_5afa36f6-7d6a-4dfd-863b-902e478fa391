package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableRunnable;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableSupplier;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;


@Component
public class AsyncHelper {
    private static final TMBLogger<AsyncHelper> logger = new TMBLogger<>(AsyncHelper.class);
    private final FeignClientHelper feignClientHelper;

    public AsyncHelper(FeignClientHelper feignClientHelper) {
        this.feignClientHelper = feignClientHelper;
    }

    @Async
    public <T> CompletableFuture<T> executeRequestAsync(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) throws TMBCommonException {
        return CompletableFuture.completedFuture(feignClientHelper.executeRequest(supplier));
    }

    @Async
    public <T> CompletableFuture<T> executeRequestAsyncSafely(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) {
        return CompletableFuture.completedFuture(feignClientHelper.executeRequestSafely(supplier));
    }

    @Async
    public <T> CompletableFuture<T> executeRequestAsyncOrElse(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier, T elseAction) {
        return CompletableFuture.completedFuture(feignClientHelper.executeRequestOrElse(supplier, elseAction));
    }

    @Async
    public <T> CompletableFuture<T> executeMethodAsyncSafely(ThrowableSupplier<T> supplier) {
        return CompletableFuture.completedFuture(executeMethodSafely(supplier));
    }

    @Async
    public void executeMethodAsyncSafelyVoid(ThrowableRunnable runnable) {
        try {
            runnable.run();
        } catch (Exception e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error("Method name {} : executeMethodAsync got UnhandledException.", callerMethodName, e);
        }
    }

    private <T> T executeMethodSafely(ThrowableSupplier<T> supplier) {
        try {
            return this.executeMethod(supplier);
        } catch (TMBCommonException e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error("Method name {} : executeMethodAsyncSafely got TMBCommonException Bug ignore exception.", callerMethodName, e);
            return null;
        }
    }

    @Async
    public <T> CompletableFuture<T> executeMethodAsync(ThrowableSupplier<T> supplier) throws TMBCommonException {
        return CompletableFuture.completedFuture(executeMethod(supplier));
    }

    private <T> T executeMethod(ThrowableSupplier<T> supplier) throws TMBCommonException {
        try {
            return supplier.get();
        } catch (TMBCommonException e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error("Method name {} : executeMethodAsync got TMBCommonException.", callerMethodName, e);
            throw e;
        } catch (Exception e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error("Method name {} : executeMethodAsync got UnhandledException.", callerMethodName, e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2);
        }
    }

    private static String getCallerMethodName(StackTraceElement[] stackTrace) {
        if (stackTrace == null || stackTrace.length < 3) {
            return StringUtils.EMPTY;
        }

        try {
            return StringUtils.joinWith(".", stackTrace[2].getMethodName(), stackTrace[1].getMethodName());
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
    }

}
