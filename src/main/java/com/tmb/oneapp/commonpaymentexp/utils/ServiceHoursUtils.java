package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.CommonTime;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ServiceHoursUtils {
    private static final TMBLogger<ServiceHoursUtils> logger = new TMBLogger<>(ServiceHoursUtils.class);
    private static final DateTimeFormatter FORMATTER_HH_MM = DateTimeFormatter.ofPattern("HH:mm");

    public static boolean isDuringServiceHours(Date currentTime, CommonTime nonServiceHours) {
        if (StringUtils.isBlank(nonServiceHours.getStart()) || StringUtils.isBlank(nonServiceHours.getEnd())) {
            return true;
        }

        LocalTime current = LocalTime.ofInstant(currentTime.toInstant(), ZoneId.of("Asia/Bangkok"));
        LocalTime startNonServiceHours = LocalTime.parse(nonServiceHours.getStart(), FORMATTER_HH_MM);
        LocalTime endNonServiceHours = LocalTime.parse(nonServiceHours.getEnd(), FORMATTER_HH_MM);

        boolean result = isOutsideTimeRange(current, startNonServiceHours, endNonServiceHours);

        logger.info("isDuringServiceHour : {}. [ current-time = {}, startNonServiceHours = {}, endNonServiceHours = {}]", result, current, startNonServiceHours, endNonServiceHours);
        return result;
    }


    private static boolean isOutsideTimeRange(LocalTime current, LocalTime start, LocalTime end) {
        boolean isSameDay = start.isBefore(end);
        if (isSameDay) {
            // Time range is within the same day
            return current.isBefore(start) || current.isAfter(end);
        } else {
            // Time range spans overnight
            return current.isBefore(start) && current.isAfter(end);
        }
    }
}
