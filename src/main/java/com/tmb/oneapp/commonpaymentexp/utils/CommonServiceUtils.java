package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import org.springframework.http.HttpStatus;

import java.time.ZonedDateTime;
import java.util.Date;


public class CommonServiceUtils {
    private static final TMBLogger<CommonServiceUtils> logger = new TMBLogger<>(CommonServiceUtils.class);
    private static final String LOGGING_INFO_GET_TMB_COMMON_THROW_EXCEPTION = "Throw common exception: ({}) reason: {}";

    private CommonServiceUtils() {
    }

    public static Date convertStringToDate(String dt) {
        return Date.from(ZonedDateTime.parse(dt).toInstant());
    }

    public static String getStringFromObject(Object input) {
        try {
            return input.toString();
        } catch (Exception ex) {
            return null;
        }
    }

    public static Status getStatusSuccess() {
        return new Status(ResponseCode.SUCCESS_V2.getCode(), ResponseCode.SUCCESS_V2.getMessage(), ResponseCode.SUCCESS_V2.getService(), null);
    }

    public static TMBCommonException getBadRequestTMBCommonException(ResponseCode responseCode) {
        logger.error(LOGGING_INFO_GET_TMB_COMMON_THROW_EXCEPTION, responseCode.getCode(), responseCode.getMessage());
        return new TMBCommonException(responseCode.getCode(), responseCode.getMessage(), responseCode.getService(),
                HttpStatus.BAD_REQUEST, null);
    }

    public static TMBCommonException getBadRequestTMBCommonException(ResponseCode responseCode, String errorMessage) {
        logger.error(LOGGING_INFO_GET_TMB_COMMON_THROW_EXCEPTION, responseCode.getCode(), errorMessage);
        return new TMBCommonException(responseCode.getCode(), errorMessage, responseCode.getService(),
                HttpStatus.BAD_REQUEST, null);
    }

    public static TMBCommonException getBusinessTmbCommonException(ResponseCode responseCode) {
        logger.error(LOGGING_INFO_GET_TMB_COMMON_THROW_EXCEPTION, responseCode.getCode(), responseCode.getMessage());
        return new TMBCommonException(responseCode.getCode(), responseCode.getMessage(), responseCode.getService(),
                HttpStatus.OK, null);
    }

    public static TMBCommonException getBusinessTmbCommonException(String errorCode, String message) {
        logger.error(LOGGING_INFO_GET_TMB_COMMON_THROW_EXCEPTION, errorCode, message);
        return new TMBCommonException(errorCode, message, ResponseCode.FAILED.getService(),
                HttpStatus.OK, null);
    }

    public static TMBCommonException getUnhandledTmbCommonException(ResponseCode responseCode) {
        logger.error(LOGGING_INFO_GET_TMB_COMMON_THROW_EXCEPTION, responseCode.getCode(), responseCode.getMessage());
        return new TMBCommonException(responseCode.getCode(), responseCode.getMessage(), responseCode.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR, null);
    }

    public static TMBCommonException getUnhandledTmbCommonException(ResponseCode responseCode, String message) {
        logger.error(LOGGING_INFO_GET_TMB_COMMON_THROW_EXCEPTION, responseCode.getCode(), message);
        return new TMBCommonException(responseCode.getCode(), message, responseCode.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR, null);
    }
}
