package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.logger.TMBLogger;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.BANK_TMB_VALIDATE_DATEFORMAT;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DateUtils {
    private static final TMBLogger<DateUtils> logger = new TMBLogger<>(DateUtils.class);

    private static final SimpleDateFormat formatDateTimeISO = new SimpleDateFormat(BANK_TMB_VALIDATE_DATEFORMAT);
    /**
     * Formats a timestamp (in milliseconds) to a string representation of a date based on ISO date format.
     *
     * @param timestamp  the timestamp in milliseconds as a string
     * @return ISO formatted date string
     * @throws IllegalArgumentException if the timestamp is invalid
     */
    public static String formatTimestampToISO(String timestamp) {
        if (timestamp == null) {
            return null;
        }

        try {
            long timeInMillis = Long.parseLong(timestamp);
            return formatDateTimeISO.format(new Date(timeInMillis));
        } catch (NumberFormatException e) {
            logger.error("Error in formatTimestampToISO. [timestamp = {}]", timestamp, e);
            throw new IllegalArgumentException("Invalid timestamp: " + timestamp, e);
        }
    }
}
