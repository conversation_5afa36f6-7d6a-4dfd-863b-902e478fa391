package com.tmb.oneapp.commonpaymentexp.utils;

import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

@UtilityClass
public class NumberUtils {
    public static final DecimalFormat COMMAS_FORMAT = new DecimalFormat("#,##0.00");

    public static String insertCommas(String number) {
        if (number == null) {
            return null;
        }
        COMMAS_FORMAT.setRoundingMode(RoundingMode.DOWN);
        return COMMAS_FORMAT.format(new BigDecimal(number));
    }

    public static String insertCommas(BigDecimal number) {
        if (number == null) {
            return null;
        }
        COMMAS_FORMAT.setRoundingMode(RoundingMode.DOWN);
        return COMMAS_FORMAT.format(number);
    }
}
