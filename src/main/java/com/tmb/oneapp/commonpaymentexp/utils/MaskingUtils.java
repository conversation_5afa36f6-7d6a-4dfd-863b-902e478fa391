package com.tmb.oneapp.commonpaymentexp.utils;

import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

@UtilityClass
public class MaskingUtils {

    private static final Pattern CITIZEN_ID_PATTERN = Pattern.compile("(.{9})(.)(.{2})(.)$");
    private static final String CITIZEN_ID_REPLACEMENT = "X-XXXX-XXXX$2-$3-$4";
    private static final Pattern CREDIT_CARD_PATTERN = Pattern.compile("(.{4})(.{2})(.{6})(.{4})");
    private static final String CREDIT_CARD_REPLACEMENT = "$1-$2XX-XXXX-$4";

    /**
     * Masks the input string based on the provided pattern and replacement format.
     *
     * @param input The input string to be masked.
     * @param pattern The regex pattern to match parts of the input that need masking.
     * @param replacementFormat The replacement format to apply to the matched parts.
     * @return The masked input string. If the input is null or empty, it is returned unchanged.
     */
    public static String mask(String input, Pattern pattern, String replacementFormat) {
        if (input == null || input.isEmpty()) {
            return input;
        }
        return pattern.matcher(input).replaceAll(replacementFormat);
    }

    /**
     * Masks a Thai citizen ID into a format like X-XXXX-XXXX2-33-4
     * Example: 195990600096440 => X-XXXX-XXXX5-96-0
     *
     * @param citizenId The full 13-digit citizen ID.
     * @return The masked citizen ID.
     */
    public static String maskCitizenId(String citizenId) {
        if (citizenId == null || citizenId.length() != 13) {
            return citizenId;
        }

        return CITIZEN_ID_PATTERN.matcher(citizenId).replaceAll(CITIZEN_ID_REPLACEMENT);
    }

    /**
     * Masks a credit card number into a format like 1234-56XX-XXXX-3456
     * Example: 1234567890123456 => 1234-56XX-XXXX-3456
     *
     * @param creditCardNumber The full 16-digit credit card number.
     * @return The masked credit card number. If the input is null or empty, it is returned unchanged.
     */
    public static String maskCreditCard(String creditCardNumber) {
        if (creditCardNumber == null || creditCardNumber.length() != 16) {
            return creditCardNumber;
        }

        return CREDIT_CARD_PATTERN.matcher(creditCardNumber).replaceAll(CREDIT_CARD_REPLACEMENT);
    }

    /**
     * Masks a string with its last 4 characters visible.
     * Example: 1234567890123456 => XX3456
     *
     * @param data The string to be masked. If the input is null or empty, it is returned unchanged.
     * @return The masked string.
     */
    public static String maskWithLastFour(String data) {
        if (data == null || StringUtils.length(data) <= 3) {
            return data;
        }

        String lastFour = data.substring(data.length() - 4);
        return "XX" + lastFour;
    }
}
