package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableSupplier;
import feign.FeignException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.function.Supplier;


@Component
public class FeignClientHelper {
    private static final TMBLogger<FeignClientHelper> logger = new TMBLogger<>(FeignClientHelper.class);
    private static final String LOG_EXCEPTION_ERROR_MESSAGE_PATTERN = "Exception encountered while fetching data in method {}.";
    private static final String LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN = "FeignException encountered while fetching data in method {}.";
    private static final Predicate<TmbServiceResponse<?>> IS_SUCCESS_CODE = body -> ResponseCode.SUCCESS_V2.getCode().equals(body.getStatus().getCode()) || ResponseCode.SUCCESS.getCode().equals(body.getStatus().getCode());
    private static final Predicate<ResponseEntity<?>> IS_HTTP_2xx_SUCCESSFUL = r -> r.getStatusCode().is2xxSuccessful();


    public <T> T executeRequestSafely(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) {
        try {
            return executeRequest(supplier);
        } catch (Exception e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error("Method name {} : execute request without throw then return null. [result = null]", callerMethodName);
            return null;
        }
    }

    public <T> T executeRequestOrElse(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier, T elseAction) {
        try {
            return executeRequest(supplier);

        } catch (Exception e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error("Method name {} : execute request without throw then return else value. [result = {}]", callerMethodName, elseAction.toString());
            return elseAction;
        }
    }

    public <T, X extends TMBCommonException> T executeRequestOrElseThrow(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier, Supplier<? extends X> exceptionSupplier) throws X {
        try {
            return executeRequest(supplier);

        } catch (Exception e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            X customException = exceptionSupplier.get();
            logger.error("Method name {} : execute request throw custom TMBCommonException. [error-code = {}]", callerMethodName, customException.getErrorCode());
            throw customException;
        }
    }

    public <T, X extends TMBCommonException> T executeRequestNullableOrElseThrow(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier, Supplier<? extends X> exceptionSupplier) throws X {
        try {
            return executeRequestNullable(supplier);

        } catch (Exception e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            X customException = exceptionSupplier.get();
            logger.error("Method name {} : execute request throw custom TMBCommonException. [error-code = {}]", callerMethodName, customException.getErrorCode());
            throw customException;
        }
    }

    public <T> T executeRequest(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) throws TMBCommonException {
        ThrowableSupplier<T> executeRequestSupplier = executeRequestSupplier(supplier);

        return baseExecuteRequest(executeRequestSupplier);
    }

    public <T> T executeRequestNullable(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) throws TMBCommonException {
        ThrowableSupplier<T> executeRequestNullableSupplier = executeRequestNullableSupplier(supplier);

        return baseExecuteRequest(executeRequestNullableSupplier);
    }

    public <T> T executeRequestCustom(ThrowableSupplier<T> supplier) throws TMBCommonException {

        return baseExecuteRequest(supplier);
    }

    private static <T> ThrowableSupplier<T> executeRequestNullableSupplier(Supplier<ResponseEntity<TmbServiceResponse<T>>> originalSupplier) {
        return () -> {
            ResponseEntity<TmbServiceResponse<T>> responseEntity = originalSupplier.get();
            if (!responseEntity.getStatusCode().is2xxSuccessful()) {
                throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Non-successful HTTP status code received");
            }

            TmbServiceResponse<T> responseEntityBody = Objects.requireNonNull(responseEntity.getBody());

            boolean isNotSuccessCode = !IS_SUCCESS_CODE.test(responseEntityBody);
            if (isNotSuccessCode) {
                throw throwErrorDependStatusCodeOrDefault(responseEntity.getBody()).get();
            }

            return responseEntityBody.getData();
        };
    }

    private static <T> ThrowableSupplier<T> executeRequestSupplier(Supplier<ResponseEntity<TmbServiceResponse<T>>> originalSupplier) {
        return () -> {
            ResponseEntity<TmbServiceResponse<T>> responseEntity = originalSupplier.get();
            return Optional.ofNullable(responseEntity)
                    .filter(IS_HTTP_2xx_SUCCESSFUL)
                    .map(HttpEntity::getBody)
                    .filter(IS_SUCCESS_CODE)
                    .map(TmbServiceResponse::getData)
                    .orElseThrow(() -> throwErrorDependStatusCodeOrDefault(responseEntity).get());
        };
    }

    private static <T> T baseExecuteRequest(ThrowableSupplier<T> customSupplier) throws TMBCommonException {
        try {
            return customSupplier.get();
        } catch (TMBCommonException e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error(LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN, callerMethodName, e);
            throw e;
        } catch (FeignException e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error(LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN, callerMethodName, e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "FeignException occurred while fetching data");
        } catch (Exception e) {
            String callerMethodName = getCallerMethodName(new Throwable().getStackTrace());
            logger.error(LOG_EXCEPTION_ERROR_MESSAGE_PATTERN, callerMethodName, e);
            throw CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Exception occurred while fetching data");
        }
    }

    private static <T> Supplier<TMBCommonException> throwErrorDependStatusCodeOrDefault(TmbServiceResponse<T> body) {
        return () -> {
            Status status = Optional.ofNullable(body).map(TmbServiceResponse::getStatus).orElse(getDefaultFailedStatus());
            return CommonServiceUtils.getBusinessTmbCommonException(status.getCode(), status.getMessage());
        };
    }

    private static <T> Supplier<TMBCommonException> throwErrorDependStatusCodeOrDefault(ResponseEntity<TmbServiceResponse<T>> responseEntity) {
        return () -> {
            final boolean isNot2xxSuccessful = !responseEntity.getStatusCode().is2xxSuccessful();
            if (isNot2xxSuccessful) {
                return CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Non-successful HTTP status code received");
            }

            boolean isDataNull = Optional.of(responseEntity).map(ResponseEntity::getBody).map(TmbServiceResponse::getData).isEmpty();
            if (isDataNull) {
                return CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.FAILED_V2.getCode(), "Got null data from feign client");
            }

            final Status status = Optional.ofNullable(responseEntity.getBody()).map(TmbServiceResponse::getStatus).orElse(getDefaultFailedStatus());
            return CommonServiceUtils.getBusinessTmbCommonException(status.getCode(), status.getMessage());
        };
    }

    private static Status getDefaultFailedStatus() {
        Status defaultFailedStatus = new Status();
        defaultFailedStatus.setCode(ResponseCode.FAILED_V2.getCode());
        defaultFailedStatus.setMessage("Got null data or status from feign client");
        return defaultFailedStatus;
    }

    private static String getCallerMethodName(StackTraceElement[] stackTrace) {
        if (stackTrace == null || stackTrace.length < 5) {
            return StringUtils.EMPTY;
        }

        try {
            return StringUtils.joinWith(".", stackTrace[4].getMethodName(), stackTrace[3].getMethodName(), stackTrace[2].getMethodName());
        } catch (Exception e) {
            return StringUtils.EMPTY;
        }
    }
}
