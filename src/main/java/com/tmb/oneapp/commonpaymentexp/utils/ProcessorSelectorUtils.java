package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_TOP_UP;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_METHOD_TMB_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_METHOD_TMB_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_PAYMENT_METHOD_OFFLINE;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_E_WALLET;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_MEA;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_MWA;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_PEA;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_TTB_FLEET_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.BLANK;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.PAYMENT_METHOD_TMB_PRODUCT;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_BILL_PROMPT_PAY;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_FLEET_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_MEA;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_MWA;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_OFFLINE;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_ONLINE_BILLER;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_PEA;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_AUTO_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TOPUP;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TOPUP_E_WALLET;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNull;

@UtilityClass
public class ProcessorSelectorUtils {
    private static final TMBLogger<ProcessorSelectorUtils> logger = new TMBLogger<>(ProcessorSelectorUtils.class);

    @LogAround
    public String getBillProcessorTypeOverride(String compCode, MasterBillerResponse masterBiller) throws TMBCommonException {
        String result = Optional.of(getBillProcessorType(compCode, masterBiller)).orElse(BLANK);

        return isShouldReturnOnlineOfflineTopUp(result)
                ? BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP
                : result;
    }

    @LogAround
    public String getBillProcessorType(String compCode, MasterBillerResponse masterBiller) throws TMBCommonException {
        validateRequireNonNull(compCode, "compCode shouldn't be null, Please verify compCode.");
        validateRequireNonNull(masterBiller, "masterBiller shouldn't be null, Please verify masterBiller.");

        Optional<String> result;

        if (isBillPay(masterBiller)) {
            result = Optional.of(mapPaymentTypeWithBill(compCode, masterBiller));
        } else if (isTopUp(masterBiller)) {
            result = Optional.of(mapPaymentTypeWithTopUp(compCode));
        } else {
            logger.error("Biller group type not match, allow only Bill and TopUp. Please verify biller group type. [ compCode = {}]", compCode);
            result = Optional.empty();
        }

        return result.orElseThrow(() -> {
            logger.error("Processor type is null, allow only Bill and TopUp. Please verify biller group type. [ compCode = {}]", compCode);
            return CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.NOT_MATCH_PAYMENT_TYPE_ERROR);
        });
    }

    public static boolean isTransactionTypeBillPay(String transactionType) {
        return COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY.equalsIgnoreCase(transactionType);
    }

    private boolean isShouldReturnOnlineOfflineTopUp(String result) {
        return StringUtils.equalsAnyIgnoreCase(result, BILLER_PAYMENT_ONLINE_BILLER, BILLER_PAYMENT_OFFLINE, BILLER_PAYMENT_TOPUP);
    }

    private <T> void validateRequireNonNull(T value, String errorMessage) throws TMBCommonException {
        try {
            Objects.requireNonNull(value, errorMessage);
        } catch (NullPointerException e) {
            logger.error(errorMessage, e);
            throw CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.FAILED_V2, errorMessage);
        }
    }

    private String mapPaymentTypeWithTopUp(String compCode) {
        if (StringUtils.equals(BILL_COMP_CODE_E_WALLET, compCode)) {
            return BILLER_PAYMENT_TOPUP_E_WALLET;
        }
        return BILLER_PAYMENT_TOPUP;
    }

    private boolean isTopUp(MasterBillerResponse masterBiller) {
        return StringUtils.equalsAnyIgnoreCase(BILLER_GROUP_TYPE_TOP_UP, getSafeNull(() -> masterBiller.getBillerInfo().getBillerGroupType()));
    }

    private boolean isBillPay(MasterBillerResponse masterBiller) {
        return StringUtils.equalsAnyIgnoreCase(BILLER_GROUP_TYPE_BILL, getSafeNull(() -> masterBiller.getBillerInfo().getBillerGroupType()));
    }

    private String mapPaymentTypeWithBill(String compCode, MasterBillerResponse masterBiller) {
        return Optional.ofNullable(mapPaymentTypeWithCompCode(compCode))
                .orElse(mapPaymentTypeWithPaymentMethodAndBillerMethod(masterBiller));
    }

    private String mapPaymentTypeWithCompCode(String compCode) {
        if (compCode.length() == 15) {
            return BILLER_PAYMENT_BILL_PROMPT_PAY;
        }

        return switch (compCode) {
            case BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN -> BILLER_PAYMENT_TMB_AUTO_LOAN;
            case BILL_COMP_CODE_TTB_FLEET_CARD -> BILLER_PAYMENT_FLEET_CARD;
            case (BILL_COMP_CODE_MEA) -> BILLER_PAYMENT_MEA;
            case (BILL_COMP_CODE_MWA) -> BILLER_PAYMENT_MWA;
            case (BILL_COMP_CODE_PEA) -> BILLER_PAYMENT_PEA;

            default -> null;
        };
    }

    private String mapPaymentTypeWithPaymentMethodAndBillerMethod(MasterBillerResponse masterBiller) {
        final String paymentMethod = getSafeNull(() -> masterBiller.getBillerInfo().getPaymentMethod());
        final String billerMethod = getSafeNull(() -> masterBiller.getBillerInfo().getBillerMethod());

        if (StringUtils.equals(paymentMethod, BILLER_PAYMENT_METHOD_OFFLINE)) {
            return BILLER_PAYMENT_OFFLINE;
        } else if (StringUtils.equals(paymentMethod, PAYMENT_METHOD_TMB_PRODUCT)
                && StringUtils.equals(billerMethod, BILLER_METHOD_TMB_CREDIT_CARD)) {
            return BILLER_PAYMENT_CREDIT_CARD;
        } else if (StringUtils.equals(billerMethod, BILLER_METHOD_TMB_LOAN)) {
            return BILLER_PAYMENT_TMB_LOAN;
        } else {
            return BILLER_PAYMENT_ONLINE_BILLER;
        }
    }
}
