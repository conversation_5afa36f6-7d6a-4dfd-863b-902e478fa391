package com.tmb.oneapp.commonpaymentexp.utils;

import com.google.zxing.WriterException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.util.MiniQR;
import lombok.experimental.UtilityClass;

import java.io.IOException;

@UtilityClass
public class QRUtils {
    private static final TMBLogger<QRUtils> logger = new TMBLogger<>(QRUtils.class);

    /**
     * This method will generate miniQR picture
     *
     * @param refNo transaction reference for generate mini QR
     * @return String mini qr with Base64
     */
    public String generateMiniQRSafely(String refNo) {
        try {
            return new MiniQR(refNo).drawToBase64(370, 370);
        } catch (IOException | WriterException | NullPointerException eIgnore) {
            logger.error("Error generateMiniQR. Please verify RefNo. [refNo = {}]", refNo, eIgnore);
            return null;
        }
    }
}
