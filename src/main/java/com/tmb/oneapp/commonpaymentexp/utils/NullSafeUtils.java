package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.oneapp.commonpaymentexp.exception.NullPointerCustomException;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpStatus;

import java.util.Optional;
import java.util.function.Supplier;

@UtilityClass
public class NullSafeUtils {

    /**
     * Safely returns the value from the supplier or a default value if the supplier throws an exception or returns null.
     */
    public static <T> T getSafeNullOrDefault(Supplier<T> valueSupplier, T defaultValue) {
        try {
            return Optional.ofNullable(valueSupplier.get()).orElse(defaultValue);
        } catch (NullPointerException e) {
            return defaultValue;
        }
    }

    /**
     * Safely returns the value from the supplier or null value if the supplier throws an exception or returns null.
     */
    public static <T> T getSafeNull(Supplier<T> valueSupplier) {
        try {
            return valueSupplier.get();
        } catch (NullPointerException e) {
            return null;
        }
    }

    public static <T> T requireNonNull(Supplier<T> valueSupplier) {
        try {
            return Optional.ofNullable(valueSupplier.get())
                    .orElseThrow(NullPointerCustomException::new);
        } catch (NullPointerException e) {
            throw new NullPointerCustomException();
        }
    }

    public static <T> T requireNonNull(Supplier<T> valueSupplier, String errorMessage) {
        try {
            return Optional.ofNullable(valueSupplier.get())
                    .orElseThrow(() -> new NullPointerCustomException(errorMessage));
        } catch (NullPointerException e) {
            throw new NullPointerCustomException(errorMessage);
        }
    }

    public static <T> T requireNonNull(Supplier<T> valueSupplier, String errorMessage, HttpStatus httpStatus) {
        try {
            return Optional.ofNullable(valueSupplier.get())
                    .orElseThrow(() -> new NullPointerCustomException(errorMessage, httpStatus));
        } catch (NullPointerException e) {
            throw new NullPointerCustomException(errorMessage, httpStatus);
        }
    }
}
