package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import feign.FeignException;
import org.springframework.http.ResponseEntity;

import java.util.function.Supplier;

import static com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils.getBusinessTmbCommonException;
import static com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils.getUnhandledTmbCommonException;


public class FeignClientUtils {
    private static final TMBLogger<FeignClientUtils> logger = new TMBLogger<>(FeignClientUtils.class);
    private static final String LOG_EXCEPTION_ERROR_MESSAGE_PATTERN = "Exception encountered while fetching data from {} in method {}.";
    private static final String LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN = "FeignException encountered while fetching data from {} in method {}.";

    private FeignClientUtils() {
    }

    public static <T> T executeRequestSafely(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) {
        try {
            return executeRequest(supplier);
        } catch (Exception e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            logger.error("Method name {} : execute request without throw then return null. [result = null]", callerMethodName);
            return null;
        }
    }

    public static <T> T executeRequestOrElse(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier, T elseAction) {
        try {
            return executeRequest(supplier);

        } catch (Exception e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            logger.error("Method name {} : execute request without throw then return else value. [result = {}]", callerMethodName, elseAction.toString());
            return elseAction;
        }
    }

    public static <T, X extends TMBCommonException> T executeRequestOrElseThrow(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier, Supplier<? extends X> exceptionSupplier) throws X {
        try {
            return executeRequest(supplier);

        } catch (Exception e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            X customException = exceptionSupplier.get();
            logger.error("Method name {} : execute request throw custom TMBCommonException. [error-code = {}]", callerMethodName, customException.getErrorCode());
            throw customException;
        }
    }

    public static <T> T executeRequest(Supplier<ResponseEntity<TmbServiceResponse<T>>> supplier) throws TMBCommonException {
        try {
            ResponseEntity<TmbServiceResponse<T>> tmbServiceResponseResponseEntity = supplier.get();

            if (!tmbServiceResponseResponseEntity.getStatusCode().is2xxSuccessful()) {
                throw getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Non-successful HTTP status code received");
            }

            TmbServiceResponse<T> responseEntityBody = tmbServiceResponseResponseEntity.getBody();

            if (responseEntityBody == null
                    || !(ResponseCode.SUCCESS.getCode().equals(responseEntityBody.getStatus().getCode())
                    || ResponseCode.SUCCESS_V2.getCode().equals(responseEntityBody.getStatus().getCode()))) {
                throw getBusinessTmbCommonException(responseEntityBody.getStatus().getCode(), responseEntityBody.getStatus().getMessage());
            }

            T data = responseEntityBody.getData();

            if (data == null) {
                throw getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Null data received");
            }

            return data;
        } catch (TMBCommonException e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            logger.error(LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), callerMethodName, e);
            throw e;
        } catch (FeignException e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            logger.error(LOG_FEIGN_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), callerMethodName, e);
            throw getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "FeignException occurred while fetching data");
        } catch (Exception e) {
            String callerMethodName = new Throwable().getStackTrace()[1].getMethodName();
            logger.error(LOG_EXCEPTION_ERROR_MESSAGE_PATTERN, supplier.toString(), callerMethodName, e);
            throw getUnhandledTmbCommonException(ResponseCode.FAILED_V2, "Exception occurred while fetching data");
        }
    }
}
