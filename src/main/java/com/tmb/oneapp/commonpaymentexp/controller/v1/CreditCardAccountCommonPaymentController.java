package com.tmb.oneapp.commonpaymentexp.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.commonpaymentexp.logger.LogExecutionTime;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.CreditCardPoint;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.service.AccountCreditCardService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;

@RestController
@RequestMapping("/common-payment")
@Tag(name = "Common payment", description = "Common payment controller")
@RequiredArgsConstructor
public class CreditCardAccountCommonPaymentController {
    private static final TMBLogger<CreditCardAccountCommonPaymentController> logger = new TMBLogger<>(CreditCardAccountCommonPaymentController.class);
    private final AccountCreditCardService accountCreditCardService;

    @LogAround
    @LogExecutionTime
    @Operation(summary = "Get credit-card for common-payment")
    @GetMapping(value = "/credit-cards", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<List<CreditCardSupplementary>>> getCreditCardList(
            @Parameter(name = HEADER_CORRELATION_ID, example = "c28f91e4-881e-4387-a597-4a39c2822b3c", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = HEADER_CRM_ID, example = "001100000000000000000001184383", required = true) @RequestHeader(HEADER_CRM_ID) @Valid @RequestHeaderNonNull String crmId,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException {

        List<CreditCardSupplementary> data = accountCreditCardService.getCreditCardAccounts(correlationId, crmId);
        TmbServiceResponse<List<CreditCardSupplementary>> response = new TmbServiceResponse<>();
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(data);

        return ResponseEntity.ok(response);
    }

    @LogAround
    @LogExecutionTime
    @Operation(summary = "Get card-point for common-payment")
    @GetMapping(value = "/credit-cards/cards-point", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<List<CreditCardPoint>>> getCardsPointList(
            @Parameter(name = HEADER_CORRELATION_ID, example = "c28f91e4-881e-4387-a597-4a39c2822b3c", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = HEADER_CRM_ID, example = "001100000000000000000001184383", required = true) @RequestHeader(HEADER_CRM_ID) @Valid @RequestHeaderNonNull String crmId,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException {

        List<CreditCardPoint> data = accountCreditCardService.getCardPointCreditCard(correlationId, crmId);
        TmbServiceResponse<List<CreditCardPoint>> response = new TmbServiceResponse<>();
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(data);

        return ResponseEntity.ok(response);
    }
}
