package com.tmb.oneapp.commonpaymentexp.controller.v1.security;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.logger.LogExecutionTime;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.EncryptedPayloadRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.PublicKeyResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.SignedPayloadResponse;
import com.tmb.oneapp.commonpaymentexp.service.CommonPaymentPartnerIntegrationService;
import com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment.InitializationCommonPaymentPartnerService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/common-payment")
@RequiredArgsConstructor
@Tag(name = "Partner Integration API", description = "APIs for external partner integration")
public class CommonPaymentPartnerIntegrationController {
    private final CommonPaymentPartnerIntegrationService commonPaymentPartnerIntegrationService;
    private final InitializationCommonPaymentPartnerService initializationCommonPaymentPartnerService;

    @LogAround
    @LogExecutionTime
    @Operation(summary = "Get public key set for a partner")
    @GetMapping("/public-key")
    public ResponseEntity<TmbServiceResponse<PublicKeyResponse>> getPublicKey(
            @RequestHeader(CommonPaymentExpConstant.HEADER_X_PARTNER_NAME) String partnerName) throws TMBCommonException {
        TmbServiceResponse<PublicKeyResponse> response = new TmbServiceResponse<>();
        PublicKeyResponse publicKeyResponse = commonPaymentPartnerIntegrationService.getPublicKey(partnerName);
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(publicKeyResponse);
        return ResponseEntity.ok(response);
    }

    @LogAround
    @LogExecutionTime
    @Operation(summary = "Initialize a payment with an encrypted payload")
    @PostMapping("/initial")
    public ResponseEntity<SignedPayloadResponse> initialEncryptedPayment(
            @RequestHeader(CommonPaymentExpConstant.HEADER_X_PARTNER_NAME) String partnerName,
            @RequestBody @Valid EncryptedPayloadRequest encryptedRequest,
            @RequestHeader HttpHeaders headers) throws TMBCommonException {

        TmbServiceResponse<InitializationCommonPaymentResponse> serviceResponse = new TmbServiceResponse<>();
        try {
            InitializationCommonPaymentRequest request = commonPaymentPartnerIntegrationService.decryptInitialPayload(encryptedRequest.getPayload(), partnerName);
            InitializationCommonPaymentResponse data = initializationCommonPaymentPartnerService.initialCommonPayment(request, headers);

            serviceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
            serviceResponse.setData(data);
        } catch (TMBCommonException e) {
            serviceResponse.setStatus(new Status(ResponseCode.FAILED_V2.getCode(), e.getMessage(), ResponseCode.FAILED_V2.getService(), null));
        }

        String signedPayload;
        try {
            signedPayload = commonPaymentPartnerIntegrationService.signServiceResponse(partnerName, serviceResponse);
        } catch (TMBCommonException e) {
            signedPayload = "{\"error\":\"Failed to sign response data\"}";
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new SignedPayloadResponse(signedPayload));
        }
        return ResponseEntity.ok(new SignedPayloadResponse(signedPayload));
    }
}