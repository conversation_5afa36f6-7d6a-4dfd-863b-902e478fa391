package com.tmb.oneapp.commonpaymentexp.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.logger.LogAround;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.requestheaderhandling.RequestHeaderNonNull;
import com.tmb.oneapp.commonpaymentexp.logger.LogExecutionTime;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentProcessor;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentProcessorSelector;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;

@RestController
@RequestMapping("/common-payment")
@Tag(name = "Common payment", description = "Common payment controller")
@RequiredArgsConstructor
public class AccountCommonPaymentController {
    private final AccountCommonPaymentProcessorSelector accountCommonPaymentProcessorSelector;

    @LogAround
    @LogExecutionTime
    @Operation(summary = "Get account for common-payment")
    @GetMapping(value = "/accounts", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<TmbServiceResponse<List<DepositAccount>>> getAccountList(
            @Parameter(name = HEADER_CORRELATION_ID, example = "c28f91e4-881e-4387-a597-4a39c2822b3c", required = true) @Valid @RequestHeader(HEADER_CORRELATION_ID) @RequestHeaderNonNull String correlationId,
            @Parameter(name = HEADER_CRM_ID, example = "001100000000000000000001184383", required = true) @RequestHeader(HEADER_CRM_ID) @Valid @RequestHeaderNonNull String crmId,
            @RequestParam(value = "filter_account_type") String filterAccountType,
            @RequestHeader HttpHeaders headers
    ) throws TMBCommonException {

        accountCommonPaymentProcessorSelector.validateFilterAccountTypeKey(filterAccountType);
        AccountCommonPaymentProcessor<List<DepositAccount>> processor = accountCommonPaymentProcessorSelector.getProcessor(filterAccountType);

        List<DepositAccount> data = processor.getAccountList(correlationId, crmId);
        TmbServiceResponse<List<DepositAccount>> response = new TmbServiceResponse<>();
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(data);

        return ResponseEntity.ok(response);
    }
}
