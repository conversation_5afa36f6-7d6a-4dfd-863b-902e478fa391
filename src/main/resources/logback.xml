<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <property name="LOGS" value="./logs"/>
    <property resource="bootstrap.properties"/>
    <appender name="CONSOLE_APPENDER" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.tmb.common.filter.MaskingPatternLayout">
                <maskPattern>\"authorization\"\s*:\s*\"(.*?)\"</maskPattern>
                <maskPattern>\"username\"\s*:\s*\"(.*?)\"</maskPattern>
                <maskPattern>\"password\"\s*:\s*\"(.*?)\"</maskPattern>
                <maskPattern>\"pin\"\s*:\s*\"(.*?)\"</maskPattern>
                <maskPattern>(\w+@\w+\.\w+)</maskPattern> <!-- Email pattern -->
                <pattern>%d{yyyy-MM-dd}T%d{HH:mm:ss}+07:00 ${spring.application.name} %replace(%t){'\s', ''} %level
                    %logger{36} [%X{correlationId}] - %m%n
                </pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="FILE_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="com.tmb.common.filter.MaskingPatternLayout">
                <maskPattern>\"authorization\"\s*:\s*\"(.*?)\"</maskPattern>
                <maskPattern>\"username\"\s*:\s*\"(.*?)\"</maskPattern>
                <maskPattern>\"password\"\s*:\s*\"(.*?)\"</maskPattern>
                <maskPattern>(\w+@\w+\.\w+)</maskPattern> <!-- Email pattern -->
                <pattern>%d{yyyy-MM-dd}T%d{HH:mm:ss}+07:00 ${spring.application.name} %replace(%t){'\s', ''} %level
                    %logger{36} [%X{correlationId}] - %m%n
                </pattern>
            </layout>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOGS}/%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
    </appender>

    <root level="info">
        <appender-ref ref="CONSOLE_APPENDER"/>
        <appender-ref ref="FILE_APPENDER"/>
    </root>

</configuration>
