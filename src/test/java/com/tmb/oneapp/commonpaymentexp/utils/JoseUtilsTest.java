package com.tmb.oneapp.commonpaymentexp.utils;

import com.nimbusds.jose.EncryptionMethod;
import com.nimbusds.jose.JWEAlgorithm;
import com.nimbusds.jose.JWEHeader;
import com.nimbusds.jose.JWEObject;
import com.nimbusds.jose.Payload;
import com.nimbusds.jose.crypto.RSAEncrypter;
import com.nimbusds.jose.jwk.Curve;
import com.nimbusds.jose.jwk.ECKey;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jose.jwk.gen.ECKeyGenerator;
import com.nimbusds.jose.jwk.gen.RSAKeyGenerator;
import com.tmb.common.exception.model.TMBCommonException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class JoseUtilsTest {

    private static String publicKeyStr;
    private static String privateKeyStr;
    private static String anotherPublicKeyStr;

    private static RSAKey rsaKey;
    private static RSAKey anotherRsaKey;
    private static JWKSet jwkSet;

    @BeforeAll
    static void setUp() throws Exception {
        KeyPair keyPair = generateRsaKeyPair();
        publicKeyStr = Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
        privateKeyStr = Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());
        anotherPublicKeyStr = Base64.getEncoder().encodeToString(generateRsaKeyPair().getPublic().getEncoded());

        rsaKey = new RSAKeyGenerator(2048).keyID("test-kid-1").generate();
        anotherRsaKey = new RSAKeyGenerator(2048).keyID("test-kid-2").generate();
        jwkSet = new JWKSet(rsaKey);
    }

    private static KeyPair generateRsaKeyPair() throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(2048);
        return keyPairGenerator.generateKeyPair();
    }

    @Nested
    class JwkBasedTests {
        @Test
        void testEncryptAndDecrypt_Success() throws TMBCommonException {
            String originalText = "This is a secret message for testing JWE!";
            RSAKey rsaPublicKey = rsaKey.toPublicJWK();
            String encryptedText = JoseUtils.encrypt(originalText, rsaPublicKey);
            String decryptedText = JoseUtils.decrypt(encryptedText, jwkSet);
            assertEquals(originalText, decryptedText);
        }

        @Test
        void testDecrypt_withWrongKey_shouldThrowException() throws TMBCommonException {
            String encryptedText = JoseUtils.encrypt("test", rsaKey.toPublicJWK());
            JWKSet wrongJwkSet = new JWKSet(anotherRsaKey);
            assertThrows(TMBCommonException.class, () -> JoseUtils.decrypt(encryptedText, wrongJwkSet));
        }

        @Test
        void testDecrypt_withNoKidInHeader_shouldThrowException() throws Exception {
            JWEHeader header = new JWEHeader(JWEAlgorithm.RSA_OAEP_256, EncryptionMethod.A256GCM);
            JWEObject jweObject = new JWEObject(header, new Payload("test"));
            jweObject.encrypt(new RSAEncrypter(rsaKey.toPublicJWK()));
            String jweString = jweObject.serialize();

            assertThrows(TMBCommonException.class, () -> JoseUtils.decrypt(jweString, jwkSet));
        }

        @Test
        void testDecrypt_withMalformedJwe_shouldThrowException() {
            String malformedJwe = "this.is.not.a.valid.jwe";
            assertThrows(TMBCommonException.class, () -> JoseUtils.decrypt(malformedJwe, jwkSet));
        }

        @Test
        void testDecrypt_withNonRsaKeyInSet_shouldThrowException() throws Exception {
            ECKey ecKey = new ECKeyGenerator(Curve.P_256)
                    .keyID(rsaKey.getKeyID())
                    .generate();
            JWKSet mixedJwkSet = new JWKSet(ecKey);

            String encryptedText = JoseUtils.encrypt("test", rsaKey.toPublicJWK());
            assertThrows(TMBCommonException.class, () -> JoseUtils.decrypt(encryptedText, mixedJwkSet));
        }

        @Test
        void testGenerateAndVerifyJws_Success() throws TMBCommonException {
            Map<String, Object> claims = Collections.singletonMap("sub", "1234567890");
            String jwsString = JoseUtils.generateJws(rsaKey, claims);
            Payload verifiedPayload = JoseUtils.verifyJws(jwsString, rsaKey.toPublicJWK());
            assertEquals("{\"sub\":\"1234567890\"}", verifiedPayload.toString());
        }

        @Test
        void testVerifyJws_withWrongKey_shouldThrowException() throws TMBCommonException {
            String jwsString = JoseUtils.generateJws(rsaKey, Collections.singletonMap("sub", "user-id"));
            assertThrows(TMBCommonException.class, () -> JoseUtils.verifyJws(jwsString, anotherRsaKey.toPublicJWK()));
        }

        @Test
        void testVerifyJws_withMalformedJws_shouldThrowException() {
            String malformedJws = "this.is.not.a.valid.jws";
            assertThrows(TMBCommonException.class, () -> JoseUtils.verifyJws(malformedJws, rsaKey.toPublicJWK()));
        }
    }

    @Nested
    class StringBasedTests {
        @Test
        void testEncryptAndDecrypt_Success() throws TMBCommonException {
            String originalText = "Legacy test message";
            String encryptedText = JoseUtils.encrypt(originalText, publicKeyStr);
            String decryptedText = JoseUtils.decrypt(encryptedText, privateKeyStr);
            assertEquals(originalText, decryptedText);
        }

        @Test
        void testGenerateAndVerifyJws_Success() throws TMBCommonException {
            Map<String, Object> claims = Collections.singletonMap("sub", "legacy-user");
            String jwsString = JoseUtils.generateJws(privateKeyStr, claims);
            Payload verifiedPayload = JoseUtils.verifyJws(jwsString, publicKeyStr);
            assertEquals("{\"sub\":\"legacy-user\"}", verifiedPayload.toString());
        }

        @Test
        void testVerifyJws_withWrongKey_shouldThrowException() throws TMBCommonException {
            String jwsString = JoseUtils.generateJws(privateKeyStr, Collections.singletonMap("sub", "user"));
            assertThrows(TMBCommonException.class, () -> JoseUtils.verifyJws(jwsString, anotherPublicKeyStr));
        }

        @Test
        void testMethods_withInvalidKeyFormat_shouldThrowException() {
            String invalidKey = "this-is-not-a-valid-base64-key";
            String validJwe = "some.valid.jwe";
            String validJws = "some.valid.jws";

            assertThrows(TMBCommonException.class, () -> JoseUtils.encrypt("test", invalidKey));
            assertThrows(TMBCommonException.class, () -> JoseUtils.decrypt(validJwe, invalidKey));
            assertThrows(TMBCommonException.class, () -> JoseUtils.generateJws(invalidKey, Collections.emptyMap()));
            assertThrows(TMBCommonException.class, () -> JoseUtils.verifyJws(validJws, invalidKey));
        }

        @Test
        void testMethods_withMalformedStrings_shouldThrowException() {
            String malformedString = "this.is.not.valid";
            assertThrows(TMBCommonException.class, () -> JoseUtils.decrypt(malformedString, privateKeyStr));
            assertThrows(TMBCommonException.class, () -> JoseUtils.verifyJws(malformedString, publicKeyStr));
        }
    }
}
