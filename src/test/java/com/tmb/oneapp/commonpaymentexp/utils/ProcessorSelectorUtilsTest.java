package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.util.stream.Stream;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_TOP_UP;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_METHOD_TMB_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_METHOD_TMB_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_PAYMENT_METHOD_OFFLINE;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_E_WALLET;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_MEA;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_MWA;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_PEA;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_TTB_FLEET_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.PAYMENT_METHOD_TMB_PRODUCT;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_BILL_PROMPT_PAY;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_FLEET_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_MEA;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_MWA;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_OFFLINE;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_ONLINE_BILLER;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_PEA;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_AUTO_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TMB_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TOPUP;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TOPUP_E_WALLET;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialActivityLog.BILLER_GROUP_BILL_PAY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class ProcessorSelectorUtilsTest {

    String compCode;
    MasterBillerResponse masterBiller;

    @BeforeEach
    void setUp() {
        compCode = "comp-code";
        masterBiller = buildDefaultMasterBiller();
    }

    public static Stream<Arguments> dateTestGetBillProcessorType() {
        return Stream.of(
                Arguments.of("ignore", BILLER_GROUP_TYPE_TOP_UP, "ignore", "ignore", BILLER_PAYMENT_TOPUP),
                Arguments.of(BILL_COMP_CODE_E_WALLET, BILLER_GROUP_TYPE_TOP_UP, "ignore", "ignore", BILLER_PAYMENT_TOPUP_E_WALLET),
                Arguments.of("ignore", BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_ONLINE_BILLER),
                Arguments.of(BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN, BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_TMB_AUTO_LOAN),
                Arguments.of("ignore", BILLER_GROUP_BILL_PAY, PAYMENT_METHOD_TMB_PRODUCT, BILLER_METHOD_TMB_CREDIT_CARD, BILLER_PAYMENT_CREDIT_CARD),
                Arguments.of("ignore", BILLER_GROUP_BILL_PAY, "ignore", BILLER_METHOD_TMB_LOAN, BILLER_PAYMENT_TMB_LOAN),
                Arguments.of("ignore", BILLER_GROUP_BILL_PAY, BILLER_PAYMENT_METHOD_OFFLINE, "ignore", BILLER_PAYMENT_OFFLINE),
                Arguments.of("comCodeLength15", BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_BILL_PROMPT_PAY),
                Arguments.of(BILL_COMP_CODE_MEA, BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_MEA),
                Arguments.of(BILL_COMP_CODE_MWA, BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_MWA),
                Arguments.of(BILL_COMP_CODE_PEA, BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_PEA),
                Arguments.of(BILL_COMP_CODE_TTB_FLEET_CARD, BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_FLEET_CARD)
        );
    }

    @ParameterizedTest
    @MethodSource("dateTestGetBillProcessorType")
    void testGetBillProcessorType_WhenSuccess_ShouldReturnCorrectBillProcessorType(String compCode, String billerGroupType, String paymentMethod, String billerMethod, String expected) throws TMBCommonException {
        masterBiller.getBillerInfo().setBillerGroupType(billerGroupType).setBillerMethod(billerMethod).setPaymentMethod(paymentMethod);

        String actual = ProcessorSelectorUtils.getBillProcessorType(compCode, masterBiller);

        assertEquals(expected, actual);
    }

    public static Stream<Arguments> dateTestGetBillProcessorTypeOverride() {
        return Stream.of(
                Arguments.of("ignore", BILLER_GROUP_TYPE_TOP_UP, "ignore", "ignore", BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP),
                Arguments.of(BILL_COMP_CODE_E_WALLET, BILLER_GROUP_TYPE_TOP_UP, "ignore", "ignore", BILLER_PAYMENT_TOPUP_E_WALLET),
                Arguments.of("ignore", BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP),
                Arguments.of(BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN, BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_TMB_AUTO_LOAN),
                Arguments.of("ignore", BILLER_GROUP_BILL_PAY, PAYMENT_METHOD_TMB_PRODUCT, BILLER_METHOD_TMB_CREDIT_CARD, BILLER_PAYMENT_CREDIT_CARD),
                Arguments.of("ignore", BILLER_GROUP_BILL_PAY, "ignore", BILLER_METHOD_TMB_LOAN, BILLER_PAYMENT_TMB_LOAN),
                Arguments.of("ignore", BILLER_GROUP_BILL_PAY, BILLER_PAYMENT_METHOD_OFFLINE, "ignore", BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP),
                Arguments.of("comCodeLength15", BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_BILL_PROMPT_PAY),
                Arguments.of(BILL_COMP_CODE_MEA, BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_MEA),
                Arguments.of(BILL_COMP_CODE_MWA, BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_MWA),
                Arguments.of(BILL_COMP_CODE_PEA, BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_PEA),
                Arguments.of(BILL_COMP_CODE_TTB_FLEET_CARD, BILLER_GROUP_BILL_PAY, "ignore", "ignore", BILLER_PAYMENT_FLEET_CARD)
        );
    }

    @ParameterizedTest
    @MethodSource("dateTestGetBillProcessorTypeOverride")
    void testGetBillProcessorTypeOverride_WhenSuccess_ShouldReturnCorrectBillProcessorType(String compCode, String billerGroupType, String paymentMethod, String billerMethod, String expected) throws TMBCommonException {
        masterBiller.getBillerInfo().setBillerGroupType(billerGroupType).setBillerMethod(billerMethod).setPaymentMethod(paymentMethod);

        String actual = ProcessorSelectorUtils.getBillProcessorTypeOverride(compCode, masterBiller);

        assertEquals(expected, actual);
    }

    @Test
    void testGetBillProcessorType_WhenFailedNotBillOrTopUp_ShouldThrowException() {
        MasterBillerResponse incorrectMasterBiller = new MasterBillerResponse();

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> ProcessorSelectorUtils.getBillProcessorTypeOverride(compCode, incorrectMasterBiller));

        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals(ResponseCode.NOT_MATCH_PAYMENT_TYPE_ERROR.getCode(), exception.getErrorCode());
    }

    @Test
    void testGetBillProcessorType_WhenFailedMasterBillerNull_ShouldThrowException() {
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> ProcessorSelectorUtils.getBillProcessorTypeOverride(compCode, null));

        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        assertEquals("masterBiller shouldn't be null, Please verify masterBiller.", exception.getErrorMessage());
    }

    @Test
    void testGetBillProcessorType_WhenFailedCompCodeNull_ShouldThrowException() {
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> ProcessorSelectorUtils.getBillProcessorTypeOverride(null, new MasterBillerResponse()));

        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        assertEquals("compCode shouldn't be null, Please verify compCode.", exception.getErrorMessage());
    }

    private static MasterBillerResponse buildDefaultMasterBiller() {
        return MasterBillerResponse.builder()
                .billerInfo(BillerInfoResponse.builder()
                        .billerGroupType(BILLER_GROUP_BILL_PAY)
                        .build()
                )
                .build();
    }
}