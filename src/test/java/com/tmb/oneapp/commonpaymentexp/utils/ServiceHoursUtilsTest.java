package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.model.CommonTime;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.junit.jupiter.MockitoExtension;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class ServiceHoursUtilsTest {

    @ParameterizedTest
    @CsvSource(value = {
            "00:00,false", "01:00,false", "02:00,false", "03:00,false",
            "04:00,true", "05:00,true", "06:00,true", "07:00,true",
            "20:00,true", "21:00,true", "22:00,true", "23:00,false"
    }, delimiterString = ",")
    void isDuringServiceHoursWhenOverNightTest(String currentTime, Boolean expected) throws ParseException {
        Date current = new SimpleDateFormat("HH:mm").parse(currentTime);
        CommonTime noneServiceHour = new CommonTime();
        noneServiceHour.setStart("23:00");
        noneServiceHour.setEnd("03:00");

        Boolean actual = ServiceHoursUtils.isDuringServiceHours(current, noneServiceHour);

        assertEquals(expected, actual);
    }

    @ParameterizedTest
    @CsvSource({
            "08:00,true", "09:00,false", "10:00,false", "11:00,true",
    })
    void isDuringServiceHoursWhenSameDayTests(String currentTime, Boolean expected) throws ParseException {
        Date current = new SimpleDateFormat("HH:mm").parse(currentTime);
        CommonTime noneServiceHour = new CommonTime();
        noneServiceHour.setStart("09:00");
        noneServiceHour.setEnd("10:00");

        Boolean actual = ServiceHoursUtils.isDuringServiceHours(current, noneServiceHour);

        assertEquals(expected, actual);
    }
}