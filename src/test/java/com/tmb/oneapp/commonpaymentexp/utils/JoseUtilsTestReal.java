// package com.tmb.oneapp.commonpaymentexp.utils;

// import com.fasterxml.jackson.core.JsonProcessingException;
// import com.fasterxml.jackson.core.type.TypeReference;
// import com.nimbusds.jose.JOSEException;
// import com.nimbusds.jose.JWEAlgorithm;
// import com.nimbusds.jose.JWSAlgorithm;
// import com.nimbusds.jose.Payload;
// import com.nimbusds.jose.jwk.JWKSet;
// import com.nimbusds.jose.jwk.KeyUse;
// import com.nimbusds.jose.jwk.RSAKey;
// import com.tmb.common.exception.model.TMBCommonException;
// import com.tmb.common.util.TMBUtils;
// import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
// import com.tmb.oneapp.commonpaymentexp.service.JwkSetProvider;
// import org.junit.jupiter.api.Assertions;
// import org.junit.jupiter.api.Test;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.test.context.ActiveProfiles;

// import java.security.KeyPair;
// import java.security.KeyPairGenerator;
// import java.security.NoSuchAlgorithmException;
// import java.security.interfaces.RSAPublicKey;
// import java.security.interfaces.RSAPrivateKey;
// import java.text.ParseException;
// import java.util.Arrays;

// import net.minidev.json.JSONObject;

// @SpringBootTest
// @ActiveProfiles("local")
// public class JoseUtilsTestReal {

//     @Autowired
//     private JwkSetProvider jwkSetProvider;

//     @Test
//     public void generateLinemanJwk() throws NoSuchAlgorithmException, JOSEException {
//         // Generate a new RSA key pair
//         KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
//         keyPairGenerator.initialize(2048); // You can choose a key size
//         KeyPair keyPair = keyPairGenerator.generateKeyPair();
//         RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
//         RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();

//         // Construct signing key
//         RSAKey signingKey = new RSAKey.Builder(rsaPublicKey)
//                 .privateKey(rsaPrivateKey)
//                 .keyUse(KeyUse.SIGNATURE)
//                 .keyID("signing-key-lazada")
//                 .algorithm(new JWSAlgorithm("RS256"))
//                 .build();

//         // Construct encryption key
//         RSAKey encryptionKey = new RSAKey.Builder(rsaPublicKey)
//                 .privateKey(rsaPrivateKey)
//                 .keyUse(KeyUse.ENCRYPTION)
//                 .keyID("encryption-key-lazada")
//                 .algorithm(new JWEAlgorithm("RSA-OAEP-256"))
//                 .build();

//         // Create JWKSet
//         JWKSet jwkSet = new JWKSet(Arrays.asList(signingKey, encryptionKey));

//         // Log the generated JWK set
//         System.out.println("Generated JWK Set for Lineman:");
//         System.out.println(new JSONObject(jwkSet.toJSONObject(false)).toJSONString());
//     }

//     @Test
//     void encryptTransactionId() throws TMBCommonException, ParseException, JOSEException {
//         String partnerName = "lazada";
//         JWKSet jwkSet = jwkSetProvider.getJwkSet(partnerName);

//         RSAKey rsaPublicKey = (RSAKey) jwkSet.getKeyByKeyId("encryption-key-lazada");
//         String transactionId = "12dc225c-a67e-428d-9046-25898da48a0a";
//         String encryptedTransactionId = JoseUtils.encrypt(transactionId, rsaPublicKey);
//         System.out.println("Encrypted Transaction ID: " + encryptedTransactionId);

//         String decryptedTransactionId = JoseUtils.decrypt(encryptedTransactionId, jwkSet);
//         System.out.println("Decrypted Transaction ID: " + decryptedTransactionId);

//         Assertions.assertEquals(transactionId, decryptedTransactionId);
//     }

//     @Test
//     void decryptTransactionId() throws TMBCommonException, ParseException, JOSEException {
//         String partnerName = "shopee";
//         JWKSet jwkSet = jwkSetProvider.getJwkSet(partnerName);
        
//         // The encrypted string should be provided here for decryption testing.
//         String transactionId = "12dc225c-a67e-428d-9046-25898da48a0a";
//         String encryptedTransactionId = "eyJhbGciOiJSU0EtT0FFUC0yNTYiLCJlbmMiOiJBMjU2R0NNIiwia2lkIjoiZW5jcnlwdGlvbi1rZXktc2hvcGVlIn0.E0cF3X4t9UzxKDtsSJvJ28BHRHR2aqkr57yQ4vo2Fdc2-AObqSX_C-F1HpsPGqwwptDlUQmO7I8NkwENtYX8MD4_yQAQm2uzzdx3VlC6U5zG4ycb3WHHOq2fKQVGXeWy7SkXxDwaCuleqwDvhdrOQbwwYaTH8uvrDCmqBUErWHTqy993va9PQiMErPgzpnlqyTAmVz6YosFQtBqY8ceH6pfa5rVDjoXZm45-SzGs0P8N7nhAHhihVW4otr6t2kWxQWiTa_P_8WiuQhY_Op2fihtpjxw9ej3UeLIBKB4Nl72wO3RrWQtWNbzaGwGdV1Dyjsvxv8H-tIg700G1fVKdtw.hi_SLaIMW9_w3emw.QHbdFk8UVQs9ky66Iv-xy42zg_MTTzye9n8Oz6axMKe8cuJLR7LRrztC169FnLKDi_UL_wt34HooXecPsp8DhgeBdgK1JTmw5fb4pUb1abggsUFPQcPoWDHtXYwf6Kec_8vuNGFokptAKrqZlTQbJ1tTbLSJUd5klX-MVJ1VNeRw6Nle-epMUaJbwAaBtbfW3sdWnDCuWzQ098Adz_3RQ3pbh8fnNxUGNRbwJovhj6HXf2DPA8fvqkPnfF13IRsXSIB6CiD8PjjPdtHPgcypqaAMuXIp7un-YUKJ7SX5ZBffGP_mzTRpXPqNHW4y3g3xdW_q6rpnbTaxLKZSczvG8wILKIA-Nu6r4AqslF0IZQ2Q90fWFmSQOoHY1hiNvnXCwfptmOxOpiTX5gQbWYyR_jQbPxYegKv7qh-edc-65fAn6WNE-nZrPiGeytUNmfZjSWO0MzFebyC6bCSSIeXz-ei9rhOO7vFcJRHPBjZaiKEK4zGYkGpXEHtnaeOdWmdYFCnbzNLQqCWNqslD_9TZfVZ6AU3pxTMAwlDlHR77caK_A-9h-GvQIGdn7ktbIy85SZlLiXoSHsBV03cIBNgjp4Uyg6pg5s9o54KMCCN8OXglpOIQqyqdedc3raEeJfdoeet9Wvemd28XDUlvJsNB00ifsRvLh-Wl_S2_ufWIWpD8YYYNY3B-3f_QshBD2LvS10vKrilCA6yXBN6zjrDBg-BvfOJoenGT10V2lgb5eAOjTWKoocfPAfXm44XrR04Q3Mw4ilXkk1OMqmGZcVWDO0SVpDVqNAbFODPt64XvNcBU0qW1fl6DvIiYqrNF55pEaqNizEMeyHw-ImPjE52BRo0y7l47FWSmPp-4XSG3QWPTQEwZOn33sI2x4O5Wo43CXMirrEM2cneQpraBQh5sUEjqZ2Ei7rvE3dBjUtXTdUE0aL-zYiY3iSF_Q6RGTakvD48kRNLi1ayYUetVPERtppQ6LLhYrGj3ZvEBVe5Mgx5pTtBd5kBUv5HJIDx41dJ1PeqqdoNWCPrQIuRU67q0HPdlUzAhdPVbLrvo7fGsxbx5HUz17iScOA--Rq_jrB9BElCzcJNPFL306CEK6QI.vizEDLgJ0lcXEdkuLegpTA";

//         String decryptedTransactionId = JoseUtils.decrypt(encryptedTransactionId, jwkSet);
//         System.out.println("Decrypted Transaction ID: " + decryptedTransactionId);

//         Assertions.assertEquals(transactionId, decryptedTransactionId);
//     }
    
//     @Test
//     void verifyTransactionId() throws TMBCommonException, ParseException, JOSEException {
//         String partnerName = "shopee";
//         JWKSet jwkSet = jwkSetProvider.getJwkSet(partnerName);

//         RSAKey rsaPublicKey = (RSAKey) jwkSet.getKeyByKeyId("signing-key-shopee");
//         String jwsString = "eyJhbGciOiJSUzI1NiIsImtpZCI6InNpZ25pbmcta2V5LXNob3BlZSJ9.****************************************************************.hDHNcEyyyc6M7Aeavsai6v8JSVh3rVgX9AEJDKg8jhEClKYJRa6hjVsxUascAi8k5TWsrrsfbne40ufeUjYcYIolHVIlxKZFLr94lS-wny35uJBv6M5j0ztNsXscqwsVEfizSpvqdO3gZcdFlES69vJN2YOgi2_ZUUJQiktTMhURIW3Vq-94-zMUN4Emk-yT2ck_TlIb8XvmmKStFqOLvcMGptHS9fjEoYs9jZ6mqIKq5vB7T3X9L4aY60r5xnvSvE2DC5Mm9aDsuopLaAiWsodxg5koDLcWRDJYMEIG_5y8BJ7nTVR_1UUSvHjy8-0BY38wvXZInpRft9lvQqy-6A";
        
//         Payload payload = JoseUtils.verifyJws(jwsString, rsaPublicKey);
//         System.out.println("Verified Payload: " + payload.toString());
//     }

//     @Test
//     void testDeserialize() throws JsonProcessingException {
//         String data = "{\"payment_information\":{\"entry_id\":\"pb\",\"transaction_type\":\"bill_pay\",\"comp_code\":\"2218\",\"fund_code\":\"\",\"deep_link_transaction_id\":\"\",\"require_address_flag\":false,\"product_detail\":{\"product_name_en\":\"Test\",\"product_name_th\":\"Test\",\"product_ref_1\":\"0815486952\",\"product_ref_2\":\"\",\"product_ref_3\":\"\",\"product_ref_4\":\"\",\"product_ref_5\":\"\",\"product_attribute_list\":[{\"label_en\":\"test\",\"label_th\":\"test\",\"value_en\":\"0815486952\",\"value_th\":\"0815486952\"}]},\"amount_detail\":{\"amount_label_en\":\"\",\"amount_label_th\":\"\",\"amount_unit_en\":\"\",\"amount_unit_th\":\"\",\"amount_value\":10,\"payment_amount\":0,\"total_interest\":0,\"disconnected_amount\":0,\"vat\":0},\"complete_screen_detail\":{\"remark_en\":\"\",\"remark_th\":\"\",\"footer_en\":\"\",\"footer_th\":\"\",\"back_btn_key_en\":\"TestApptoApp\",\"back_btn_key_th\":\"TestApptoApp\",\"back_btn_url\":\"anon.POC-PartyApp://\"}}}";
//         InitializationCommonPaymentRequest request = TMBUtils.convertStringToJavaObjWithTypeReference(data, new TypeReference<>() {});
//         System.out.println(request);
//     }
// }
