package com.tmb.oneapp.commonpaymentexp.utils;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableSupplier;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import feign.FeignException;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.Optional;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FeignClientHelperTest {
    @InjectMocks
    FeignClientHelper feignClientHelper;

    @Mock
    CustomerServiceClient customerServiceClient;

    @Nested
    class ExecuteRequestSafelyTest {
        @Test
        void testExecuteRequestSafelyWhenSuccessShouldReturnData() {
            mockFeignReturnSuccessData();

            assertNotNull(feignClientHelper.executeRequestSafely(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));
        }

        @Test
        void testExecuteRequestSafelyWhenFailedFeignExceptionShouldReturnNull() {
            mockFeignThrowFeignException();

            assertNull(feignClientHelper.executeRequestSafely(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));
        }

        @Test
        void testExecuteRequestSafelyWhenFailedGotDataNullShouldReturnNull() {
            mockFeignReturnSuccessWithNullData();

            assertNull(feignClientHelper.executeRequestSafely(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));
        }

        @Test
        void testExecuteRequestSafelyWhenFailedHttpStatus500ShouldReturnNull() {
            mockFeignReturnHttp500InternalServiceError();

            assertNull(feignClientHelper.executeRequestSafely(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));
        }

        @Test
        void testExecuteRequestSafelyWhenFailedStatusCodeNotSuccessShouldReturnNull() {
            mockFeignReturnFailedStatus();

            assertNull(feignClientHelper.executeRequestSafely(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));
        }

        @Test
        void testExecuteRequestSafelyWhenFailedNullPointerExceptionShouldReturnNull() {
            mockFeignReturnSuccessBodyNull(ResponseEntity.ok(null));

            assertNull(feignClientHelper.executeRequestSafely(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));
        }
    }

    @Nested
    class ExecuteRequestOrElseTest {
        @Test
        void testExecuteRequestOrElseWhenSuccessShouldReturnData() {
            var elseAction = new CustomerKYCResponse();
            mockFeignReturnSuccessData();

            assertNotNull(feignClientHelper.executeRequestOrElse(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseAction));
        }

        @Test
        void testExecuteRequestOrElseWhenFailedFeignExceptionShouldReturnElseAction() {
            var elseAction = new CustomerKYCResponse();
            mockFeignThrowFeignException();

            assertEquals(elseAction, feignClientHelper.executeRequestOrElse(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseAction));
        }

        @Test
        void testExecuteRequestOrElseWhenFailedGotDataNullShouldReturnElseAction() {
            var elseAction = new CustomerKYCResponse();
            mockFeignReturnSuccessWithNullData();

            assertEquals(elseAction, feignClientHelper.executeRequestOrElse(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseAction));
        }

        @Test
        void testExecuteRequestOrElseWhenFailedHttpStatus500ShouldReturnElseAction() {
            var elseAction = new CustomerKYCResponse();
            mockFeignReturnHttp500InternalServiceError();

            assertEquals(elseAction, feignClientHelper.executeRequestOrElse(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseAction));
        }

        @Test
        void testExecuteRequestOrElseWhenFailedStatusCodeNotSuccessShouldReturnElseAction() {
            var elseAction = new CustomerKYCResponse();
            mockFeignReturnFailedStatus();

            assertEquals(elseAction, feignClientHelper.executeRequestOrElse(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseAction));
        }

        @Test
        void testExecuteRequestOrElseWhenFailedNullPointerExceptionShouldReturnElseAction() {
            var elseAction = new CustomerKYCResponse();
            mockFeignReturnSuccessBodyNull(ResponseEntity.ok(null));

            assertEquals(elseAction, feignClientHelper.executeRequestOrElse(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseAction));
        }
    }

    @Nested
    class ExecuteRequestOrElseThrowTest {
        @Test
        void testExecuteRequestOrElseThrowWhenSuccessShouldReturnData() throws TMBCommonException {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignReturnSuccessData();

            assertNotNull(feignClientHelper.executeRequestOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));
        }

        @Test
        void testExecuteRequestOrElseThrowWhenFailedFeignExceptionShouldThrowElseException() {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignThrowFeignException();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));

            assertEquals(elseException.get().getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testExecuteRequestOrElseThrowWhenFailedGotDataNullShouldThrowElseException() {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignReturnSuccessWithNullData();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));

            assertEquals(elseException.get().getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testExecuteRequestOrElseThrowWhenFailedHttpStatus500ShouldThrowElseException() {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignReturnHttp500InternalServiceError();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));

            assertEquals(elseException.get().getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testExecuteRequestOrElseThrowWhenFailedStatusCodeNotSuccessShouldThrowElseException() {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignReturnFailedStatus();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));

            assertEquals(elseException.get().getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testExecuteRequestOrElseThrowWhenFailedNullPointerExceptionShouldThrowElseException() {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignReturnSuccessBodyNull(ResponseEntity.ok(null));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));

            assertEquals(elseException.get().getErrorCode(), exception.getErrorCode());
        }
    }

    @Nested
    class ExecuteRequestTest {
        @Test
        void testExecuteRequestWhenSuccessShouldReturnData() throws TMBCommonException {
            mockFeignReturnSuccessData();

            assertNotNull(feignClientHelper.executeRequest(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));
        }

        @Test
        void testExecuteRequestWhenFailedFeignExceptionShouldThrowTMBException() {
            mockFeignThrowFeignException();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequest(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals("FeignException occurred while fetching data", exception.getErrorMessage());
        }

        @Test
        void testExecuteRequestWhenFailedGotDataNullShouldThrowTMBException() {
            mockFeignReturnSuccessWithNullData();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequest(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals("Got null data from feign client", exception.getErrorMessage());
        }

        @Test
        void testExecuteRequestWhenFailedHttpStatus500ShouldThrowTMBException() {
            mockFeignReturnHttp500InternalServiceError();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequest(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals("Non-successful HTTP status code received", exception.getErrorMessage());
        }

        @Test
        void testExecuteRequestWhenFailedStatusCodeNotSuccessShouldThrowTMBException() {
            String errorMessageFromFeignClient = "Service is not available from other service";
            String codeFromFeignClient = "1XXXXX-code-form-other-service";
            Status failedStatusCodeFromOtherService = new Status(codeFromFeignClient, errorMessageFromFeignClient, ResponseCode.FAILED_V2.getService(), null);
            TmbServiceResponse<CustomerKYCResponse> tmbServiceResponse = new TmbServiceResponse<>();
            tmbServiceResponse.setData(new CustomerKYCResponse());
            tmbServiceResponse.setStatus(failedStatusCodeFromOtherService);
            when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequest(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));

            assertEquals(codeFromFeignClient, exception.getErrorCode());
            assertEquals(errorMessageFromFeignClient, exception.getErrorMessage());
        }

        @Test
        void testExecuteRequestWhenFailedNullPointerExceptionShouldThrowTMBException() {
            mockFeignReturnSuccessBodyNull(ResponseEntity.ok(null));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequest(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        }
    }

    @Nested
    class ExecuteRequestCustomTest {
        @Test
        void testExecuteRequestCustomWhenSuccessShouldReturnData() throws TMBCommonException {
            mockFeignReturnSuccessData();
            ThrowableSupplier<CustomerKYCResponse> customSupplier = () -> Optional.of(customerServiceClient.fetchCustomerKYC("correlationId", "crmId"))
                    .filter(r -> r.getStatusCode().is2xxSuccessful())
                    .map(ResponseEntity::getBody)
                    .filter(s -> StringUtils.equalsAnyIgnoreCase(s.getStatus().getCode(), ResponseCode.SUCCESS_V2.getCode(), ResponseCode.SUCCESS.getCode()))
                    .map(TmbServiceResponse::getData)
                    .orElseThrow();

            CustomerKYCResponse actual = feignClientHelper.executeRequestCustom(customSupplier);
            assertEquals(CustomerKYCResponse.class, actual.getClass());
        }

        @Test
        void testExecuteRequestCustomWhenSuccessWithHttp200OkButStatusCode404NotFoundShouldReturnData() throws TMBCommonException {
            Status notFoundStatusCodeNotNormal = new Status("404", "ignore-message", "service", null);
            TmbServiceResponse<CustomerKYCResponse> tmbServiceResponse = new TmbServiceResponse<>();
            tmbServiceResponse.setData(new CustomerKYCResponse());
            tmbServiceResponse.setStatus(notFoundStatusCodeNotNormal);
            when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

            ThrowableSupplier<CustomerKYCResponse> customSupplier = () -> Optional.of(customerServiceClient.fetchCustomerKYC("correlationId", "crmId"))
                    .filter(r -> r.getStatusCode().is2xxSuccessful())
                    .map(ResponseEntity::getBody)
                    .filter(s -> StringUtils.equalsAnyIgnoreCase(s.getStatus().getCode(), notFoundStatusCodeNotNormal.getCode(), ResponseCode.SUCCESS_V2.getCode(), ResponseCode.SUCCESS.getCode()))
                    .map(TmbServiceResponse::getData)
                    .orElseThrow();

            CustomerKYCResponse actual = feignClientHelper.executeRequestCustom(customSupplier);
            assertEquals(CustomerKYCResponse.class, actual.getClass());
        }

        @Test
        void testExecuteRequestCustomWhenFailedFeignExceptionShouldThrowTMBException() {
            mockFeignThrowFeignException();
            ThrowableSupplier<CustomerKYCResponse> customSupplier = () -> Optional.of(customerServiceClient.fetchCustomerKYC("correlationId", "crmId"))
                    .filter(r -> r.getStatusCode().is2xxSuccessful())
                    .map(ResponseEntity::getBody)
                    .filter(s -> StringUtils.equalsAnyIgnoreCase(s.getStatus().getCode(), ResponseCode.SUCCESS_V2.getCode(), ResponseCode.SUCCESS.getCode()))
                    .map(TmbServiceResponse::getData)
                    .orElseThrow();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestCustom(customSupplier));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals("FeignException occurred while fetching data", exception.getErrorMessage());
        }

        @Test
        void testExecuteRequestCustomWhenFailedGotDataNullShouldThrowTMBException() {
            mockFeignReturnSuccessWithNullData();
            ThrowableSupplier<CustomerKYCResponse> customSupplier = () -> Optional.of(customerServiceClient.fetchCustomerKYC("correlationId", "crmId"))
                    .filter(r -> r.getStatusCode().is2xxSuccessful())
                    .map(ResponseEntity::getBody)
                    .filter(s -> StringUtils.equalsAnyIgnoreCase(s.getStatus().getCode(), ResponseCode.SUCCESS_V2.getCode(), ResponseCode.SUCCESS.getCode()))
                    .map(TmbServiceResponse::getData)
                    .orElseThrow();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestCustom(customSupplier));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals("Exception occurred while fetching data", exception.getErrorMessage());
        }

        @Test
        void testExecuteRequestCustomWhenFailedHttpStatus500ShouldThrowTMBException() {
            mockFeignReturnHttp500InternalServiceError();
            ThrowableSupplier<CustomerKYCResponse> customSupplier = () -> Optional.of(customerServiceClient.fetchCustomerKYC("correlationId", "crmId"))
                    .filter(r -> r.getStatusCode().is2xxSuccessful())
                    .map(ResponseEntity::getBody)
                    .filter(s -> StringUtils.equalsAnyIgnoreCase(s.getStatus().getCode(), ResponseCode.SUCCESS_V2.getCode(), ResponseCode.SUCCESS.getCode()))
                    .map(TmbServiceResponse::getData)
                    .orElseThrow();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestCustom(customSupplier));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals("Exception occurred while fetching data", exception.getErrorMessage());
        }

        @Test
        void testExecuteRequestCustomWhenFailedStatusCodeNotSuccessShouldThrowTMBException() {
            TMBCommonException expectedError = CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.NOT_ALLOW_COMMON_PAYMENT_ERROR);

            ThrowableSupplier<CustomerKYCResponse> customSupplier = () -> {
                ResponseEntity<TmbServiceResponse<CustomerKYCResponse>> response = customerServiceClient.fetchCustomerKYC("correlationId", "crmId");
                if (!response.getStatusCode().is2xxSuccessful()) {
                    throw new TMBCommonException("ignore");
                }
                boolean isSuccessCode = StringUtils.equalsAnyIgnoreCase(response.getBody().getStatus().getCode(), ResponseCode.SUCCESS_V2.getCode(), ResponseCode.SUCCESS.getCode());
                if (!isSuccessCode) {
                    throw expectedError;
                }
                return response.getBody().getData();
            };

            Status failedStatusCodeFromOtherService = new Status("404", "ignore-message", ResponseCode.FAILED_V2.getService(), null);
            TmbServiceResponse<CustomerKYCResponse> tmbServiceResponse = new TmbServiceResponse<>();
            tmbServiceResponse.setData(new CustomerKYCResponse());
            tmbServiceResponse.setStatus(failedStatusCodeFromOtherService);
            when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestCustom(customSupplier));

            assertEquals(expectedError.getErrorCode(), exception.getErrorCode());
            assertEquals(expectedError.getErrorMessage(), exception.getErrorMessage());
        }

        @Test
        void testExecuteRequestCustomWhenFailedNullPointerExceptionShouldThrowTMBException() {
            mockFeignReturnSuccessBodyNull(ResponseEntity.ok(null));
            ThrowableSupplier<CustomerKYCResponse> customSupplier = () -> Optional.of(customerServiceClient.fetchCustomerKYC("correlationId", "crmId"))
                    .filter(r -> r.getStatusCode().is2xxSuccessful())
                    .map(ResponseEntity::getBody)
                    .filter(s -> StringUtils.equalsAnyIgnoreCase(s.getStatus().getCode(), ResponseCode.SUCCESS_V2.getCode(), ResponseCode.SUCCESS.getCode()))
                    .map(TmbServiceResponse::getData)
                    .orElseThrow();


            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestCustom(customSupplier));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        }
    }

    @Nested
    class ExecuteRequestNullableTest {
        @Test
        void testExecuteRequestNullableWhenSuccessShouldReturnData() throws TMBCommonException {
            mockFeignReturnSuccessData();

            assertNotNull(feignClientHelper.executeRequestNullable(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));
        }

        @Test
        void testExecuteRequestNullableWhenSuccessGotDataNullShouldReturnNull() throws TMBCommonException {
            mockFeignReturnSuccessWithNullData();

            assertNull(feignClientHelper.executeRequestNullable(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));
        }

        @Test
        void testExecuteRequestNullableWhenFailedFeignExceptionShouldThrowTMBException() {
            mockFeignThrowFeignException();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestNullable(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals("FeignException occurred while fetching data", exception.getErrorMessage());
        }

        @Test
        void testExecuteRequestNullableWhenFailedHttpStatus500ShouldThrowTMBException() {
            mockFeignReturnHttp500InternalServiceError();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestNullable(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals("Non-successful HTTP status code received", exception.getErrorMessage());
        }

        @Test
        void testExecuteRequestNullableWhenFailedStatusCodeNotSuccessShouldThrowTMBException() {
            String errorMessageFromFeignClient = "Service is not available from other service";
            String codeFromFeignClient = "1XXXXX-code-form-other-service";
            Status failedStatusCodeFromOtherService = new Status(codeFromFeignClient, errorMessageFromFeignClient, ResponseCode.FAILED_V2.getService(), null);
            TmbServiceResponse<CustomerKYCResponse> tmbServiceResponse = new TmbServiceResponse<>();
            tmbServiceResponse.setData(new CustomerKYCResponse());
            tmbServiceResponse.setStatus(failedStatusCodeFromOtherService);
            when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestNullable(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));

            assertEquals(codeFromFeignClient, exception.getErrorCode());
            assertEquals(errorMessageFromFeignClient, exception.getErrorMessage());
        }

        @Test
        void testExecuteRequestNullableWhenFailedNullPointerExceptionShouldThrowTMBException() {
            mockFeignReturnSuccessBodyNull(ResponseEntity.ok(null));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestNullable(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId")));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        }
    }

    @Nested
    class ExecuteRequestNullableOrElseThrowTest {
        @Test
        void testExecuteRequestNullableOrElseThrowWhenSuccessShouldReturnData() throws TMBCommonException {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignReturnSuccessData();

            assertNotNull(feignClientHelper.executeRequestNullableOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));
        }

        @Test
        void testExecuteRequestNullableOrElseThrowWhenSuccessGotDataNullShouldReturnNull() throws TMBCommonException {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignReturnSuccessWithNullData();

            assertNull(feignClientHelper.executeRequestNullableOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));
        }

        @Test
        void testExecuteRequestNullableOrElseThrowWhenFailedFeignExceptionShouldThrowElseException() {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignThrowFeignException();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestNullableOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));

            assertEquals(elseException.get().getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testExecuteRequestNullableOrElseThrowWhenFailedHttpStatus500ShouldThrowElseException() {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignReturnHttp500InternalServiceError();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestNullableOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));

            assertEquals(elseException.get().getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testExecuteRequestNullableOrElseThrowWhenFailedStatusCodeNotSuccessShouldThrowElseException() {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignReturnFailedStatus();

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestNullableOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));

            assertEquals(elseException.get().getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testExecuteRequestNullableOrElseThrowWhenFailedNullPointerExceptionShouldThrowElseException() {
            Supplier<TMBCommonException> elseException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.ETE_ERROR);
            mockFeignReturnSuccessBodyNull(ResponseEntity.ok(null));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> feignClientHelper.executeRequestNullableOrElseThrow(() -> customerServiceClient.fetchCustomerKYC("correlationId", "crmId"), elseException));

            assertEquals(elseException.get().getErrorCode(), exception.getErrorCode());
        }
    }

    private void mockFeignReturnSuccessBodyNull(ResponseEntity<TmbServiceResponse<CustomerKYCResponse>> ok) {
        when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenReturn(ok);
    }

    private void mockFeignReturnHttp500InternalServiceError() {
        when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenReturn(ResponseEntity.internalServerError().body(new TmbServiceResponse<>()));
    }

    private void mockFeignThrowFeignException() {
        when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenThrow(FeignException.class);
    }

    private void mockFeignReturnFailedStatus() {
        TmbServiceResponse<CustomerKYCResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new CustomerKYCResponse());
        tmbServiceResponse.setStatus(getStatusCodeFailed());
        when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockFeignReturnSuccessWithNullData() {
        TmbServiceResponse<CustomerKYCResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(null);
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockFeignReturnSuccessData() {
        TmbServiceResponse<CustomerKYCResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new CustomerKYCResponse());
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        when(customerServiceClient.fetchCustomerKYC(anyString(), anyString())).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private static Status getStatusCodeFailed() {
        return new Status(ResponseCode.FAILED_V2.getCode(), ResponseCode.FAILED_V2.getMessage(), ResponseCode.FAILED_V2.getService(), null);
    }
}