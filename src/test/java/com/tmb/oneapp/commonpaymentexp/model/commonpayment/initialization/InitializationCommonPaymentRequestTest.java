package com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PhraseDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class InitializationCommonPaymentRequestTest {

    private static Validator validator;

    private static ValidatorFactory validatorFactory;

    @BeforeAll
    static void setUpValidatorFactory() {
        validatorFactory = Validation.buildDefaultValidatorFactory();
        validator = validatorFactory.getValidator();
    }

    @AfterAll
    static void closeValidatorFactory() {
        validatorFactory.close();
    }

    @Test
    void testInitializationCommonPaymentRequestWhenAmountIsZeroShouldFailed() {
        BigDecimal amountZeroShouldFailed = new BigDecimal("0.00");
        var request = initialCompleteRequestValidation();
        request.getPaymentInformation().getAmountDetail().setAmountValue(amountZeroShouldFailed);

        Set<ConstraintViolation<InitializationCommonPaymentRequest>> actualViolations = validator.validate(request);

        int expectedError = 1;
        assertEquals(expectedError, actualViolations.size());
        assertEquals("AmountValue must be positive", actualViolations.iterator().next().getMessage());
    }

    @Test
    void testInitializationCommonPaymentRequestWhenAmountIsNegativeShouldFailed() {
        BigDecimal amountZeroShouldFailed = new BigDecimal("-1.00");
        var request = initialCompleteRequestValidation();
        request.getPaymentInformation().getAmountDetail().setAmountValue(amountZeroShouldFailed);

        Set<ConstraintViolation<InitializationCommonPaymentRequest>> actualViolations = validator.validate(request);

        int expectedError = 2;
        assertEquals(expectedError, actualViolations.size());
        assertTrue(isContainMessage(actualViolations, "AmountValue must be positive"));
        assertTrue(isContainMessage(actualViolations, "AmountValue must be positive or zero"));
    }

    private static boolean isContainMessage(Set<ConstraintViolation<InitializationCommonPaymentRequest>> violations, String text) {
        return violations.stream().anyMatch(v -> v.getMessage().equals(text));
    }

    private static InitializationCommonPaymentRequest initialCompleteRequestValidation() {
        return new InitializationCommonPaymentRequest().setPaymentInformation(new PaymentInformation()
                .setEntryId("entry-id")
                .setTransactionType("bill_pay")
                .setRequireAddressFlag(false)
                .setProductDetail(new ProductDetail().setProductAttributeList(List.of(new PhraseDetail().setValueEn("en").setValueTh("th"))))
                .setAmountDetail(new AmountDetail().setAmountValue(new BigDecimal("1.00")))
        );
    }
}