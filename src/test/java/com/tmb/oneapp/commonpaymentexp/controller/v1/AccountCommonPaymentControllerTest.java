package com.tmb.oneapp.commonpaymentexp.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentProcessor;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentProcessorSelector;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AccountCommonPaymentControllerTest {
    @InjectMocks
    AccountCommonPaymentController accountCommonPaymentController;

    @Mock
    AccountCommonPaymentProcessorSelector accountCommonPaymentProcessorSelector;

    @Mock
    AccountCommonPaymentProcessor<List<DepositAccount>> processor;

    String crmId;
    String correlationId;
    HttpHeaders headers;
    String filterAccountType;

    @BeforeEach
    void setUp() {
        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        filterAccountType = "accountType";
    }

    @Test
    void testGetAccountList_ShouldSuccess() throws TMBCommonException {
        doNothing().when(accountCommonPaymentProcessorSelector).validateFilterAccountTypeKey(filterAccountType);
        when(accountCommonPaymentProcessorSelector.getProcessor(filterAccountType)).thenReturn(processor);
        when(processor.getAccountList(correlationId, crmId)).thenReturn(new ArrayList<>());

        Assertions.assertDoesNotThrow(() -> accountCommonPaymentController.getAccountList(correlationId, crmId, filterAccountType, headers));
    }

    @Test
    void testGetAccountList_WhenValidateFilterAccountTypeKeyThrowException_ShouldSuccess() throws TMBCommonException {
        filterAccountType = "incorrect-account-type";
        doThrow(TMBCommonException.class).when(accountCommonPaymentProcessorSelector).validateFilterAccountTypeKey(filterAccountType);

        Assertions.assertThrows(TMBCommonException.class, () -> accountCommonPaymentController.getAccountList(correlationId, crmId, filterAccountType, headers));
    }
}