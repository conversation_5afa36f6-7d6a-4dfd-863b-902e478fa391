package com.tmb.oneapp.commonpaymentexp.controller.v1.security;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.EncryptedPayloadRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.PublicKeyResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.SignedPayloadResponse;
import com.tmb.oneapp.commonpaymentexp.service.CommonPaymentPartnerIntegrationService;
import com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment.InitializationCommonPaymentPartnerService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonPaymentPartnerIntegrationControllerTest {

    @Mock
    private CommonPaymentPartnerIntegrationService commonPaymentPartnerIntegrationService;

    @Mock
    private InitializationCommonPaymentPartnerService initializationCommonPaymentPartnerService;

    @InjectMocks
    private CommonPaymentPartnerIntegrationController partnerIntegrationController;

    @Test
    void testGetPublicKeySuccess() throws TMBCommonException {
        String partnerName = "test-partner";
        Map<String, Object> jwkSetMap = Map.of("kty", "RSA", "e", "AQAB");
        PublicKeyResponse publicKeyResponse = new PublicKeyResponse(jwkSetMap);

        when(commonPaymentPartnerIntegrationService.getPublicKey(partnerName)).thenReturn(publicKeyResponse);

        ResponseEntity<TmbServiceResponse<PublicKeyResponse>> responseEntity = partnerIntegrationController.getPublicKey(partnerName);

        assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        TmbServiceResponse<PublicKeyResponse> responseBody = responseEntity.getBody();
        assertNotNull(responseBody);
        assertEquals(ResponseCode.SUCCESS_V2.getCode(), responseBody.getStatus().getCode());
        assertEquals(ResponseCode.SUCCESS_V2.getMessage(), responseBody.getStatus().getMessage());
        assertNotNull(responseBody.getData());
        assertEquals(jwkSetMap, responseBody.getData().getJwkSet());
    }

    @Test
    void testGetPublicKeyPartnerNotFound() throws TMBCommonException {
        String partnerName = "unknown-partner";
        TMBCommonException givenException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                "JWKSet not found for client: " + partnerName,
                ResponseCode.FAILED_V2.getService(),
                HttpStatus.BAD_REQUEST,
                null
        );

        when(commonPaymentPartnerIntegrationService.getPublicKey(partnerName)).thenThrow(givenException);

        TMBCommonException thrownException = assertThrows(TMBCommonException.class, () -> {
            partnerIntegrationController.getPublicKey(partnerName);
        });

        assertEquals(givenException.getErrorCode(), thrownException.getErrorCode());
        assertEquals(givenException.getErrorMessage(), thrownException.getErrorMessage());
    }

    @Test
    void testInitialEncryptedPaymentSuccess() throws TMBCommonException {
        String partnerName = "test-partner";
        String encryptedPayloadStr = "ey...encrypted...";
        EncryptedPayloadRequest encryptedRequest = new EncryptedPayloadRequest();
        encryptedRequest.setPayload(encryptedPayloadStr);

        InitializationCommonPaymentRequest decryptedRequest = new InitializationCommonPaymentRequest();
        InitializationCommonPaymentResponse mockResponseData = new InitializationCommonPaymentResponse();
        HttpHeaders headers = new HttpHeaders();

        when(commonPaymentPartnerIntegrationService.decryptInitialPayload(encryptedPayloadStr, partnerName)).thenReturn(decryptedRequest);
        when(initializationCommonPaymentPartnerService.initialCommonPayment(decryptedRequest, headers)).thenReturn(mockResponseData);

        String expectedSignedPayload = "signed-success-payload";
        when(commonPaymentPartnerIntegrationService.signServiceResponse(any(String.class), any(TmbServiceResponse.class))).thenReturn(expectedSignedPayload);

        ResponseEntity<SignedPayloadResponse> responseEntity =
                partnerIntegrationController.initialEncryptedPayment(partnerName, encryptedRequest, headers);

        assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        SignedPayloadResponse responseBody = responseEntity.getBody();
        assertNotNull(responseBody);
        assertEquals(expectedSignedPayload, responseBody.getPayload());
    }

    @Test
    void testInitialEncryptedPaymentDecryptionFailed() throws TMBCommonException {
        String partnerName = "test-partner";
        String encryptedPayloadStr = "ey...invalid-encrypted...";
        EncryptedPayloadRequest encryptedRequest = new EncryptedPayloadRequest();
        encryptedRequest.setPayload(encryptedPayloadStr);
        HttpHeaders headers = new HttpHeaders();
        TMBCommonException givenException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                "Decryption failed",
                ResponseCode.FAILED_V2.getService(),
                HttpStatus.BAD_REQUEST,
                null
        );

        when(commonPaymentPartnerIntegrationService.decryptInitialPayload(encryptedPayloadStr, partnerName)).thenThrow(givenException);

        String expectedSignedPayload = "signed-error-payload";
        when(commonPaymentPartnerIntegrationService.signServiceResponse(any(String.class), any(TmbServiceResponse.class))).thenReturn(expectedSignedPayload);

        ResponseEntity<SignedPayloadResponse> responseEntity =
                partnerIntegrationController.initialEncryptedPayment(partnerName, encryptedRequest, headers);

        assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
        SignedPayloadResponse responseBody = responseEntity.getBody();
        assertNotNull(responseBody);
        assertEquals(expectedSignedPayload, responseBody.getPayload());
    }

    @Test
    void testInitialEncryptedPaymentSigningFailed() throws TMBCommonException {
        String partnerName = "test-partner";
        String encryptedPayloadStr = "ey...valid-encrypted...";
        EncryptedPayloadRequest encryptedRequest = new EncryptedPayloadRequest();
        encryptedRequest.setPayload(encryptedPayloadStr);
        HttpHeaders headers = new HttpHeaders();

        InitializationCommonPaymentRequest decryptedRequest = new InitializationCommonPaymentRequest();
        InitializationCommonPaymentResponse mockResponseData = new InitializationCommonPaymentResponse();

        when(commonPaymentPartnerIntegrationService.decryptInitialPayload(encryptedPayloadStr, partnerName)).thenReturn(decryptedRequest);
        when(initializationCommonPaymentPartnerService.initialCommonPayment(decryptedRequest, headers)).thenReturn(mockResponseData);

        TMBCommonException signingException = new TMBCommonException(
                ResponseCode.PAYLOAD_SERIALIZATION_FAILED.getCode(),
                "Failed to sign response data",
                ResponseCode.PAYLOAD_SERIALIZATION_FAILED.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                null
        );
        when(commonPaymentPartnerIntegrationService.signServiceResponse(any(String.class), any(TmbServiceResponse.class))).thenThrow(signingException);

        ResponseEntity<SignedPayloadResponse> responseEntity =
                partnerIntegrationController.initialEncryptedPayment(partnerName, encryptedRequest, headers);

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, responseEntity.getStatusCode());
        SignedPayloadResponse responseBody = responseEntity.getBody();
        assertNotNull(responseBody);
        assertEquals("{\"error\":\"Failed to sign response data\"}", responseBody.getPayload());
    }
}