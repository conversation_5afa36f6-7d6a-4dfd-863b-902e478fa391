package com.tmb.oneapp.commonpaymentexp.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PhraseDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.PaymentMethodCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment.InitializationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ConfirmationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ValidationCommonPaymentService;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonPaymentControllerTest {
    @InjectMocks
    CommonPaymentController commonPaymentController;

    @Mock
    InitializationCommonPaymentService initializationCommonPaymentService;

    @Mock
    PaymentMethodCommonPaymentService paymentMethodCommonPaymentService;

    @Mock
    ValidationCommonPaymentService validationCommonPaymentService;

    @Mock
    ConfirmationCommonPaymentService confirmationCommonPaymentService;

    String crmId;
    String correlationId;
    HttpHeaders headers;
    Validator validator;

    @BeforeEach
    void setUp() {
        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testInitialCommonPayment_ShouldSuccess() throws TMBCommonException {
        when(initializationCommonPaymentService.initialCommonPayment(any(), any())).thenReturn(new InitializationCommonPaymentResponse());

        Assertions.assertDoesNotThrow(() -> commonPaymentController.initialCommonPayment(correlationId, crmId, new InitializationCommonPaymentRequest(), headers));
    }

    @Test
    void testGetCommonPaymentMethod_ShouldSuccess() throws TMBCommonException {
        when(paymentMethodCommonPaymentService.getCommonPaymentMethod(anyString(), any())).thenReturn(new PaymentMethodCommonPaymentResponse());

        Assertions.assertDoesNotThrow(() -> commonPaymentController.getCommonPaymentMethod(correlationId, crmId, "transaction-id", headers));
    }

    @Test
    void testValidateCommonPayment_ShouldSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
        when(validationCommonPaymentService.validateCommonPayment(any(), any())).thenReturn(new ValidationCommonPaymentResponse());

        Assertions.assertDoesNotThrow(() -> commonPaymentController.validateCommonPayment(correlationId, crmId, new ValidationCommonPaymentRequest(), headers));
    }

    @Test
    void testConfirmCommonPayment_ShouldSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
        when(confirmationCommonPaymentService.confirmCommonPayment(any(), any())).thenReturn(new ConfirmationCommonPaymentResponse());

        Assertions.assertDoesNotThrow(() -> commonPaymentController.confirmCommonPayment(correlationId, crmId, new ConfirmationCommonPaymentRequest(), headers));
    }

    // ================== START Validate request ==================
    @ParameterizedTest
    @CsvSource(value = {
            "Invalid\"Note",
            "Invalid=Note",
            "Invalid<Note",
            "Invalid>Note",
            "Invalid~Note",
            "Invalid\\Note",
            "Invalid'Note"
    }, delimiterString = ",")
    void testNoteWithInvalidCharacters(String invalidNote) {
        ValidationCommonPaymentRequest request = initDefaultValidationCommonPaymentRequest();
        request.setNote(invalidNote);

        Set<ConstraintViolation<ValidationCommonPaymentRequest>> violations = validator.validate(request);

        String expectedMessage = "Note must not contain invalid characters: \" ' = < > ~ \\";
        int expectedError = 1;
        Assertions.assertEquals(expectedError, violations.size());
        Assertions.assertEquals(expectedMessage, violations.iterator().next().getMessage());
    }

    @ParameterizedTest
    @CsvSource(value = {
            "ValidNote123",
            "AnotherValid_Note",
            "Valid-Note.WithoutErrors",
            "Alphanumeric123456",
            "SymbolsAllowedLike@#$%^&*()",
            "Spaces Are Also Valid",
            "null",
            "empty"
    }, delimiterString = ",", nullValues = "null", emptyValue = "empty")
    void testNoteWithOnlyValidCharacters(String validNote) {
        ValidationCommonPaymentRequest request = initDefaultValidationCommonPaymentRequest();
        request.setNote(validNote);

        Set<ConstraintViolation<ValidationCommonPaymentRequest>> violations = validator.validate(request);

        int expectedNotError = 0;
        Assertions.assertEquals(expectedNotError, violations.size());
    }

    @ParameterizedTest
    @CsvSource(value = {
            "valid_reference123",
            "1234567890",
            "empty",
            "null"
    }, delimiterString = ",", nullValues = "null", emptyValue = "empty")
    void testReference1WithOnlyValidCharacters(String validReference) {

        InitializationCommonPaymentRequest request = mockInitializationCommonPaymentRequest();
        ProductDetail productDetail = request.getPaymentInformation().getProductDetail();
        productDetail.setProductRef1(validReference);

        Set<ConstraintViolation<InitializationCommonPaymentRequest>> violations = validator.validate(request);

        int expectedNotError = 0;
        Assertions.assertEquals(expectedNotError, violations.size());
    }

    @ParameterizedTest
    @CsvSource(value = {
            "valid_reference123",
            "1234567890",
            "empty",
            "null"
    }, delimiterString = ",", nullValues = "null", emptyValue = "empty")
    void testReference2WithOnlyValidCharacters(String validReference) {
        InitializationCommonPaymentRequest request = mockInitializationCommonPaymentRequest();
        ProductDetail productDetail = request.getPaymentInformation().getProductDetail();
        productDetail.setProductRef1(null);
        productDetail.setProductRef2(validReference);

        Set<ConstraintViolation<InitializationCommonPaymentRequest>> violations = validator.validate(request);

        int expectedNotError = 0;
        Assertions.assertEquals(expectedNotError, violations.size());
    }

    @ParameterizedTest
    @CsvSource(value = {
            "valid_reference123",
            "1234567890",
            "empty",
            "null"
    }, delimiterString = ",", nullValues = "null", emptyValue = "empty")
    void testReference3WithOnlyValidCharacters(String validReference) {
        InitializationCommonPaymentRequest request = mockInitializationCommonPaymentRequest();
        ProductDetail productDetail = request.getPaymentInformation().getProductDetail();

        productDetail.setProductRef1(null);
        productDetail.setProductRef2(null);
        productDetail.setProductRef3(validReference);

        Set<ConstraintViolation<InitializationCommonPaymentRequest>> violations = validator.validate(request);

        int expectedNotError = 0;
        Assertions.assertEquals(expectedNotError, violations.size());
    }

    @ParameterizedTest
    @CsvSource(value = {
            "invalid@reference!",
            "#@$%^"
    }, delimiterString = ",", nullValues = "null", emptyValue = "empty")
    void testReference1WithOnlyInvalidCharacters(String invalidReference) {
        InitializationCommonPaymentRequest request = new InitializationCommonPaymentRequest();
        PaymentInformation paymentInformation = new PaymentInformation();
        paymentInformation.setEntryId("entry-id");
        paymentInformation.setTransactionType("bill_pay");
        AmountDetail amountDetail = new AmountDetail();
        amountDetail.setAmountValue(BigDecimal.valueOf(1000));
        paymentInformation.setAmountDetail(amountDetail);
        ProductDetail productDetail = new ProductDetail();
        productDetail.setProductRef1(invalidReference);
        PhraseDetail phraseDetail = new PhraseDetail();
        phraseDetail.setLabelEn("label-en");
        phraseDetail.setLabelTh("label-th");
        phraseDetail.setValueEn("value-en");
        phraseDetail.setValueTh("value-th");
        productDetail.setProductAttributeList(List.of(phraseDetail));
        paymentInformation.setProductDetail(productDetail);
        request.setPaymentInformation(paymentInformation);

        Set<ConstraintViolation<InitializationCommonPaymentRequest>> violations = validator.validate(request);
        System.out.println(violations);

        int expectedError = 1;
        Assertions.assertEquals(expectedError, violations.size());
        Assertions.assertEquals("Reference 1 contains invalid characters", violations.iterator().next().getMessage());
    }

    @ParameterizedTest
    @CsvSource(value = {
            "invalid@reference!",
            "#@$%^"
    }, delimiterString = ",", nullValues = "null", emptyValue = "empty")
    void testReference2WithOnlyInvalidCharacters(String invalidReference) {

        InitializationCommonPaymentRequest request = new InitializationCommonPaymentRequest();
        PaymentInformation paymentInformation = new PaymentInformation();
        paymentInformation.setEntryId("entry-id");
        paymentInformation.setTransactionType("bill_pay");
        AmountDetail amountDetail = new AmountDetail();
        amountDetail.setAmountValue(BigDecimal.valueOf(1000));
        paymentInformation.setAmountDetail(amountDetail);
        ProductDetail productDetail = new ProductDetail();
        productDetail.setProductRef1(null);
        productDetail.setProductRef2(invalidReference);
        PhraseDetail phraseDetail = new PhraseDetail();
        phraseDetail.setLabelEn("label-en");
        phraseDetail.setLabelTh("label-th");
        phraseDetail.setValueEn("value-en");
        phraseDetail.setValueTh("value-th");
        productDetail.setProductAttributeList(List.of(phraseDetail));
        paymentInformation.setProductDetail(productDetail);
        request.setPaymentInformation(paymentInformation);

        Set<ConstraintViolation<InitializationCommonPaymentRequest>> violations = validator.validate(request);

        int expectedError = 1;
        Assertions.assertEquals(expectedError, violations.size());
        Assertions.assertEquals("Reference 2 contains invalid characters", violations.iterator().next().getMessage());
    }

    @ParameterizedTest
    @CsvSource(value = {
            "invalid@reference!",
            "#@$%^"
    }, delimiterString = ",", nullValues = "null", emptyValue = "empty")
    void testReference3WithOnlyInvalidCharacters(String invalidReference) {
        InitializationCommonPaymentRequest request = mockInitializationCommonPaymentRequest();
        ProductDetail productDetail = request.getPaymentInformation().getProductDetail();
        productDetail.setProductRef1(null);
        productDetail.setProductRef2(null);
        productDetail.setProductRef3(invalidReference);

        Set<ConstraintViolation<InitializationCommonPaymentRequest>> violations = validator.validate(request);

        int expectedError = 1;
        Assertions.assertEquals(expectedError, violations.size());
        Assertions.assertEquals("Reference 3 contains invalid characters", violations.iterator().next().getMessage());
    }

    @Test
    void testTransactionIdWithInvalidCharacters() {
        ValidationCommonPaymentRequest request = initDefaultValidationCommonPaymentRequest();
        request.setTransactionId("");

        Set<ConstraintViolation<ValidationCommonPaymentRequest>> violations = validator.validate(request);

        int expectedError = 1;
        Assertions.assertEquals(expectedError, violations.size());
        Assertions.assertEquals("transaction_id should not be blank", violations.iterator().next().getMessage());
    }

    @Test
    void testFlowWithInvalidCharacters() {
        ValidationCommonPaymentRequest request = initDefaultValidationCommonPaymentRequest();
        request.setFlow("");

        Set<ConstraintViolation<ValidationCommonPaymentRequest>> violations = validator.validate(request);

        int expectedError = 1;
        Assertions.assertEquals(expectedError, violations.size());
        Assertions.assertEquals("flow should not be blank", violations.iterator().next().getMessage());
    }

    private static ValidationCommonPaymentRequest initDefaultValidationCommonPaymentRequest() {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        request.setTransactionId("transaction-id");
        request.setFlow("flow");
        return request;
    }

    private InitializationCommonPaymentRequest mockInitializationCommonPaymentRequest() {
        InitializationCommonPaymentRequest request = new InitializationCommonPaymentRequest();
        PaymentInformation paymentInformation = new PaymentInformation();
        paymentInformation.setEntryId("entry-id");
        paymentInformation.setTransactionType("bill_pay");
        AmountDetail amountDetail = new AmountDetail();
        amountDetail.setAmountValue(BigDecimal.valueOf(1000));
        paymentInformation.setAmountDetail(amountDetail);
        ProductDetail productDetail = new ProductDetail();
        PhraseDetail phraseDetail = new PhraseDetail();
        phraseDetail.setLabelEn("label-en");
        phraseDetail.setLabelTh("label-th");
        phraseDetail.setValueEn("value-en");
        phraseDetail.setValueTh("value-th");
        productDetail.setProductAttributeList(List.of(phraseDetail));
        paymentInformation.setProductDetail(productDetail);
        request.setPaymentInformation(paymentInformation);
        return request;
    }
    // ================== END Validate request ==================

}