package com.tmb.oneapp.commonpaymentexp.controller.v1;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.service.AccountCreditCardService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

import java.util.ArrayList;

import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CreditCardAccountCommonPaymentControllerTest {
    @InjectMocks
    CreditCardAccountCommonPaymentController creditCardAccountCommonPaymentController;

    @Mock
    AccountCreditCardService accountCreditCardService;

    String crmId;
    String correlationId;
    HttpHeaders headers;

    @BeforeEach
    void setUp() {
        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
    }

    @Test
    void testGetCreditCardList_ShouldSuccess() throws TMBCommonException {
        when(accountCreditCardService.getCreditCardAccounts(correlationId, crmId)).thenReturn(new ArrayList<>());

        Assertions.assertDoesNotThrow(() -> creditCardAccountCommonPaymentController.getCreditCardList(correlationId, crmId, headers));
    }

    @Test
    void testGetCardsPointList_ShouldSuccess() throws TMBCommonException {
        when(accountCreditCardService.getCardPointCreditCard(correlationId, crmId)).thenReturn(new ArrayList<>());

        Assertions.assertDoesNotThrow(() -> creditCardAccountCommonPaymentController.getCardsPointList(correlationId, crmId, headers));
    }
}