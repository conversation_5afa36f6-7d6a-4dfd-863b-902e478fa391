package com.tmb.oneapp.commonpaymentexp.validator.specialbiller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class ToyotaBillValidatorTest {

    @InjectMocks
    private ToyotaBillValidator toyotaBillValidation;

    @BeforeEach
    void setUp() {
    }

    @Test
    void testCheckDigit_WhenValidInput_ShouldDoesNotThrow() {
        String ref1 = "123456789012345678"; // 18 characters, valid
        String ref2 = "876543210987654348"; // 18 characters, valid

        assertDoesNotThrow(() -> toyotaBillValidation.checkDigit(ref1, ref2));
    }

    @Test
    void testCheckDigit_WhenRef1TooLong_ShouldThrowTMBCommonException() {
        String ref1 = "12345678901234567890"; // 20 characters, invalid
        String ref2 = "876543210987654321";  // 18 characters, valid

        TMBCommonException exception = assertThrows(
                TMBCommonException.class,
                () -> toyotaBillValidation.checkDigit(ref1, ref2)
        );

        assertEquals(ResponseCode.INCORRECT_TOYOTA_REF.getCode(), exception.getErrorCode());
    }

    @Test
    void testCheckDigit_WhenRef2TooLong_ShouldThrowTMBCommonException() {
        String ref1 = "123456789012345678";  // 18 characters, valid
        String ref2 = "87654321098765432199"; // 20 characters, invalid

        TMBCommonException exception = assertThrows(
                TMBCommonException.class,
                () -> toyotaBillValidation.checkDigit(ref1, ref2)
        );

        assertEquals(ResponseCode.INCORRECT_TOYOTA_REF.getCode(), exception.getErrorCode());
    }

    @Test
    void testCheckDigit_WhenRef1Empty__ShouldThrowTMBCommonException() {
        String ref1 = ""; // Empty, invalid
        String ref2 = "876543210987654321";  // 18 characters, valid

        TMBCommonException exception = assertThrows(
                TMBCommonException.class,
                () -> toyotaBillValidation.checkDigit(ref1, ref2)
        );

        assertEquals(ResponseCode.INCORRECT_TOYOTA_REF.getCode(), exception.getErrorCode());
    }

    @Test
    void testCheckDigit_WhenInvalidCheckDigit__ShouldThrowTMBCommonException() {
        String ref1 = "123456789012345678"; // Valid
        String ref2 = "876543210987654320"; // Invalid check digit

        TMBCommonException exception = assertThrows(
                TMBCommonException.class,
                () -> toyotaBillValidation.checkDigit(ref1, ref2)
        );

        assertEquals(ResponseCode.INCORRECT_TOYOTA_REF.getCode(), exception.getErrorCode());
    }

    @Test
    void testCheckDigit_WhenNumberFormatException__ShouldThrowTMBCommonException() {
        String ref1 = "123ABC789012345678"; // Contains non-numeric characters, invalid
        String ref2 = "876543210987654321"; // Valid

        TMBCommonException exception = assertThrows(
                TMBCommonException.class,
                () -> toyotaBillValidation.checkDigit(ref1, ref2)
        );

        assertEquals(ResponseCode.INCORRECT_TOYOTA_REF.getCode(), exception.getErrorCode());
    }
}