package com.tmb.oneapp.commonpaymentexp.validator.specialbiller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;

@ExtendWith(MockitoExtension.class)
public class LandTransportBillValidatorTest {

    @Spy
    @InjectMocks
    private LandTransportBillValidator landTransportBillValidation;

    @BeforeEach
    void setUp() {
    }

    @Test
    void testSum() {
        String s = "123456";
        int c1 = 2;
        int c2 = 3;

        int result = landTransportBillValidation.sum(s, c1, c2);

        int expected = 1 * 2 + 2 * 3 + 3 * 2 + 4 * 3 + 5 * 2 + 6 * 3;
        assertEquals(expected, result);
    }

    @Test
    void testMod() {
        int total = 123;
        int modulo = 100;

        String result = landTransportBillValidation.mod(total, modulo);

        assertEquals("23", result);
    }

    @Test
    void testModWithSingleDigitResult() {
        int total = 5;
        int modulo = 10;

        String result = landTransportBillValidation.mod(total, modulo);

        assertEquals("05", result);
    }

    @Test
    void testCheckDigit_WhenValid_ShouldDoesNotThrow() {
        String ref1 = "123456789";
        String ref2 = "987654306";
        String amount = "123.45";

        doReturn(false).when(landTransportBillValidation).isExceedDueDate(anyString(), any());

        assertDoesNotThrow(() -> landTransportBillValidation.checkDigit(ref1, ref2, amount));
    }

    @Test
    void testCheckDigit_WhenInvalid_ShouldThrowException() {
        String ref1 = "123";
        String ref2 = "987";
        String amount = "123.45";

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                landTransportBillValidation.checkDigit(ref1, ref2, amount));

        assertEquals(ResponseCode.INCORRECT_DLT_REF.getCode(), exception.getErrorCode());
    }

    @Test
    void testVerify_WhenValid_ShouldReturnTrue() throws TMBCommonException {
        String ref1 = "123456789"; // Length 9
        String ref2 = "987654306"; // Length 9 with last 2 digits as check digit
        String amount = "123.45";  // Properly formatted

        doReturn(false).when(landTransportBillValidation).isExceedDueDate(anyString(), any());

        boolean result = landTransportBillValidation.verify(ref1, ref2, amount);

        assertTrue(result);
    }

    @Test
    void testVerify_WhenInvalidCheckDigitResultNotEqualWithLast2DigitRef2_ShouldReturnFalse() throws TMBCommonException {
        String ref1 = "123456789"; // Length 9
        String ref2 = "987654329"; // Length 9 with last 2 digits as check digit
        String amount = "123.45";  // Properly formatted

        doReturn(false).when(landTransportBillValidation).isExceedDueDate(anyString(), any());

        boolean result = landTransportBillValidation.verify(ref1, ref2, amount);

        assertFalse(result);
    }

    @Test
    void testVerify_InvalidLength() throws TMBCommonException {
        String ref1 = "12345";
        String ref2 = "98765";
        String amount = "123.45";

        boolean result = landTransportBillValidation.verify(ref1, ref2, amount);

        assertFalse(result);
    }

    @Test
    void testVerify_InvalidDueDate() {
        doReturn(true).when(landTransportBillValidation).isExceedDueDate(anyString(), any());

        String ref1 = "123456789";
        String ref2 = "987654321";
        String amount = "123.45";

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                landTransportBillValidation.verify(ref1, ref2, amount));

        assertEquals(ResponseCode.INCORRECT_DLT_EXCEEDED_DUD_DATE.getCode(), exception.getErrorCode());
    }

    @Test
    void testVerify_WhenNumberFormatException__ShouldThrowTMBCommonException() {
        String ref1 = "123A56789"; // Contains non-numeric characters, invalid
        String ref2 = "987654306";
        String amount = "123.45";

        doReturn(false).when(landTransportBillValidation).isExceedDueDate(anyString(), any());

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                landTransportBillValidation.verify(ref1, ref2, amount));

        assertEquals(ResponseCode.INCORRECT_DLT_REF.getCode(), exception.getErrorCode());
    }

    @Test
    void testIsExceedDueDate_NotExceeded() throws ParseException {
        String ref2 = "123016789";

        Date fixedDate = new SimpleDateFormat("dd-MM-yyyy").parse("31-01-2023");

        boolean result = landTransportBillValidation.isExceedDueDate(ref2, fixedDate);

        assertFalse(result);
    }

    @Test
    void testIsExceedDueDate_Exceeded() {
        String ref2 = "1987654321";

        Date tomorrow = Date.from(Instant.now().plus(1, ChronoUnit.DAYS));

        boolean result = landTransportBillValidation.isExceedDueDate(ref2, tomorrow);

        assertTrue(result);
    }
}
