package com.tmb.oneapp.commonpaymentexp.validator;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.ConversionRateDetail;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.WowPoint;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.BillerInfoResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CardBalancesAsync;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.client.CommonAuthenticationService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.ServiceHoursUtils;
import com.tmb.oneapp.commonpaymentexp.validator.specialbiller.AIABillValidator;
import com.tmb.oneapp.commonpaymentexp.validator.specialbiller.LandTransportBillValidator;
import com.tmb.oneapp.commonpaymentexp.validator.specialbiller.ToyotaBillValidator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_AIA_0002;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_AIA_2095;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_LAND_TRANSPORT_0980;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_TOYOTA_0287;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_TOYOTA_0840;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_TOYOTA_2192;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_TOYOTA_2193;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class BaseBillPayValidatorTest {
    @InjectMocks
    private BaseBillPayValidator baseBillPayValidator;

    @Mock
    private AIABillValidator aiaBillValidator;
    @Mock
    private ToyotaBillValidator toyotaBillValidation;
    @Mock
    private LandTransportBillValidator landTransportBillValidation;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private CommonAuthenticationService commonAuthenticationService;


    @BeforeEach
    void setUp() {
    }

    @Nested
    class ValidateSpecialBillerOfflineTest {
        private String reference1;
        private String reference2;
        private BigDecimal amount;
        private MasterBillerResponse masterBillerResponse;

        @BeforeEach
        void setUp() {
            reference1 = "reference1";
            reference2 = "reference2";
            amount = BigDecimal.TEN;
            masterBillerResponse = new MasterBillerResponse();
        }

        @Test
        void testValidateSpecialBillerOffline_ShouldExecuteAiaBillValidation() throws TMBCommonException {
            for (String compCode : List.of(SPECIAL_OFFLINE_BILLER_AIA_0002, SPECIAL_OFFLINE_BILLER_AIA_2095)) {
                Assertions.assertDoesNotThrow(() -> baseBillPayValidator.validateSpecialBillerOffline(compCode, reference1, reference2, amount, masterBillerResponse));

                verify(aiaBillValidator, times(1)).checkDigit(compCode, reference1, masterBillerResponse);
            }
        }

        @Test
        void testValidateSpecialBillerOffline_ShouldExecuteToyotaBillValidation() throws TMBCommonException {
            List<String> specialOfflineBillerToyota = List.of(SPECIAL_OFFLINE_BILLER_TOYOTA_0287,
                    SPECIAL_OFFLINE_BILLER_TOYOTA_2193,
                    SPECIAL_OFFLINE_BILLER_TOYOTA_0840,
                    SPECIAL_OFFLINE_BILLER_TOYOTA_2192);

            for (String compCode : specialOfflineBillerToyota) {
                Assertions.assertDoesNotThrow(() -> baseBillPayValidator.validateSpecialBillerOffline(compCode, reference1, reference2, amount, masterBillerResponse));
            }

            verify(toyotaBillValidation, times(4)).checkDigit(reference1, reference2);
        }

        @Test
        void testValidateSpecialBillerOffline_ShouldExecuteLandTransportBillValidation() throws TMBCommonException {
            Assertions.assertDoesNotThrow(() -> baseBillPayValidator.validateSpecialBillerOffline(SPECIAL_OFFLINE_BILLER_LAND_TRANSPORT_0980, reference1, reference2, amount, masterBillerResponse));

            verify(landTransportBillValidation, times(1)).checkDigit(reference1, reference2, amount.toString());
        }

        @Test
        void testValidateSpecialBillerOffline_WhenCompCodeNotSpecialBiller_ShouldSkipExecuteValidation() throws TMBCommonException {
            String compCode = "not-special-biller";

            Assertions.assertDoesNotThrow(() -> baseBillPayValidator.validateSpecialBillerOffline(compCode, reference1, reference2, amount, masterBillerResponse));

            verify(landTransportBillValidation, never()).checkDigit(anyString(), anyString(), any());
            verify(toyotaBillValidation, never()).checkDigit(anyString(), anyString());
            verify(aiaBillValidator, never()).checkDigit(anyString(), anyString(), any());
        }
    }

    @Nested
    class ValidateServiceHoursTest {

        @Test
        void testValidateServiceHoursWhenWithinServiceHoursThenSuccess() {
            MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
            BillerInfoResponse billerInfo = new BillerInfoResponse();
            billerInfo.setStartTime("00:00");
            billerInfo.setEndTime("23:59");
            masterBillerResponse.setBillerInfo(billerInfo);

            try (MockedStatic<ServiceHoursUtils> mockedStatic = Mockito.mockStatic(ServiceHoursUtils.class)) {
                mockedStatic.when(() -> ServiceHoursUtils.isDuringServiceHours(any(Date.class), any()))
                        .thenReturn(true);

                assertDoesNotThrow(() -> baseBillPayValidator.validateServiceHours(masterBillerResponse));

                mockedStatic.verify(() -> ServiceHoursUtils.isDuringServiceHours(any(Date.class), any()));
            }
        }

        @Test
        void testValidateServiceHoursWhenMasterBillerInCacheWithinServiceHoursThenSuccess() {
            MasterBillerResponseInCache masterBillerResponse = new MasterBillerResponseInCache();
            BillerInfoResponseInCache billerInfo = new BillerInfoResponseInCache();
            billerInfo.setStartTime("00:00");
            billerInfo.setEndTime("23:59");
            masterBillerResponse.setBillerInfo(billerInfo);

            try (MockedStatic<ServiceHoursUtils> mockedStatic = Mockito.mockStatic(ServiceHoursUtils.class)) {
                mockedStatic.when(() -> ServiceHoursUtils.isDuringServiceHours(any(Date.class), any()))
                        .thenReturn(true);

                assertDoesNotThrow(() -> baseBillPayValidator.validateServiceHours(masterBillerResponse));

                mockedStatic.verify(() -> ServiceHoursUtils.isDuringServiceHours(any(Date.class), any()));
            }
        }

        @Test
        void testValidateServiceHoursWhenOutsideServiceHoursThenThrowException() {
            MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
            BillerInfoResponse billerInfo = new BillerInfoResponse();
            billerInfo.setStartTime("00:00");
            billerInfo.setEndTime("23:59");
            masterBillerResponse.setBillerInfo(billerInfo);

            TMBCommonException exception = assertThrows(TMBCommonException.class,
                    () -> baseBillPayValidator.validateServiceHours(masterBillerResponse));
            assertEquals(ResponseCode.UNAVAILABLE_SERVICE_HOUR.getCode(), exception.getErrorCode());
        }
    }

    @Test
    void testValidateBillerExpirationWhenBillerNotExpiredThenSuccess() {
        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        LocalDateTime futureDate = LocalDateTime.now().plusDays(1);
        String expiredDate = Date.from(futureDate.atZone(ZoneId.systemDefault()).toInstant()).toString();
        billerInfo.setExpiredDate(expiredDate);
        masterBillerResponse.setBillerInfo(billerInfo);

        try (MockedStatic<CommonServiceUtils> mockedCommonUtils = Mockito.mockStatic(CommonServiceUtils.class)) {
            Date mockFutureDate = Date.from(futureDate.atZone(ZoneId.systemDefault()).toInstant());
            mockedCommonUtils.when(() -> CommonServiceUtils.convertStringToDate(anyString()))
                    .thenReturn(mockFutureDate);

            assertDoesNotThrow(() -> baseBillPayValidator.validateBillerExpiration(masterBillerResponse));

            mockedCommonUtils.verify(() -> CommonServiceUtils.convertStringToDate(expiredDate));
        }
    }

    @Test
    void testValidateBillerExpirationWhenBillerExpiredThenThrowException() {
        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        LocalDateTime pastDate = LocalDateTime.now().minusDays(1);
        String expiredDate = Date.from(pastDate.atZone(ZoneId.systemDefault()).toInstant()).toString();
        billerInfo.setExpiredDate(expiredDate);
        masterBillerResponse.setBillerInfo(billerInfo);

        try (MockedStatic<CommonServiceUtils> mockedCommonUtils = Mockito.mockStatic(CommonServiceUtils.class)) {
            Date mockPastDate = Date.from(pastDate.atZone(ZoneId.systemDefault()).toInstant());
            mockedCommonUtils.when(() -> CommonServiceUtils.convertStringToDate(anyString()))
                    .thenReturn(mockPastDate);

            TMBCommonException mockException = new TMBCommonException(
                    ResponseCode.BILLER_EXPIRED.getCode(),
                    ResponseCode.BILLER_EXPIRED.getMessage(),
                    ResponseCode.BILLER_EXPIRED.getService(),
                    HttpStatus.OK,
                    null);
            mockedCommonUtils.when(() -> CommonServiceUtils.getBusinessTmbCommonException(
                            eq(ResponseCode.BILLER_EXPIRED)
                    ))
                    .thenReturn(mockException);

            TMBCommonException exception = assertThrows(TMBCommonException.class,
                    () -> baseBillPayValidator.validateBillerExpiration(masterBillerResponse));
            assertEquals(ResponseCode.BILLER_EXPIRED.getCode(), exception.getErrorCode());

            mockedCommonUtils.verify(() -> CommonServiceUtils.convertStringToDate(expiredDate));
            mockedCommonUtils.verify(() -> CommonServiceUtils.getBusinessTmbCommonException(
                    eq(ResponseCode.BILLER_EXPIRED)
            ));
        }
    }

    @Test
    void testValidateDailyLimitWhenPayingWithCreditCardThenDoNotValidate() throws TMBCommonException {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
        creditCard.setPayWithCreditCardFlag(true);
        request.setCreditCard(creditCard);

        baseBillPayValidator.validateDailyLimit(request, new MasterBillerResponse(), new CustomerCrmProfile());

        verify(dailyLimitService, never()).validateDailyLimitExceeded(any(), any(), any());
    }

    @Test
    void testValidateDailyLimitWhenPayingWithDepositThenValidate() throws TMBCommonException {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
        creditCard.setPayWithCreditCardFlag(false);
        request.setCreditCard(creditCard);

        DepositValidationCommonPaymentRequest deposit = new DepositValidationCommonPaymentRequest();
        deposit.setAmount(BigDecimal.TEN);
        request.setDeposit(deposit);

        MasterBillerResponse masterBiller = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setBillerGroupType("0");
        masterBiller.setBillerInfo(billerInfo);
        CustomerCrmProfile customerProfile = new CustomerCrmProfile();

        doNothing().when(dailyLimitService).validateDailyLimitExceeded(any(), any(), any());

        baseBillPayValidator.validateDailyLimit(request, masterBiller, customerProfile);


        verify(dailyLimitService).validateDailyLimitExceeded(masterBiller.getBillerInfo().getBillerGroupType(), customerProfile, BigDecimal.TEN);
    }

    @Nested
    class ValidateInsufficientFund {
        @Test
        void testValidateInsufficientFundWhenPayingWithCreditCardAndSufficientFundsThenSuccess() {
            ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
            CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
            creditCard.setPayWithCreditCardFlag(true);
            creditCard.setAmount(BigDecimal.TEN);
            request.setCreditCard(creditCard);

            CreditCardSupplementary creditCardDetail = new CreditCardSupplementary();
            CardBalancesAsync cardBalances = new CardBalancesAsync();
            cardBalances.setAvailableCreditAllowance(BigDecimal.valueOf(100));
            creditCardDetail.setCardBalances(cardBalances);

            assertDoesNotThrow(() -> baseBillPayValidator.validateInsufficientFund(
                    request, creditCardDetail, null, BigDecimal.ONE));
        }

        @Test
        void testValidateInsufficientFundWhenPayingWithCreditCardAndInsufficientFundsThenThrowException() {
            ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
            CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
            creditCard.setPayWithCreditCardFlag(true);
            creditCard.setAmount(BigDecimal.valueOf(100));
            request.setCreditCard(creditCard);

            CreditCardSupplementary creditCardDetail = new CreditCardSupplementary();
            CardBalancesAsync cardBalances = new CardBalancesAsync();
            cardBalances.setAvailableCreditAllowance(BigDecimal.TEN);
            creditCardDetail.setCardBalances(cardBalances);

            TMBCommonException exception = assertThrows(TMBCommonException.class,
                    () -> baseBillPayValidator.validateInsufficientFund(
                            request, creditCardDetail, null, BigDecimal.ONE));
            assertEquals(ResponseCode.CREDIT_CARD_INSUFFICIENT_FUNDS_ERROR.getCode(), exception.getErrorCode());
        }

        @Test
        void testValidateInsufficientFundWhenPayingWithDepositAndSufficientFundsThenSuccess() {
            ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
            request.setDeposit(new DepositValidationCommonPaymentRequest().setPayWithDepositFlag(true).setAmount(BigDecimal.TEN));
            DepositAccount fromDepositAccount = new DepositAccount().setAvailableBalance(BigDecimal.valueOf(100));

            assertDoesNotThrow(() -> baseBillPayValidator.validateInsufficientFund(
                    request, fromDepositAccount, BigDecimal.ONE));
        }

        @Test
        void testValidateInsufficientFundWhenPayingWithDepositAndInsufficientFundsThenThrowException() {
            ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
            request.setDeposit(new DepositValidationCommonPaymentRequest().setPayWithDepositFlag(true).setAmount(BigDecimal.TEN));
            DepositAccount fromDepositAccount = new DepositAccount().setAvailableBalance(BigDecimal.TEN);

            TMBCommonException exception = assertThrows(TMBCommonException.class,
                    () -> baseBillPayValidator.validateInsufficientFund(request, fromDepositAccount, BigDecimal.ONE));

            assertEquals(ResponseCode.DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR.getCode(), exception.getErrorCode());
        }
    }

    @Test
    void testValidateCompCodeExclusionWhenCompCodeNotExcludedThenSuccess() {
        BillPayConfiguration configData = new BillPayConfiguration();
        configData.setBillerExcludeList(Arrays.asList("CODE1", "CODE2"));

        assertDoesNotThrow(() -> baseBillPayValidator.validateCompCodeExclusion(configData, "CODE3"));
    }

    @Test
    void testValidateCompCodeExclusionWhenCompCodeExcludedThenThrowException() {
        BillPayConfiguration configData = new BillPayConfiguration();
        configData.setBillerExcludeList(Arrays.asList("CODE1", "CODE2"));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> baseBillPayValidator.validateCompCodeExclusion(configData, "CODE1"));
        assertEquals(ResponseCode.EXCLUDE_BILLER_ERROR.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidateInsufficientFundWhenPayingWithDepositAndSufficientFundsThenSuccess() {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
        creditCard.setPayWithCreditCardFlag(false);
        request.setCreditCard(creditCard);

        DepositValidationCommonPaymentRequest deposit = new DepositValidationCommonPaymentRequest();
        deposit.setAmount(BigDecimal.TEN);
        request.setDeposit(deposit);

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAvailableBalance(BigDecimal.valueOf(100));

        assertDoesNotThrow(() -> baseBillPayValidator.validateInsufficientFund(
                request, null, depositAccount, BigDecimal.ONE));
    }

    @Test
    void testValidateInsufficientFundWhenPayingWithDepositAndInsufficientFundsThenThrowException() {
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
        creditCard.setPayWithCreditCardFlag(false);
        request.setCreditCard(creditCard);

        DepositValidationCommonPaymentRequest deposit = new DepositValidationCommonPaymentRequest();
        deposit.setAmount(BigDecimal.valueOf(100));
        request.setDeposit(deposit);

        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAvailableBalance(BigDecimal.TEN);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> baseBillPayValidator.validateInsufficientFund(
                        request, null, depositAccount, BigDecimal.ONE));
        assertEquals(ResponseCode.DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR.getCode(), exception.getErrorCode());
    }

    @Nested
    class ValidateWowPointTest {

        @Test
        void testValidateWowPoint_WhenSuccess_ShouldDoesNotThrow() {
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache()
                    .setWowPointFlag(true)
                    .setWowPoint(new WowPoint()
                            .setMinPaymentAmount(new BigDecimal(100))
                            .setMin(new BigDecimal(10))
                            .setMax(new BigDecimal(10000))
                            .setConversionRate(new ConversionRateDetail(new BigDecimal(2), new BigDecimal(15))));

            ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest()
                    .setDeposit(new DepositValidationCommonPaymentRequest().setAmount(new BigDecimal("9500.50")))
                    .setWowPoint(new WowPointValidationCommonPaymentRequest()
                            .setWowPointAmount(new BigDecimal(1500))
                            .setDiscountAmount(new BigDecimal(200))
                            .setTxnAmount(new BigDecimal("9300.50")));

            assertDoesNotThrow(() -> baseBillPayValidator.validateWowPoint(request, new CommonPaymentDraftCache().setCommonPaymentRule(commonPaymentRule)));
        }

        @Test
        void testValidateWowPoint_WhenFailedInvalidMinPaymentAmount_ShouldThrowTMBCommonException() {
            BigDecimal minAmount = new BigDecimal("101.00");
            BigDecimal discountAmount = new BigDecimal(100);
            BigDecimal minWowPointPaymentAmount = new BigDecimal(10);

            var invalidWowPoint = new WowPointValidationCommonPaymentRequest()
                    .setWowPointAmount(new BigDecimal(1000))
                    .setDiscountAmount(discountAmount);

            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache()
                    .setWowPointFlag(true)
                    .setWowPoint(new WowPoint()
                            .setMinPaymentAmount(minWowPointPaymentAmount)
                            .setMin(new BigDecimal(10))
                            .setMax(new BigDecimal(100))
                            .setConversionRate(new ConversionRateDetail(new BigDecimal(1), new BigDecimal(10))));

            ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest()
                    .setDeposit(new DepositValidationCommonPaymentRequest().setAmount(minAmount))
                    .setWowPoint(invalidWowPoint);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> baseBillPayValidator.validateWowPoint(request, new CommonPaymentDraftCache().setCommonPaymentRule(commonPaymentRule)));

            assertEquals(ResponseCode.INVALID_WOW_POINT.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.OK, exception.getStatus());
        }

        @Test
        void testValidateWowPoint_WhenFailedWowPointGreaterThanMaximum_ShouldThrowTMBCommonException() {
            var invalidWowPoint = new WowPointValidationCommonPaymentRequest()
                    .setWowPointAmount(new BigDecimal(1000))
                    .setDiscountAmount(new BigDecimal(100));

            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache()
                    .setWowPointFlag(true)
                    .setWowPoint(new WowPoint()
                            .setMinPaymentAmount(new BigDecimal(100))
                            .setMin(new BigDecimal(10))
                            .setMax(new BigDecimal(10))
                            .setConversionRate(new ConversionRateDetail(new BigDecimal(1), new BigDecimal(10))));

            ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest()
                    .setDeposit(new DepositValidationCommonPaymentRequest().setAmount(new BigDecimal("9500.50")))
                    .setWowPoint(invalidWowPoint);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> baseBillPayValidator.validateWowPoint(request, new CommonPaymentDraftCache().setCommonPaymentRule(commonPaymentRule)));

            assertEquals(ResponseCode.INVALID_WOW_POINT.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.OK, exception.getStatus());
        }

        @Test
        void testValidateWowPoint_WhenFailedWowPointLowerThanMinimum_ShouldThrowTMBCommonException() {
            WowPointValidationCommonPaymentRequest invalidWowPoint = new WowPointValidationCommonPaymentRequest()
                    .setWowPointAmount(new BigDecimal(1))
                    .setDiscountAmount(new BigDecimal(100));

            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(new WowPoint()
                    .setMinPaymentAmount(new BigDecimal(100))
                    .setMin(new BigDecimal(10))
                    .setMax(new BigDecimal(100))
                    .setConversionRate(new ConversionRateDetail(new BigDecimal(1), new BigDecimal(10))));

            ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest()
                    .setDeposit(new DepositValidationCommonPaymentRequest().setAmount(new BigDecimal("9500.50")))
                    .setWowPoint(invalidWowPoint);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> baseBillPayValidator.validateWowPoint(request, new CommonPaymentDraftCache().setCommonPaymentRule(commonPaymentRule)));

            assertEquals(ResponseCode.INVALID_WOW_POINT.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.OK, exception.getStatus());
        }

        @Test
        void testValidateWowPoint_WhenFailedWowPointConversionIsNotCorrect_ShouldThrowTMBCommonException() {
            WowPointValidationCommonPaymentRequest invalidWowPoint = new WowPointValidationCommonPaymentRequest()
                    .setWowPointAmount(new BigDecimal(100))
                    .setDiscountAmount(new BigDecimal(100));

            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(new WowPoint()
                    .setMinPaymentAmount(new BigDecimal(100))
                    .setMin(new BigDecimal(10))
                    .setMax(new BigDecimal(10000))
                    .setConversionRate(new ConversionRateDetail(new BigDecimal(1), new BigDecimal(10))));

            ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest()
                    .setDeposit(new DepositValidationCommonPaymentRequest().setAmount(new BigDecimal("9500.50")))
                    .setWowPoint(invalidWowPoint);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> baseBillPayValidator.validateWowPoint(request, new CommonPaymentDraftCache().setCommonPaymentRule(commonPaymentRule)));

            assertEquals(ResponseCode.INVALID_WOW_POINT.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.OK, exception.getStatus());
        }

        @Test
        void testValidateWowPoint_WhenFailedTnxAmountIsNotCorrect_ShouldThrowTMBCommonException() {
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache()
                    .setWowPointFlag(true)
                    .setWowPoint(new WowPoint()
                            .setMinPaymentAmount(new BigDecimal(100))
                            .setMin(new BigDecimal(10))
                            .setMax(new BigDecimal(10000))
                            .setConversionRate(new ConversionRateDetail(new BigDecimal(2), new BigDecimal(15))));

            ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest()
                    .setDeposit(new DepositValidationCommonPaymentRequest().setAmount(new BigDecimal("9500.50")))
                    .setWowPoint(new WowPointValidationCommonPaymentRequest()
                            .setWowPointAmount(new BigDecimal(1500))
                            .setDiscountAmount(new BigDecimal(200))
                            .setTxnAmount(new BigDecimal("9333.00")));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> baseBillPayValidator.validateWowPoint(request, new CommonPaymentDraftCache().setCommonPaymentRule(commonPaymentRule)));

            assertEquals(ResponseCode.INVALID_WOW_POINT.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.OK, exception.getStatus());
        }
    }

    @Test
    void testValidatePostAuthenticateTransaction_WhenNotFoundCache_ShouldDoesNotThrow() throws TMBCommonException {
        Mockito.when(commonAuthenticationService.getCacheDataForValidatePostAuthentication(anyString(), anyString())).thenReturn(null);

        assertDoesNotThrow(() -> baseBillPayValidator.validatePostAuthenticateTransaction("x-cor", "transactionId"));
    }

    @Test
    void testValidatePostAuthenticateTransaction_WhenFoundTransactionCache_ShouldThrowTmbException() throws TMBCommonException {
        Mockito.when(commonAuthenticationService.getCacheDataForValidatePostAuthentication(anyString(), anyString())).thenReturn("true");

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> baseBillPayValidator.validatePostAuthenticateTransaction("x-cor", "transactionId"));

        assertEquals("030039", exception.getErrorCode());
    }
}