package com.tmb.oneapp.commonpaymentexp.validator.specialbiller;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
class AIABillValidatorTest {
    @InjectMocks
    AIABillValidator aiaBillValidator;

    MasterBillerResponse masterBillerResponse;

    @BeforeEach
    void setUp() {
        masterBillerResponse = new MasterBillerResponse();
        masterBillerResponse.setRef1(new ReferenceResponse().setLabelEn("Reference 1"));
    }

    @Test
    void testCheckDigit_ValidRef1Pass() throws TMBCommonException {
        // Valid ref1 for "0002" prefix
        String billerCompCode = "0002";
        String validRef1 = "1234567898";

        assertDoesNotThrow(() -> aiaBillValidator.checkDigit(billerCompCode, validRef1, masterBillerResponse));

    }

    @Test
    void testCheckDigit_InvalidRef1ThrowsException() {
        String billerCompCode = "0002";
        String invalidRef1 = "1234567890"; // Invalid checksum

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> {
            aiaBillValidator.checkDigit(billerCompCode, invalidRef1, masterBillerResponse);
        });
    }

    @Test
    void testCheckDigit_BlankRef1ThrowsException() {
        String billerCompCode = "0002";
        String blankRef1 = "";

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> {
            aiaBillValidator.checkDigit(billerCompCode, blankRef1, masterBillerResponse);
        });
    }

    @Test
    void testCheckDigit_InvalidPrefixThrowsException() {
        String billerCompCode = "2095";
        String invalidRef1 = "0234567890"; // Prefix "0" is not valid for 2095

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> {
            aiaBillValidator.checkDigit(billerCompCode, invalidRef1, masterBillerResponse);
        });
    }

    @Test
    void testVerify_SpecialCaseRef1Pass() {
        // Ref1 starts with '9' and is <= 9002000000L
        String ref1 = "9002000000";

        boolean result = aiaBillValidator.verify(List.of("9"), ref1);

        assertTrue(result);
    }

    @Test
    void testVerify_Ref1WithRef1ModZeroAndCorrectCheckDigitPass() {
        // Ref1: checksum is divisible by 10 and check digit is 0
        String ref1 = "1235567800";

        boolean result = aiaBillValidator.verify(List.of("1"), ref1);

        assertTrue(result);
    }


    @Test
    void testVerify_Ref1WithCorrectChecksumPass() {
        // Ref1: checksum calculation should pass
        String ref1 = "1234567898";

        boolean result = aiaBillValidator.verify(List.of("1"), ref1);

        assertTrue(result);
    }

    @Test
    void testVerify_Ref1WithIncorrectChecksumFails() {
        // Ref1: checksum calculation should fail
        String ref1 = "1234567890";

        boolean result = aiaBillValidator.verify(List.of("1"), ref1);

        assertFalse(result);
    }

    @Test
    void testVerify_Ref1BlankFails() {
        // Blank ref1 should fail
        String ref1 = "";

        boolean result = aiaBillValidator.verify(List.of("1"), ref1);

        assertFalse(result);
    }

    @Test
    void testVerify_Ref1InvalidLengthFails() {
        // Ref1 with incorrect length should fail
        String ref1 = "12345";

        boolean result = aiaBillValidator.verify(List.of("1"), ref1);

        assertFalse(result);
    }

    @Test
    void testVerify_NumberFormatExceptionFails() {
        // Invalid numeric values should fail
        String ref1 = "12345X7890";

        boolean result = aiaBillValidator.verify(List.of("1"), ref1);

        assertFalse(result);
    }
}