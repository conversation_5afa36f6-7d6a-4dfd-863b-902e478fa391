package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment.InitializationCommonPaymentPartnerService;
import com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment.InitializationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment.InitializationServiceFactory;
import com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment.PartnerInitializationValidatorImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.util.Base64;

import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class InitializationCommonPaymentPartnerServiceTest {
    InitializationCommonPaymentPartnerService partnerInitializationService;

    @Mock
    InitializationCommonPaymentService initializationCommonPaymentService;
    @Mock
    InitializationServiceFactory factory;
    @Mock
    PartnerInitializationValidatorImpl partnerInitializationValidator;

    HttpHeaders headers;
    String correlationId;
    InitializationCommonPaymentRequest request;

    @BeforeEach
    void setUp() throws TMBCommonException {
        when(factory.create(partnerInitializationValidator)).thenReturn(initializationCommonPaymentService);
        partnerInitializationService = new InitializationCommonPaymentPartnerService(factory, partnerInitializationValidator, "https://www.ttbbank.com/th/deeplink-suite/redirect", "https://touch.ttbdirect.com/linkaccess/commonpayment");

        correlationId = "test-correlation-id";

        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        request = initialCommonPaymentRequest();
    }

    @Test
    void testInitialCommonPayment_WhenSuccess_ShouldDoesNotThrowException() throws TMBCommonException {
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(new InitializationCommonPaymentResponse());

        assertDoesNotThrow(() -> partnerInitializationService.initialCommonPayment(request, headers));
    }

    @Test
    void testInitialCommonPayment_WhenFailedInitializationCommonPaymentGotException_ShouldThrowTMBCommonException() throws TMBCommonException {
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenThrow(TMBCommonException.class);

        assertThrows(TMBCommonException.class, () -> partnerInitializationService.initialCommonPayment(request, headers));
    }

    @Test
    void testInitialCommonPayment_WhenResponseIsNull_ShouldReturnNull() throws TMBCommonException {
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(null);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNull(result);
    }

    @Test
    void testInitialCommonPayment_WhenResponseHasNoDeeplink_ShouldReturnOriginalResponse() throws TMBCommonException {
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        assertNull(result.getDeeplinkUrl());
    }

    @Test
    void testInitialCommonPayment_WhenResponseHasEmptyDeeplink_ShouldReturnOriginalResponse() throws TMBCommonException {
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        originalResponse.setDeeplinkUrl("");
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        assertEquals("", result.getDeeplinkUrl());
    }

    @Test
    void testInitialCommonPayment_WhenResponseHasValidDeeplinkAndInAppWebviewTrue_ShouldWrapDeeplinkWithChannelParameter() throws TMBCommonException {
        String originalDeeplink = "oneapp://payment/confirm?transactionId=123";
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        originalResponse.setDeeplinkUrl(originalDeeplink);
        headers.add(CommonPaymentExpConstant.HEADER_X_IN_APP_WEBVIEW, "true");
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        
        String expectedDeeplinkWithChannel = "oneapp://payment/confirm?transactionId=123&channel=";
        String expectedBase64 = Base64.getEncoder().encodeToString(expectedDeeplinkWithChannel.getBytes());
        // UriComponentsBuilder URL-encodes query parameter values, so = becomes %3D
        String expectedWrappedDeeplink = "https://www.ttbbank.com/th/deeplink-suite/redirect?app=" + expectedBase64.replace("=", "%3D");
        assertEquals(expectedWrappedDeeplink, result.getDeeplinkUrl());
    }

    @Test
    void testInitialCommonPayment_WhenResponseHasValidDeeplinkWithoutQueryParamsAndInAppWebviewTrue_ShouldAddChannelParameter() throws TMBCommonException {
        String originalDeeplink = "oneapp://payment/confirm";
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        originalResponse.setDeeplinkUrl(originalDeeplink);
        headers.add(CommonPaymentExpConstant.HEADER_X_IN_APP_WEBVIEW, "true");
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        
        String expectedDeeplinkWithChannel = "oneapp://payment/confirm?channel=";
        String expectedBase64 = Base64.getEncoder().encodeToString(expectedDeeplinkWithChannel.getBytes());
        // UriComponentsBuilder URL-encodes query parameter values, so = becomes %3D
        String expectedWrappedDeeplink = "https://www.ttbbank.com/th/deeplink-suite/redirect?app=" + expectedBase64.replace("=", "%3D");
        assertEquals(expectedWrappedDeeplink, result.getDeeplinkUrl());
    }

    @Test
    void testInitialCommonPayment_WhenResponseHasWhitespaceOnlyDeeplink_ShouldReturnOriginalResponse() throws TMBCommonException {
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        originalResponse.setDeeplinkUrl("   ");
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        assertEquals("   ", result.getDeeplinkUrl());
    }

    @Test
    void testInitialCommonPayment_WhenInAppWebviewFalse_ShouldReturnUniversalLink() throws TMBCommonException {
        String originalDeeplink = "oneapp://payment/confirm?transaction_id=12345";
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        originalResponse.setDeeplinkUrl(originalDeeplink);
        headers.add(CommonPaymentExpConstant.HEADER_X_IN_APP_WEBVIEW, "false");
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        assertEquals("https://touch.ttbdirect.com/linkaccess/commonpayment?transaction_id=12345", result.getDeeplinkUrl());
    }

    @Test
    void testInitialCommonPayment_WhenInAppWebviewMissing_ShouldReturnUniversalLink() throws TMBCommonException {
        String originalDeeplink = "oneapp://payment/confirm?transaction_id=67890";
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        originalResponse.setDeeplinkUrl(originalDeeplink);
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        assertEquals("https://touch.ttbdirect.com/linkaccess/commonpayment?transaction_id=67890", result.getDeeplinkUrl());
    }

    @Test
    void testInitialCommonPayment_WhenPartnerNameProvided_ShouldUsePartnerNameAsChannel() throws TMBCommonException {
        String originalDeeplink = "oneapp://payment/confirm?transactionId=123";
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        originalResponse.setDeeplinkUrl(originalDeeplink);
        headers.add(CommonPaymentExpConstant.HEADER_X_IN_APP_WEBVIEW, "true");
        headers.add(CommonPaymentExpConstant.HEADER_X_PARTNER_NAME, "shopee");
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        
        String expectedDeeplinkWithChannel = "oneapp://payment/confirm?transactionId=123&channel=shopee";
        String expectedBase64 = Base64.getEncoder().encodeToString(expectedDeeplinkWithChannel.getBytes());
        String expectedWrappedDeeplink = "https://www.ttbbank.com/th/deeplink-suite/redirect?app=" + expectedBase64.replace("=", "%3D");
        assertEquals(expectedWrappedDeeplink, result.getDeeplinkUrl());
    }

    @Test
    void testInitialCommonPayment_WhenPartnerNameIsNull_ShouldUseEmptyStringAsChannel() throws TMBCommonException {
        String originalDeeplink = "oneapp://payment/confirm?transactionId=123";
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        originalResponse.setDeeplinkUrl(originalDeeplink);
        headers.add(CommonPaymentExpConstant.HEADER_X_IN_APP_WEBVIEW, "true");
        headers.add(CommonPaymentExpConstant.HEADER_X_PARTNER_NAME, null);
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        
        String expectedDeeplinkWithChannel = "oneapp://payment/confirm?transactionId=123&channel=";
        String expectedBase64 = Base64.getEncoder().encodeToString(expectedDeeplinkWithChannel.getBytes());
        String expectedWrappedDeeplink = "https://www.ttbbank.com/th/deeplink-suite/redirect?app=" + expectedBase64.replace("=", "%3D");
        assertEquals(expectedWrappedDeeplink, result.getDeeplinkUrl());
    }

    @Test
    void testInitialCommonPayment_WhenPartnerNameIsEmpty_ShouldUseEmptyStringAsChannel() throws TMBCommonException {
        String originalDeeplink = "oneapp://payment/confirm?transactionId=123";
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        originalResponse.setDeeplinkUrl(originalDeeplink);
        headers.add(CommonPaymentExpConstant.HEADER_X_IN_APP_WEBVIEW, "true");
        headers.add(CommonPaymentExpConstant.HEADER_X_PARTNER_NAME, "");
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        
        String expectedDeeplinkWithChannel = "oneapp://payment/confirm?transactionId=123&channel=";
        String expectedBase64 = Base64.getEncoder().encodeToString(expectedDeeplinkWithChannel.getBytes());
        String expectedWrappedDeeplink = "https://www.ttbbank.com/th/deeplink-suite/redirect?app=" + expectedBase64.replace("=", "%3D");
        assertEquals(expectedWrappedDeeplink, result.getDeeplinkUrl());
    }

    @Test
    void testInitialCommonPayment_WhenDeeplinkHasNoTransactionId_ShouldReturnUniversalLinkWithEmptyTransactionId() throws TMBCommonException {
        String originalDeeplink = "oneapp://payment/confirm?someOtherParam=value";
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        originalResponse.setDeeplinkUrl(originalDeeplink);
        headers.add(CommonPaymentExpConstant.HEADER_X_IN_APP_WEBVIEW, "false");
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        assertEquals("https://touch.ttbdirect.com/linkaccess/commonpayment?transaction_id=", result.getDeeplinkUrl());
    }

    @Test
    void testInitialCommonPayment_WhenInvalidDeeplinkFormat_ShouldReturnUniversalLinkWithEmptyTransactionId() throws TMBCommonException {
        String originalDeeplink = "invalid-url-format";
        InitializationCommonPaymentResponse originalResponse = new InitializationCommonPaymentResponse();
        originalResponse.setDeeplinkUrl(originalDeeplink);
        headers.add(CommonPaymentExpConstant.HEADER_X_IN_APP_WEBVIEW, "false");
        when(initializationCommonPaymentService.initialCommonPayment(request, headers)).thenReturn(originalResponse);

        InitializationCommonPaymentResponse result = partnerInitializationService.initialCommonPayment(request, headers);

        assertNotNull(result);
        assertEquals("https://touch.ttbdirect.com/linkaccess/commonpayment?transaction_id=", result.getDeeplinkUrl());
    }

    private InitializationCommonPaymentRequest initialCommonPaymentRequest() {
        return new InitializationCommonPaymentRequest()
                .setPaymentInformation(new PaymentInformation()
                        .setEntryId("entry-id")
                        .setCompCode("comp-code")
                        .setTransactionType(COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY)
                        .setProductDetail(new ProductDetail()
                                .setProductRef1("ref1")
                                .setProductRef2("ref2"))
                        .setAmountDetail(new AmountDetail()
                                .setAmountValue(new BigDecimal("1000.00")))
                );
    }
}