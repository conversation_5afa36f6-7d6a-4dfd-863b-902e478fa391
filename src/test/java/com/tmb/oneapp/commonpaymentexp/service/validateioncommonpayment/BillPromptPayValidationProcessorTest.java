package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.cache.Transaction;
import com.tmb.common.cache.service.TransactionServices;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.BillerCreditcardMerchant;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ValidChannel;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CompleteScreenDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PhraseDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Sender;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.EDonationValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.CATEGORY_E_DONATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_BILL_PROMPT_PAY;
import static com.tmb.oneapp.commonpaymentexp.constant.PromptPayETEConstant.PROMPTPAY_REF_SEQ;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY;
import static com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.BillPromptPayValidationProcessor.BILLER_CHANNEL_97;
import static com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.BillPromptPayValidationProcessor.THAI_NATION_ID_TYPE;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BillPromptPayValidationProcessorTest {
    @InjectMocks
    private BillPromptPayValidationProcessor billPromptPayValidationProcessor;

    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private PaymentService paymentService;
    @Mock
    private CustomerService customerService;
    @Mock
    private BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private CacheService cacheService;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    @Mock
    private BaseBillPayValidator baseBillPayValidator;
    @Mock
    private TransactionServices transactionServices;


    private String crmId;
    private String correlationId;
    private HttpHeaders headers;
    private CustomerCrmProfile customerCrmProfile;
    private ValidationCommonPaymentRequest request;
    private CommonPaymentDraftCache draftCache;
    private CommonPaymentConfig commonPaymentConfig;
    private String compCode;
    private String depositAccountNumber;
    private MasterBillerResponse masterBillerResponse;
    private CustomerKYCResponse customerKYCResponse;


    @BeforeEach
    void setUp() throws TMBCommonException {
        TestUtils.setUpAsyncHelperExecuteMethodAsync(asyncHelper);
        Transaction.setTransactionServices(transactionServices);
        ReflectionTestUtils.setField(billPromptPayValidationProcessor, "amloAmountValidate", new BigDecimal("700000"));
        ReflectionTestUtils.setField(billPromptPayValidationProcessor, "isQRISO20022FlagOn", false);

        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        compCode = "***************";
        depositAccountNumber = "**********";
        commonPaymentConfig = new CommonPaymentConfig();
        masterBillerResponse = setUpMasterBillerDefault();
        customerCrmProfile = new CustomerCrmProfile();
        customerKYCResponse = new CustomerKYCResponse();

        draftCache = initialCacheDataForBillPayTransaction();
        request = initialDefaultRequest();
    }

    @Test
    void testGetProcessorType_ShouldReturnBillPromptPay() {
        assertEquals(BILLER_PAYMENT_BILL_PROMPT_PAY, billPromptPayValidationProcessor.getProcessorType());
    }

    @Nested
    class ExecutePromptPayValidationTest {

        @Test
        void testExecuteValidateBillPromptPay_WhenNormalPromptPayTransaction_ShouldSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, JsonProcessingException, TMBCommonExceptionWithResponse {
            mockFetchMasterBillerByCompCode(masterBillerResponse);
            mockFetchCustomerCrmProfile(customerCrmProfile);
            mockGetAccountByAccountNumber();
            mockFetchBillPayConfig();
            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();
            mockStaticTransactionGetSequenceId();
            mockValidatePromptPayPayment();

            ValidationCommonPaymentResponse actual = billPromptPayValidationProcessor.executeValidate(request, headers, draftCache);

            assertNotNull(actual.getFee());
            assertNotNull(actual.getTransactionId());
            assertNotNull(actual.getTotalAmount());
            Assertions.assertTrue(actual.getIsRequireCommonAuthen());
            Assertions.assertNull(actual.getCitizenId());
            assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
            assertEquals(COMMON_AUTH_BILL_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
            assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());

            assertCallValidateCompCodeExclusion();
            assertCallValidateServiceHours();
            assertCallValidateBillerExpiration();
            assertCallValidateDailyLimitExceeded();
            assertWriteActivityLog();
            assertSaveCache();
        }

        @Test
        void testExecuteValidateBillPromptPay_WhenEDonationTransactionAndAllowToShareRDAndThaiNation_ShouldSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse {
            String thaiNationType = THAI_NATION_ID_TYPE;
            String transactionEDonation = CATEGORY_E_DONATION_ID;
            var allowShareToRd = new EDonationValidationCommonPaymentRequest().setAllowShareToRdFlag(true);
            masterBillerResponse.getBillerInfo().setBillerCategoryCode(transactionEDonation);
            customerKYCResponse.setIdType(thaiNationType);
            request.setEDonation(allowShareToRd);
            mockFetchMasterBillerByCompCode(masterBillerResponse);
            mockFetchCustomerCrmProfile(customerCrmProfile);
            mockGetAccountByAccountNumber();
            mockFetchBillPayConfig();
            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();
            mockStaticTransactionGetSequenceId();
            mockValidatePromptPayPayment();
            mockGetCustomerKYC(customerKYCResponse);

            ValidationCommonPaymentResponse actual = billPromptPayValidationProcessor.executeValidate(request, headers, draftCache);

            assertNull(actual.getCitizenId());
            verify(customerService, times(1)).getCustomerKYC(correlationId, crmId);
        }

        @Test
        void testExecuteValidateBillPromptPay_WhenEDonationTransactionAndAllowToShareRDAndThaiNationAndAmountExceedAmloThreshold_ShouldReturnCitizenIdSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse {
            BigDecimal amountExceedAmloThreshold = new BigDecimal("700001");
            String thaiNationType = THAI_NATION_ID_TYPE;
            String transactionEDonation = CATEGORY_E_DONATION_ID;
            var allowShareToRd = new EDonationValidationCommonPaymentRequest().setAllowShareToRdFlag(true);
            masterBillerResponse.getBillerInfo().setBillerCategoryCode(transactionEDonation);
            customerKYCResponse.setIdType(thaiNationType);
            request.setEDonation(allowShareToRd);
            request.getDeposit().setAmount(amountExceedAmloThreshold);
            mockFetchMasterBillerByCompCode(masterBillerResponse);
            mockFetchCustomerCrmProfile(customerCrmProfile);
            mockGetAccountByAccountNumber();
            mockFetchBillPayConfig();
            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();
            mockStaticTransactionGetSequenceId();
            mockValidatePromptPayPayment();
            mockGetCustomerKYC(customerKYCResponse);

            ValidationCommonPaymentResponse actual = billPromptPayValidationProcessor.executeValidate(request, headers, draftCache);

            assertNotNull(actual.getCitizenId());
            assertEquals("X-XXXX-XXXX0-12-3", actual.getCitizenId());
            verify(customerService, times(1)).getCustomerKYC(correlationId, crmId);
        }

        @Test
        void testExecuteValidateBillPromptPay_WhenFailedTMBCommonExceptionAtPrePareDateExecuteMethodAsync_ShouldThrowTMBCommonException() throws TMBCommonException {
            TMBCommonException expectedException = new TMBCommonException(ResponseCode.FAILED_V2.getCode(), "message", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);
            when(paymentService.getMasterBiller(correlationId, compCode)).thenThrow(expectedException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> billPromptPayValidationProcessor.executeValidate(request, headers, draftCache));

            assertEquals(expectedException, exception);
            assertWriteActivityLog();
        }

        @Test
        void testExecuteValidateBillPromptPay_WhenFailedNullPointerExceptionAtPrePareDateExecuteMethodAsync_ShouldThrowTMBCommonException() throws TMBCommonException {
            when(paymentService.getMasterBiller(correlationId, compCode)).thenThrow(new NullPointerException(""));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> billPromptPayValidationProcessor.executeValidate(request, headers, draftCache));

            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
        }

        @Test
        void testExecuteValidateBillPromptPay_WhenIsQRISO20022FlagOnAndHasValidChannel_ShouldCallToETEISO2022Success() throws TMBCommonException, TMBCommonExceptionWithResponse {
            boolean isQRISO2022TurnOn = true;
            ValidChannel validChannelHas97 = new ValidChannel().setChannelCode(BILLER_CHANNEL_97);
            ReflectionTestUtils.setField(billPromptPayValidationProcessor, "isQRISO20022FlagOn", isQRISO2022TurnOn);
            masterBillerResponse.getBillerInfo().setValidChannels(List.of(validChannelHas97));
            mockFetchMasterBillerByCompCode(masterBillerResponse);
            mockFetchCustomerCrmProfile(customerCrmProfile);
            mockGetAccountByAccountNumber();
            mockFetchBillPayConfig();
            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();
            mockStaticTransactionGetSequenceId();
            mockValidatePromptPayISO2022Payment();

            assertDoesNotThrow(() -> billPromptPayValidationProcessor.executeValidate(request, headers, draftCache));

            verify(paymentService, times(1)).validateBillPromptPayISO20022Payment(eq(correlationId), eq(crmId), any(PromptPayETEValidateRequest.class));
        }
    }

    @Nested
    class ValidateDataPromptPayPaymentTest {
        @ParameterizedTest
        @CsvSource(value = {
                "ignore,ignore, true",
                "97,57, false",
                "null,57, false",
                "null,null, false",
                "ignore,ignore, false",
        }, delimiterString = ",", nullValues = "null")
        void testValidateQRChannel_ShouldDoesNotThrowException(String channelCode1, String channelCode2, Boolean isQRISO20022FlagOn) {
            ValidChannel validChannel1 = new ValidChannel().setChannelCode(channelCode1);
            ValidChannel validChannel2 = new ValidChannel().setChannelCode(channelCode2);
            ReflectionTestUtils.setField(billPromptPayValidationProcessor, "isQRISO20022FlagOn", isQRISO20022FlagOn);

            masterBillerResponse.getBillerInfo().setValidChannels(List.of(validChannel1, validChannel2));

            assertDoesNotThrow(() -> TestUtils.invokeMethod(billPromptPayValidationProcessor, "validateQRChannel", masterBillerResponse));
        }

        @Test
        void testTransformRequest_ShouldTransformRequest() {
            ProductDetail productDetail = new ProductDetail()
                    .setProductRef1("lowercase1")
                    .setProductRef2("lowercase2");

            assertDoesNotThrow(() -> TestUtils.invokeMethod(billPromptPayValidationProcessor, "transformRequestBody", productDetail));

            assertEquals("LOWERCASE1", productDetail.getProductRef1());
            assertEquals("LOWERCASE2", productDetail.getProductRef2());
        }

        @Test
        void testValidateQRChannel_WhenHasChannel97ButNotHasChannel57_ShouldThrowTmbCommonException() {
            ValidChannel validChannel1 = new ValidChannel().setChannelCode(BILLER_CHANNEL_97);
            ValidChannel validChannel2 = new ValidChannel().setChannelCode(null);
            ReflectionTestUtils.setField(billPromptPayValidationProcessor, "isQRISO20022FlagOn", false);
            masterBillerResponse.getBillerInfo().setValidChannels(List.of(validChannel1, validChannel2));

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> TestUtils.invokeMethodWithThrow(billPromptPayValidationProcessor, "validateQRChannel", masterBillerResponse));

            assertEquals(ResponseCode.BILL_PROMPT_PAY_SCAN_QR_BILLER_NOT_ALLOW_PAY_WITH_QR.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.OK, exception.getStatus());
        }
    }

    @Nested
    class ValidateCalculateFee {
        @Test
        void testCalculateFeeWhenWaiveFlagFromDepositAccountTrueThenReturnZeroFee() {
            String isWaiveFeeFromDepositFlag = "1";
            PromptPayETEValidateResponse eteResponse = new PromptPayETEValidateResponse();
            eteResponse.setFee(new BigDecimal("25.005"));
            DepositAccount fromDepositAccount = new DepositAccount();
            fromDepositAccount.setWaiveFeeForBillpay(isWaiveFeeFromDepositFlag);

            BigDecimal result = TestUtils.invokeMethod(billPromptPayValidationProcessor, "calculateFee", eteResponse, fromDepositAccount);

            assertEquals(new BigDecimal("0.00"), result);
        }

        @Test
        void testCalculateFeeWhenWaiveIsNullThenShouldNotThrowException() {
            PromptPayETEValidateResponse eteResponse = new PromptPayETEValidateResponse();
            eteResponse.setFee(new BigDecimal("25.005"));
            DepositAccount fromDepositAccount = new DepositAccount();
            fromDepositAccount.setWaiveFeeForBillpay("0");

            BigDecimal result = TestUtils.invokeMethod(billPromptPayValidationProcessor, "calculateFee", eteResponse, fromDepositAccount);

            assertEquals(new BigDecimal("25.00"), result);
        }
    }

    @Test
    void testHandleException_WhenNotTMBCommonException_ShouldThrowTMBCommonException() {
        RuntimeException unHandleExceptionClass = new RuntimeException();

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> billPromptPayValidationProcessor.handleException(null, null, null, null, null, null, unHandleExceptionClass));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
    }

    @Nested
    class HandleExceptionTest {
        @Test
        void testHandleException_WhenFailedTMBCommonException_ShouldThrowTMBCommonException() {
            assertThrows(TMBCommonException.class, () -> billPromptPayValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonException("ignore message")));
        }

        @Test
        void testHandleException_WhenFailedTMBCommonExceptionWithResponse_ShouldThrowTMBCommonExceptionWithResponse() {
            assertThrows(TMBCommonExceptionWithResponse.class, () -> billPromptPayValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonExceptionWithResponse("", "", "", HttpStatus.OK, null, null)));
        }
    }

    private void mockGetCustomerKYC(CustomerKYCResponse customerKYCResponse) throws TMBCommonException {
        customerKYCResponse.setIdNo("**********123");
        when(customerService.getCustomerKYC(anyString(), anyString())).thenReturn(customerKYCResponse);
    }

    private void assertCallValidateBillerExpiration() throws TMBCommonException {
        verify(baseBillPayValidator, times(1)).validateBillerExpiration(any(MasterBillerResponse.class));
    }

    private void assertCallValidateServiceHours() throws TMBCommonException {
        verify(baseBillPayValidator, times(1)).validateServiceHours(any(MasterBillerResponse.class));
    }

    private void assertCallValidateCompCodeExclusion() throws TMBCommonException {
        verify(baseBillPayValidator, times(1)).validateCompCodeExclusion(any(BillPayConfiguration.class), eq(compCode));
    }

    private void assertCallValidateDailyLimitExceeded() throws TMBCommonException {
        verify(dailyLimitService, times(1)).validateDailyLimitExceeded(eq(masterBillerResponse.getBillerInfo().getBillerGroupType()), eq(customerCrmProfile), any(BigDecimal.class));
    }

    private void mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth() throws TMBCommonException {
        when(dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(any(HttpHeaders.class), any(BigDecimal.class), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(new CommonAuthenResult().setRequireCommonAuthen(true));
    }

    private void mockStaticTransactionGetSequenceId() {
        when(Transaction.getSequenceKey(PROMPTPAY_REF_SEQ, 6)).thenReturn("ignore-transaction-sequence-id");
    }

    private void mockValidatePromptPayPayment() throws TMBCommonException, TMBCommonExceptionWithResponse {
        PromptPayETEValidateResponse eteResponse = new PromptPayETEValidateResponse();
        eteResponse.setSender(new Sender());
        eteResponse.setAmount(new BigDecimal("100.00"));
        eteResponse.setFee(new BigDecimal("0.00"));
        when(paymentService.validateBillPromptPayPayment(eq(correlationId), eq(crmId), any(PromptPayETEValidateRequest.class))).thenReturn(eteResponse);

    }

    private void mockValidatePromptPayISO2022Payment() throws TMBCommonException, TMBCommonExceptionWithResponse {
        PromptPayETEValidateResponse eteResponse = new PromptPayETEValidateResponse();
        eteResponse.setSender(new Sender());
        eteResponse.setAmount(new BigDecimal("100.00"));
        eteResponse.setFee(new BigDecimal("0.00"));
        when(paymentService.validateBillPromptPayISO20022Payment(eq(correlationId), eq(crmId), any(PromptPayETEValidateRequest.class))).thenReturn(eteResponse);

    }

    private void mockFetchBillPayConfig() throws TMBCommonException {
        BillerCreditcardMerchant e1 = new BillerCreditcardMerchant();
        e1.setMerchantId("merchantId");
        List<BillerCreditcardMerchant> billerCreditCardMerchant = List.of(e1);
        BillPayConfiguration billPayConfig = new BillPayConfiguration().setBillerCreditcardMerchant(billerCreditCardMerchant);

        when(paymentService.getBillPayConfig(correlationId)).thenReturn(billPayConfig);
    }

    private void mockGetAccountByAccountNumber() throws TMBCommonException {
        DepositAccount depositAccount = new DepositAccount()
                .setAccountNumber(depositAccountNumber)
                .setAccountType("SDA")
                .setAvailableBalance(BigDecimal.valueOf(999999.99));
        when(billPayAccountCommonPaymentService.getAccountByAccountNumber(depositAccountNumber, headers)).thenReturn(depositAccount);
    }

    private void mockFetchCustomerCrmProfile(CustomerCrmProfile customerCrmProfile) throws TMBCommonException {
        customerCrmProfile.setPaymentAccuUsgAmt(BigDecimal.valueOf(100.50))
                .setBillpayAccuUsgAmt(BigDecimal.valueOf(2000.50))
                .setEbAccuUsgAmtDaily(2000.50);
        when(customerService.getCustomerCrmProfile(correlationId, crmId)).thenReturn(customerCrmProfile);
    }

    private void mockFetchMasterBillerByCompCode(MasterBillerResponse masterBiller) throws TMBCommonException {
        when(paymentService.getMasterBiller(correlationId, compCode)).thenReturn(masterBiller);
    }

    private MasterBillerResponse setUpMasterBillerDefault() {
        MasterBillerResponse masterBiller = new MasterBillerResponse().setBillerInfo(new BillerInfoResponse());

        BillerInfoResponse billerInfoResponse = masterBiller.getBillerInfo();
        billerInfoResponse.setStartTime(null);
        billerInfoResponse.setEndTime(null);
        billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        billerInfoResponse.setBillerCompCode(compCode);
        billerInfoResponse.setNameEn("Bill-PromptPay");
        billerInfoResponse.setBillerGroupType(BILLER_GROUP_TYPE_BILL);
        billerInfoResponse.setBillerCategoryCode("not sure");

        masterBiller.setRef1(new ReferenceResponse().setIsMobile(false));

        return masterBiller;
    }

    private CommonPaymentDraftCache initialCacheDataForBillPayTransaction() {
        PaymentInformation p = new PaymentInformation()
                .setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY)
                .setTransactionType("bill_pay")
                .setCompCode(compCode)
                .setFundCode("ignore")
                .setRequireAddressFlag(true)
                .setProductDetail(new ProductDetail()
                        .setProductNameEn("Product-name-en-value")
                        .setProductNameTh("Product-name-th-value")
                        .setProductAttributeList(List.of(
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("1000000006")
                                        .setValueTh("1000000006"),
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("0987834783")
                                        .setValueTh("0987834783")
                        ))
                        .setProductRef1("reference1")
                        .setProductRef2("reference2")
                )
                .setAmountDetail(new AmountDetail()
                        .setAmountLabelEn("Amount-lable-en-value")
                        .setAmountLabelTh("Amount-lable-th-value")
                        .setAmountUnitEn("Amount-unit-en-value")
                        .setAmountUnitTh("Amount-unit-th-value")
                        .setAmountValue(BigDecimal.valueOf(9855.00))
                )
                .setCompleteScreenDetail(new CompleteScreenDetail()
                        .setRemarkEn("Remark-en-value")
                        .setRemarkTh("Remark-th-value")
                        .setFooterEn("Footer-en-value")
                        .setFooterTh("Footer-th-value")
                        .setBackBtnKeyEn("Back-btn-key-en-value")
                        .setBackBtnKeyTh("Back-btn-key-th-value")
                        .setBackBtnUrl("www.google.co.th")
                );

        return new CommonPaymentDraftCache()
                .setProcessorType(BILLER_PAYMENT_BILL_PROMPT_PAY)
                .setPaymentInformation(p)
                .setCrmId(crmId)
                .setCommonPaymentConfig(commonPaymentConfig);
    }

    private ValidationCommonPaymentRequest initialDefaultRequest() {
        request = new ValidationCommonPaymentRequest();
        request.setDeposit(new DepositValidationCommonPaymentRequest().setPayWithDepositFlag(true).setAccountNumber(depositAccountNumber).setAmount(BigDecimal.valueOf(9500.50)));
        request.setCreditCard(new CreditCardValidationCommonPaymentRequest().setPayWithCreditCardFlag(false));
        request.setFlow("flow");
        request.setTransactionId("transaction_id");
        return request;
    }

    private void assertSaveCache() throws JsonProcessingException {
        verify(cacheService, times(1)).set(anyString(), eq(COMMON_PAYMENT_HASH_KEY_CACHE), any());
    }

    private void assertWriteActivityLog() {
        verify(logEventPublisherService, times(1)).saveActivityLog(any());
    }
}