package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ConfirmationCommonPaymentProcessor;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ConfirmationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.ConfirmationCommonPaymentProcessorSelector;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;

import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ConfirmationCommonPaymentServiceTest {
    @InjectMocks
    private ConfirmationCommonPaymentService confirmationCommonPaymentService;

    @Mock
    private CacheService cacheService;
    @Mock
    private ConfirmationCommonPaymentProcessorSelector confirmationCommonPaymentProcessorSelector;
    @Mock
    private ConfirmationCommonPaymentProcessor confirmationCommonPaymentProcessor;
    @Mock
    private CacheMapper cacheMapper;

    private HttpHeaders headers;
    private CommonPaymentDraftCache draftCache;
    private ConfirmationCommonPaymentRequest request;

    @BeforeEach
    void setUp() {
        headers = new HttpHeaders();
        headers.set(CommonPaymentExpConstant.HEADER_CORRELATION_ID, "correlationId");
        headers.set(CommonPaymentExpConstant.HEADER_CRM_ID, "crmId");
        headers.set(CommonPaymentExpConstant.HEADER_IP_ADDRESS, "0.0.0.0");

        draftCache = new CommonPaymentDraftCache();
        PaymentInformation paymentInfo = new PaymentInformation();
        paymentInfo.setTransactionType("bill_pay");
        draftCache.setPaymentInformation(paymentInfo);
        draftCache.setProcessorType(BILLER_PAYMENT_CREDIT_CARD);

        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setStartTime("08:00");
        billerInfo.setEndTime("20:00");
        masterBillerResponse.setBillerInfo(billerInfo);
        when(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse)).thenReturn(new MasterBillerResponseInCache());
        validateDraftCache.setMasterBillerResponse(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse));
        var commonAuthentication = new CommonAuthenticationValidationCommonPaymentResponse();
        commonAuthentication.setFeatureId(COMMON_PAYMENT_BILL_PAY_FEATURE_ID);
        commonAuthentication.setTotalPaymentAccumulateUsage(BigDecimal.valueOf(500.55));
        validateDraftCache.setCommonAuthentication(commonAuthentication);
        OCPBillRequest ocpBillPaymentConfirmRequest = new OCPBillRequest();
        ocpBillPaymentConfirmRequest.setAmount("5000.50");
        validateDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setOcpBillPaymentConfirmRequest(ocpBillPaymentConfirmRequest));
        draftCache.setValidateDraftCache(validateDraftCache);

        ValidationCommonPaymentRequest validateRequest = new ValidationCommonPaymentRequest();
        validateRequest.setFlow("PAYMENT_CONFIRMATION");
        validateRequest.setTransactionId("transactionId");
        draftCache.setValidateRequest(validateRequest);

        request = new ConfirmationCommonPaymentRequest();
        request.setTransactionId("transactionId");
    }

    @Test
    void testConfirmCommonPaymentWhenSuccess() throws Exception {
        when(cacheService.get(anyString(), anyString(), any())).thenReturn(draftCache);
        when(confirmationCommonPaymentProcessorSelector.getProcessor(anyString())).thenReturn(confirmationCommonPaymentProcessor);

        ConfirmationCommonPaymentResponse expectedResponse = new ConfirmationCommonPaymentResponse();
        when(confirmationCommonPaymentProcessor.executeConfirm(any(), any(), any())).thenReturn(expectedResponse);

        ConfirmationCommonPaymentResponse result = confirmationCommonPaymentService.confirmCommonPayment(request, headers);

        assertNotNull(result);
    }

    @Test
    void testConfirmCommonPaymentWhenCacheNotFound() throws TMBCommonException {
        when(cacheService.get(anyString(), anyString(), any())).thenReturn(null);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> confirmationCommonPaymentService.confirmCommonPayment(request, headers));
        assertEquals(ResponseCode.TRANSACTION_NOT_FOUND_ERROR.getCode(), exception.getErrorCode());
    }

    @Test
    void testConfirmCommonPaymentWhenProcessorNotFound() {
        try {
            when(cacheService.get(anyString(), anyString(), any())).thenReturn(draftCache);
            when(confirmationCommonPaymentProcessorSelector.getProcessor(anyString())).thenReturn(null);
            doNothing().when(confirmationCommonPaymentProcessorSelector).validateTransactionType(anyString());

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> confirmationCommonPaymentService.confirmCommonPayment(request, headers));
            assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        } catch (TMBCommonException e) {
            fail("Should not throw checked exception directly: " + e.getMessage());
        }
    }

    @Test
    void testConfirmCommonPaymentWhenValidateTransactionTypeFailed() throws Exception {
        when(cacheService.get(anyString(), anyString(), any())).thenReturn(draftCache);
        doThrow(new TMBCommonException("Validate transaction type failed")).when(confirmationCommonPaymentProcessorSelector).validateTransactionType(anyString());

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> {
            try {
                confirmationCommonPaymentService.confirmCommonPayment(request, headers);
            } catch (TMBCommonException e) {
                throw e;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        assertEquals("Validate transaction type failed", exception.getMessage());
    }

    @Test
    void testGetDraftCache() throws Exception {
        when(cacheService.get(anyString(), anyString(), any())).thenReturn(draftCache);
        var method = ConfirmationCommonPaymentService.class.getDeclaredMethod("getDraftCache", String.class);
        method.setAccessible(true);
        Object result = method.invoke(confirmationCommonPaymentService, "txn123");
        assertNotNull(result);
    }

    @Test
    void testGetDraftCacheWhenNull() throws Exception {
        when(cacheService.get(anyString(), anyString(), any())).thenReturn(null);
        var method = ConfirmationCommonPaymentService.class.getDeclaredMethod("getDraftCache", String.class);
        method.setAccessible(true);
        Exception exception = assertThrows(Exception.class, () -> method.invoke(confirmationCommonPaymentService, "txn123"));
        assertInstanceOf(TMBCommonException.class, exception.getCause());
    }

    @Test
    void testGetPaymentProcessor() throws Exception {
        when(confirmationCommonPaymentProcessorSelector.getProcessor(anyString())).thenReturn(confirmationCommonPaymentProcessor);
        doNothing().when(confirmationCommonPaymentProcessorSelector).validateTransactionType(anyString());
        var method = ConfirmationCommonPaymentService.class.getDeclaredMethod("getPaymentProcessor", CommonPaymentDraftCache.class);
        method.setAccessible(true);
        Object result = method.invoke(confirmationCommonPaymentService, draftCache);
        assertNotNull(result);
    }

    @Test
    void testGetPaymentProcessorWhenNull() throws Exception {
        when(confirmationCommonPaymentProcessorSelector.getProcessor(anyString())).thenReturn(null);
        doNothing().when(confirmationCommonPaymentProcessorSelector).validateTransactionType(anyString());
        var method = ConfirmationCommonPaymentService.class.getDeclaredMethod("getPaymentProcessor", CommonPaymentDraftCache.class);
        method.setAccessible(true);
        Exception exception = assertThrows(Exception.class, () -> method.invoke(confirmationCommonPaymentService, draftCache));
        assertInstanceOf(TMBCommonException.class, exception.getCause());
    }
}
