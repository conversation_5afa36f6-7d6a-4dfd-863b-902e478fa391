package com.tmb.oneapp.commonpaymentexp.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.common.logger.TMBLogger;
import com.tmb.common.model.BaseEvent;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.model.financiallog.FinancialRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.TransactionActivities;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class LogEventPublisherServiceTest {
    private static final TMBLogger<LogEventPublisherServiceTest> logger = new TMBLogger<>(LogEventPublisherServiceTest.class);

    @Mock
    private KafkaProducerService kafkaProducerService;

    @InjectMocks
    private LogEventPublisherService logEventPublisherService;

    private String topicDevActivity = "dev_activity";
    private String topicDevFinancial = "dev_financial";
    private String topicDevTransaction = "dev_transaction";

    @BeforeEach
    void setup() {
        ReflectionTestUtils.setField(logEventPublisherService, "topicNameActivity", topicDevActivity);
        ReflectionTestUtils.setField(logEventPublisherService, "topicNameFinancial", topicDevFinancial);
        ReflectionTestUtils.setField(logEventPublisherService, "topicNameTransaction", topicDevTransaction);
    }

    @Test
    void testSaveActivityLogShouldSuccessWithoutException() {
        BaseEvent event = new BaseEvent();
        String expectedEventString = "test-event-string";

        try (MockedStatic<TMBUtils> mockedTMBUtils = mockStatic(TMBUtils.class)) {
            mockedTMBUtils.when(() -> TMBUtils.convertJavaObjectToString(event))
                    .thenReturn(expectedEventString);

            logEventPublisherService.saveActivityLog(event);

            verify(kafkaProducerService).sendMessageAsync(anyString(), eq(expectedEventString));
        }
    }

    @Test
    void testSaveActivityLogWhenManyEventShouldSuccessWithoutException() {
        BaseEvent event1 = new BaseEvent();
        BaseEvent event2 = new BaseEvent();

        logEventPublisherService.saveActivityLog(event1, event2);

        verify(kafkaProducerService, times(2)).sendMessageAsync(anyString(), anyString());
    }

    @Test
    void testSaveActivityLogWhenConvertToStringErrorShouldNotThrowException() {
        BaseEvent event = new BaseEvent();

        try (MockedStatic<TMBUtils> mockedTMBUtils = mockStatic(TMBUtils.class)) {
            mockedTMBUtils.when(() -> TMBUtils.convertJavaObjectToString(event))
                    .thenThrow(new JsonProcessingException("Error converting to string") {
                    });

            assertDoesNotThrow(() -> logEventPublisherService.saveActivityLog(event));
        }
    }

    @Test
    void testSaveFinancialLogShouldSuccessWithoutException() {
        String correlationId = "test-correlation-id";
        FinancialRequest financialRequest = new FinancialRequest();
        financialRequest.setReferenceID("test-ref-id");
        String expectedFinancialLog = "test-financial-log";

        try (MockedStatic<TMBUtils> mockedTMBUtils = mockStatic(TMBUtils.class)) {
            mockedTMBUtils.when(() -> TMBUtils.convertJavaObjectToString(financialRequest))
                    .thenReturn(expectedFinancialLog);

            logEventPublisherService.saveFinancialLog(correlationId, financialRequest);

            verify(kafkaProducerService, times(1)).sendMessageAsync(topicDevFinancial, expectedFinancialLog);
        }
    }

    @Test
    void testSaveFinancialLogWhenConvertToStringErrorShouldNotThrowException() {
        String correlationId = "test-correlation-id";
        FinancialRequest financialRequest = new FinancialRequest();

        try (MockedStatic<TMBUtils> mockedTMBUtils = mockStatic(TMBUtils.class)) {
            mockedTMBUtils.when(() -> TMBUtils.convertJavaObjectToString(financialRequest))
                    .thenThrow(new JsonProcessingException("Error converting to string") {
                    });

            assertDoesNotThrow(() -> logEventPublisherService.saveFinancialLog(correlationId, financialRequest));
        }
    }

    @Test
    void testSaveTransactionLogShouldSuccessWithoutException() {
        String correlationId = "test-correlation-id";
        TransactionActivities transactionActivity = new TransactionActivities();
        String expectedTransactionLog = "test-transaction-log";

        try (MockedStatic<TMBUtils> mockedTMBUtils = mockStatic(TMBUtils.class)) {
            mockedTMBUtils.when(() -> TMBUtils.convertJavaObjectToString(transactionActivity))
                    .thenReturn(expectedTransactionLog);

            logEventPublisherService.saveTransactionLog(correlationId, transactionActivity);

            verify(kafkaProducerService, times(1)).sendMessageAsync(topicDevTransaction, expectedTransactionLog);
        }
    }

    @Test
    void testSaveTransactionLogWhenConvertToStringErrorShouldNotThrowException() {
        String correlationId = "test-correlation-id";
        TransactionActivities transactionActivity = new TransactionActivities();

        try (MockedStatic<TMBUtils> mockedTMBUtils = mockStatic(TMBUtils.class)) {
            mockedTMBUtils.when(() -> TMBUtils.convertJavaObjectToString(transactionActivity))
                    .thenThrow(new JsonProcessingException("Error converting to string") {
                    });

            assertDoesNotThrow(() -> logEventPublisherService.saveTransactionLog(correlationId, transactionActivity));
        }
    }
}