package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.CacheResponse;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class HpExpServiceTest {
    @Mock
    private FeignClientHelper feignClientHelper;

    @InjectMocks
    private HpExpService hpExpService;

    private String correlationId;
    private String acceptLanguage;
    private String appVersion;
    private String crmId;
    private String objectiveId;
    private String hpAccountNo;

    @BeforeEach
    void setUp() {
        correlationId = "correlationId";
        acceptLanguage = "th";
        appVersion = "1.0.0";
        crmId = "001100000000000000000018593707";
        objectiveId = "HP0001";
        hpAccountNo = "****************";
    }

    @Test
    void testGetAldxFeeCacheWhenSuccessShouldReturnCacheResponse() throws TMBCommonException {
        CacheResponse expectedResponse = new CacheResponse();
        expectedResponse.setValue("test-value");

        when(feignClientHelper.<CacheResponse>executeRequest(any()))
                .thenReturn(expectedResponse);

        CacheResponse actualResponse = hpExpService.getAldxFeeCache(
                correlationId, acceptLanguage, appVersion, crmId, objectiveId, hpAccountNo);

        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        assertEquals("test-value", actualResponse.getValue());

        verify(feignClientHelper).executeRequest(any());
    }

    @Test
    void testGetAldxFeeCacheWithDirectFeignClientWhenSuccessShouldReturnCacheResponse() throws TMBCommonException {
        CacheResponse expectedCacheResponse = new CacheResponse();
        expectedCacheResponse.setValue("test-value-from-feign");

        when(feignClientHelper.<CacheResponse>executeRequest(any()))
                .thenReturn(expectedCacheResponse);

        CacheResponse actualResponse = hpExpService.getAldxFeeCache(
                correlationId, acceptLanguage, appVersion, crmId, objectiveId, hpAccountNo);

        assertNotNull(actualResponse);
        assertEquals("test-value-from-feign", actualResponse.getValue());

        verify(feignClientHelper).executeRequest(any());
    }

    @Test
    void testGetAldxFeeCacheWithDirectFeignClientInteraction() throws TMBCommonException {

        CacheResponse expectedCacheResponse = new CacheResponse();
        expectedCacheResponse.setValue("test-value-from-direct-interaction");

        when(feignClientHelper.<CacheResponse>executeRequest(any()))
                .thenReturn(expectedCacheResponse);

        CacheResponse actualResponse = hpExpService.getAldxFeeCache(
                correlationId, acceptLanguage, appVersion, crmId, objectiveId, hpAccountNo);

        assertNotNull(actualResponse);
        assertEquals("test-value-from-direct-interaction", actualResponse.getValue());

        verify(feignClientHelper).executeRequest(any());
    }

    @Test
    void testGetAldxFeeCacheWithDirectFeignClientWhenResponseStatusNotSuccessShouldThrowException() throws TMBCommonException {
        TMBCommonException expectedException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                ResponseCode.FAILED_V2.getMessage(),
                ResponseCode.FAILED_V2.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                null);

        when(feignClientHelper.<CacheResponse>executeRequest(any()))
                .thenThrow(expectedException);

        TMBCommonException actualException = assertThrows(TMBCommonException.class, () ->
                hpExpService.getAldxFeeCache(correlationId, acceptLanguage, appVersion, crmId, objectiveId, hpAccountNo));

        assertEquals(ResponseCode.FAILED_V2.getCode(), actualException.getErrorCode());

        verify(feignClientHelper).executeRequest(any());
    }

    @Test
    void testGetAldxFeeCacheWhenFeignExceptionShouldThrowTMBCommonException() throws TMBCommonException {
        TMBCommonException expectedException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                ResponseCode.FAILED_V2.getMessage(),
                ResponseCode.FAILED_V2.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                null);

        when(feignClientHelper.<CacheResponse>executeRequest(any()))
                .thenThrow(expectedException);

        TMBCommonException actualException = assertThrows(TMBCommonException.class, () ->
                hpExpService.getAldxFeeCache(correlationId, acceptLanguage, appVersion, crmId, objectiveId, hpAccountNo));

        assertEquals(ResponseCode.FAILED_V2.getCode(), actualException.getErrorCode());
        verify(feignClientHelper).executeRequest(any());
    }

    @Test
    void testGetAldxFeeCacheWhenGeneralExceptionShouldThrowTMBCommonException() throws TMBCommonException {
        TMBCommonException expectedException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                ResponseCode.FAILED_V2.getMessage(),
                ResponseCode.FAILED_V2.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                null);

        when(feignClientHelper.<CacheResponse>executeRequest(any()))
                .thenThrow(expectedException);

        TMBCommonException actualException = assertThrows(TMBCommonException.class, () ->
                hpExpService.getAldxFeeCache(correlationId, acceptLanguage, appVersion, crmId, objectiveId, hpAccountNo));

        assertEquals(ResponseCode.FAILED_V2.getCode(), actualException.getErrorCode());
        verify(feignClientHelper).executeRequest(any());
    }

    @Test
    void testGetAldxFeeCacheWhenTMBCommonExceptionShouldPropagateException() throws TMBCommonException {
        TMBCommonException expectedException = new TMBCommonException(
                ResponseCode.FAILED.getCode(),
                ResponseCode.FAILED.getMessage(),
                ResponseCode.FAILED.getService(),
                HttpStatus.INTERNAL_SERVER_ERROR,
                null);

        when(feignClientHelper.<CacheResponse>executeRequest(any()))
                .thenThrow(expectedException);

        TMBCommonException actualException = assertThrows(TMBCommonException.class, () ->
                hpExpService.getAldxFeeCache(correlationId, acceptLanguage, appVersion, crmId, objectiveId, hpAccountNo));

        assertEquals(ResponseCode.FAILED.getCode(), actualException.getErrorCode());
        verify(feignClientHelper).executeRequest(any());
    }
}