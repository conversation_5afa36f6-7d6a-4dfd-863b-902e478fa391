package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.request.notification.NotificationRequest;
import com.tmb.common.model.response.notification.NotificationResponse;
import com.tmb.oneapp.commonpaymentexp.client.NotificationServiceClient;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.function.Supplier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NotificationServiceTest {
    @InjectMocks
    NotificationService notificationService;
    @Mock
    private NotificationServiceClient notificationServiceClient;
    @Mock
    private FeignClientHelper feignClientHelper;

    @BeforeEach
    void setUp() {
    }

    @Test
    void testSendMessage_ShouldSuccess() {
        setUpFeignClientHelperExecuteRequestSafely();
        TmbServiceResponse<NotificationResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new NotificationResponse());
        when(notificationServiceClient.sendMessage(anyString(), any(NotificationRequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> notificationService.sendMessage("correlation-id", new NotificationRequest()));
    }

    private void setUpFeignClientHelperExecuteRequestSafely() {
        when(feignClientHelper.executeRequestSafely(any())).thenAnswer(invocation -> {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
            ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
            return feignResponseEntity.getBody().getData();
        });
    }
}