package com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.exception.NullPointerCustomException;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.DeepLinkRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.CallbackInitialRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitialPrepareData;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;


@ExtendWith(MockitoExtension.class)
class PartnerInitializationValidatorImplTest {
    @InjectMocks
    private PartnerInitializationValidatorImpl partnerInitializationValidator;

    private CommonPaymentConfig commonPaymentConfig;
    private InitializationCommonPaymentRequest request;
    private HttpHeaders headers;
    private InitialPrepareData prepareData;
    private MasterBillerResponse masterBillerResponse;
    private DeepLinkRequest deepLinkRequest;
    private String correlationId;

    @BeforeEach
    void setUp() {
        correlationId = "test-correlation-id";

        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        request = initialCommonPaymentRequest();
        deepLinkRequest = null;
        masterBillerResponse = initialMasterBiller();
        commonPaymentConfig = initialCommonPaymentConfigWithExpireDate("2099-12-02 23:59:59");
        prepareData = new InitialPrepareData()
                .setMasterBillerResponse(masterBillerResponse)
                .setCommonPaymentConfig(commonPaymentConfig)
                .setDeepLinkRequest(deepLinkRequest);

    }

    @Test
    void testValidateData_WhenSuccessRequestCallbackUrlContainInCommonPaymentConfig_ShouldDoesNotThrowException() {
        request.getPaymentInformation().getCallback().setEnableCallbackFlag(true).setCallbackUrl("www.correct.url");

        assertDoesNotThrow(() -> partnerInitializationValidator.validateData(request, headers, prepareData));

    }

    @Test
    void testValidateData_WhenSuccessRequestCallbackNull_ShouldSkipValidateCallbackUrlAndDoesNotThrowException() {
        request.getPaymentInformation().setCallback(null);

        assertDoesNotThrow(() -> partnerInitializationValidator.validateData(request, headers, prepareData));

    }

    @Test
    void testValidateData_WhenSuccessEnableCallbackFlagFalse_ShouldDoesNotThrowException() {
        request.getPaymentInformation().getCallback().setEnableCallbackFlag(false).setCallbackUrl("www.wrong.url");

        assertDoesNotThrow(() -> partnerInitializationValidator.validateData(request, headers, prepareData));

    }

    @Test
    void testValidateData_WhenFailedRequestCallbackUrlNotContainInCommonPaymentConfig_ShouldThrowException() {
        request.getPaymentInformation().getCallback().setEnableCallbackFlag(true).setCallbackUrl("www.wrong.url");

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> partnerInitializationValidator.validateData(request, headers, prepareData));

        assertEquals(ResponseCode.INVALID_CALLBACK_URL_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
    }

    @Test
    void testValidateData_WhenFailedRequestCallbackUrlNullAndEnableCallbackFlagTrue_ShouldThrowException() {
        request.getPaymentInformation().getCallback().setEnableCallbackFlag(true).setCallbackUrl(null);

        NullPointerCustomException exception = assertThrows(NullPointerCustomException.class, () -> partnerInitializationValidator.validateData(request, headers, prepareData));

        assertEquals("callbackUrlRequest should not be null when enableCallbackUrl is true", exception.getMessage());
        assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
    }

    @Test
    void testValidateData_WhenFailedCommonPaymentConfigNull_ShouldThrowException() {
        prepareData.setCommonPaymentConfig(null);
        request.getPaymentInformation().getCallback().setEnableCallbackFlag(true).setCallbackUrl("www.correct.url");

        NullPointerCustomException exception = assertThrows(NullPointerCustomException.class, () -> partnerInitializationValidator.validateData(request, headers, prepareData));

        assertEquals("common_payment_config should not be null, Please verify common_payment_config", exception.getMessage());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
    }

    @Test
    void testValidateData_WhenFailedCommonPaymentConfigExpired_ShouldThrowException() {
        LocalDateTime expireData = LocalDateTime.parse("1999-12-02 23:59:59", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        prepareData.getCommonPaymentConfig().setExpiryDate(expireData);
        request.getPaymentInformation().getCallback().setEnableCallbackFlag(true).setCallbackUrl("www.correct.url");

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> partnerInitializationValidator.validateData(request, headers, prepareData));

        assertEquals(ResponseCode.PRODUCT_EXPIRED_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    private static CommonPaymentConfig initialCommonPaymentConfigWithExpireDate(String date) {
        CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig();
        commonPaymentConfig.setCallbackUrls(List.of(
                "www.correct.url",
                "www.correct.v1.url"
        ));
        commonPaymentConfig.setExpiryDate(parseDateTime(date));
        return commonPaymentConfig;
    }

    private static LocalDateTime parseDateTime(String date) {
        return LocalDateTime.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private MasterBillerResponse initialMasterBiller() {
        return new MasterBillerResponse().setBillerInfo(
                        new BillerInfoResponse()
                                .setBillerGroupType(BILLER_GROUP_TYPE_BILL)
                )
                .setRef1(new ReferenceResponse().setIsMobile(false));
    }

    private InitializationCommonPaymentRequest initialCommonPaymentRequest() {
        return new InitializationCommonPaymentRequest()
                .setPaymentInformation(new PaymentInformation()
                        .setCallback(new CallbackInitialRequest())
                        .setEntryId("entry-id")
                        .setCompCode("comp-code")
                        .setTransactionType(COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY)
                        .setProductDetail(new ProductDetail()
                                .setProductRef1("ref1")
                                .setProductRef2("ref2"))
                        .setAmountDetail(new AmountDetail()
                                .setAmountValue(new BigDecimal("1000.00")))
                );
    }
}