package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ConfirmationCommonPaymentProcessorSelectorTest {
    ConfirmationCommonPaymentProcessorSelector selector;

    @Mock
    ConfirmationCommonPaymentProcessor mockProcessor1;

    @Mock
    ConfirmationCommonPaymentProcessor mockProcessor2;

    String transactionType;

    @BeforeEach
    void setUp() {
        transactionType = "bill_pay";
        when(mockProcessor1.getProcessorType()).thenReturn(transactionType);
        when(mockProcessor2.getProcessorType()).thenReturn("dstatement");

        selector = new ConfirmationCommonPaymentProcessorSelector(List.of(mockProcessor1, mockProcessor2));
    }

    @Test
    void testGetProcessor_ShouldSuccess() {
        ConfirmationCommonPaymentProcessor result = selector.getProcessor(transactionType);

        assertNotNull(result);
        assertEquals(mockProcessor1, result);
    }

    @Test
    void testValidateTransactionType_WhenKeyMatched_ShouldDoesNotThrowException() {
        Assertions.assertDoesNotThrow(() -> selector.validateTransactionType(transactionType));
    }

    @Test
    void testValidateTransactionType_WhenKeyNotMatched_ShouldThrowTMBCommonException() {
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> selector.validateTransactionType("incorrect-transaction-type"));

        Assertions.assertEquals(ResponseCode.MISSING_REQUIRED_FIELD_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
    }
}