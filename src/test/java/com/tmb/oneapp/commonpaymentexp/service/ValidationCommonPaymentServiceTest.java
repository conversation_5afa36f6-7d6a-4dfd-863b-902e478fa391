package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ValidationCommonPaymentProcessorSelector;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ValidationCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp.OnlineOfflineTopUpOCPBillPaymentValidationProcessor;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_CACHE;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ValidationCommonPaymentServiceTest {
    @InjectMocks
    ValidationCommonPaymentService validationCommonPaymentService;
    @Mock
    CacheService cacheService;
    @Mock
    ValidationCommonPaymentProcessorSelector validationCommonPaymentProcessorSelector;
    @Mock
    OnlineOfflineTopUpOCPBillPaymentValidationProcessor OnlineOfflineTopUpBillPaymentValidationProcessor;

    HttpHeaders headers;
    String crmId;
    String correlationId;
    String transactionId;
    CommonPaymentDraftCache cacheData;

    @BeforeEach
    void setup() {
        crmId = "crmId";
        correlationId = "correlationId";
        transactionId = "transaction-id-value";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        cacheData = new CommonPaymentDraftCache()
                .setPaymentInformation(new PaymentInformation().setTransactionType(COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY));
    }

    @Test
    void testValidateCommonPayment_WhenTransactionTypeIsBillPay_ShouldSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
        cacheData.setProcessorType(COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY);
        when(cacheService.get(anyString(), eq(COMMON_PAYMENT_HASH_KEY_CACHE), eq(CommonPaymentDraftCache.class))).thenReturn(cacheData);
        when(validationCommonPaymentProcessorSelector.getProcessor(COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY)).thenReturn(OnlineOfflineTopUpBillPaymentValidationProcessor);
        doNothing().when(validationCommonPaymentProcessorSelector).validateTransactionType(anyString());
        when(OnlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(any(ValidationCommonPaymentRequest.class), any(HttpHeaders.class), any(CommonPaymentDraftCache.class))).thenReturn(new ValidationCommonPaymentResponse());

        Assertions.assertDoesNotThrow(() -> validationCommonPaymentService.validateCommonPayment(new ValidationCommonPaymentRequest().setTransactionId(transactionId), headers));
    }

    @Test
    void testValidateCommonPayment_WhenCacheNotFound_ShouldThrowTMBCommonException() throws TMBCommonException {
        cacheData.setProcessorType(COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY);
        when(cacheService.get(anyString(), eq(COMMON_PAYMENT_HASH_KEY_CACHE), eq(CommonPaymentDraftCache.class))).thenThrow(TMBCommonException.class);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> validationCommonPaymentService.validateCommonPayment(new ValidationCommonPaymentRequest(), headers));

        Assertions.assertEquals(ResponseCode.TRANSACTION_NOT_FOUND_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());

    }
}