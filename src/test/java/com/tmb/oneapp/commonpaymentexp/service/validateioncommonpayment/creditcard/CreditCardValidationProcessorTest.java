package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.creditcard;

import com.tmb.common.cache.Transaction;
import com.tmb.common.cache.service.TransactionServices;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.creditcard.CardInfo;
import com.tmb.common.model.creditcard.CardStatus;
import com.tmb.common.model.creditcard.CreditCardDetail;
import com.tmb.common.model.creditcard.SilverlakeCustomerDetail;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableSupplier;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.service.AccountCreditCardService;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CreditCardService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.ServiceHoursUtils;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Supplier;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CATEGORY_CODE_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_APP_VERSION;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_PRE_LOGIN;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_CREDIT_CARD;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CreditCardValidationProcessorTest {

    private static final String DEFAULT_TRANSACTION_ID = "transaction_id";
    private static final String DEFAULT_DEPOSIT_ACCOUNT = "***************";
    private static final String DEFAULT_CREDIT_CARD_ID = "****************705000171";
    private static final String DEFAULT_ACCOUNT_ID = "****************";
    private static final String DEFAULT_REF1 = "****************";
    private static final String DEFAULT_REF2 = "123456";
    private static final BigDecimal DEFAULT_PAYMENT_AMOUNT = BigDecimal.valueOf(1000.00);

    @InjectMocks
    private CreditCardValidationProcessor creditCardValidationProcessor;

    @Mock
    private PaymentService paymentService;
    @Mock
    private CustomerService customerService;
    @Mock
    private AccountCreditCardService accountCreditCardService;
    @Mock
    private BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private CustomerServiceClient customerServiceClient;
    @Mock
    private CacheService cacheService;
    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private CreditCardService creditCardService;
    @Mock
    private BaseBillPayValidator baseBillPayValidator;
    @Mock
    private TransactionServices transactionServices;

    private String crmId;
    private String correlationId;
    private String acceptLanguage;
    private String appVersion;
    private HttpHeaders headers;
    private ValidationCommonPaymentRequest request;
    private CommonPaymentDraftCache cacheData;

    @BeforeEach
    void setUp() {
        crmId = "crmId";
        correlationId = "correlationId";
        acceptLanguage = "th";
        appVersion = "5.12.0";

        headers = new HttpHeaders();
        headers.add(HEADER_CRM_ID, crmId);
        headers.add(HEADER_CORRELATION_ID, correlationId);
        headers.add(HEADER_ACCEPT_LANGUAGE, acceptLanguage);
        headers.add(HEADER_APP_VERSION, appVersion);
        headers.add(HEADER_PRE_LOGIN, "false");

        cacheData = new CommonPaymentDraftCache();
        request = new ValidationCommonPaymentRequest();
        Transaction.setTransactionServices(transactionServices);
    }

    @Test
    void testGetProcessorTypeShouldReturnCreditCard() {
        String actual = creditCardValidationProcessor.getProcessorType();
        assertEquals(BILLER_PAYMENT_CREDIT_CARD, actual);
    }

    @ParameterizedTest
    @CsvSource({
            "10.00, 0, 10.00",
            "10.00, 1, 0.00",
    })
    void testGivenVariousParametersWhenCalculateFeeThenReturnExpectedFee(
            String feeAmount, String waiveFee, String expectedFee) {
        BigDecimal fee = new BigDecimal(feeAmount);
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setWaiveFeeForBillpay(waiveFee);

        BigDecimal result = creditCardValidationProcessor.calculateFee(fee, depositAccount);

        assertEquals(new BigDecimal(expectedFee), result);
    }

    @Test
    void testGivenValidDepositPaymentWhenExecuteThenSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
        setupForSuccessCase();
        mockTargetCreditCardDetail();
        when(transactionServices.getTransactionId(anyString(), anyInt())).thenReturn("12345");

        ValidationCommonPaymentResponse actual = creditCardValidationProcessor.executeValidate(request, headers, cacheData);

        assertSuccessfulResponse(actual);
    }

    @Test
    void testGivenExpiredBillerWhenExecuteThenThrowBillerExpiredException() throws TMBCommonException {
        cacheData = initializeCacheDataForCreditCard();
        request = initializeDefaultRequest();
        setupBasicMocks();
        mockGetAccountByAccountNumber();
        mockTargetCreditCardDetail();
        TMBCommonException billerExpiredException = CommonServiceUtils.getBusinessTmbCommonException(
                ResponseCode.BILLER_EXPIRED
        );
        doThrow(billerExpiredException)
                .when(baseBillPayValidator)
                .validateBillerExpiration(any(MasterBillerResponse.class));

        try (MockedStatic<ServiceHoursUtils> mockedServiceHoursUtils = mockServiceHours(true);
             MockedStatic<Transaction> mockedTransaction = mockStatic(Transaction.class)) {

            mockedTransaction.when(() -> Transaction.getTransactionId(anyString(), anyInt()))
                    .thenReturn("12345");

            TMBCommonException exception = assertThrows(TMBCommonException.class,
                    () -> creditCardValidationProcessor.executeValidate(request, headers, cacheData));

            assertEquals(ResponseCode.BILLER_EXPIRED.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.OK, exception.getStatus());
        }
    }

    @Test
    void testGivenOutsideServiceHoursWhenExecuteThenThrowUnavailableServiceHourException() throws TMBCommonException {
        cacheData = initializeCacheDataForCreditCard();
        request = initializeDefaultRequest();
        setupBasicMocks();
        mockGetAccountByAccountNumber();
        mockTargetCreditCardDetail();
        TMBCommonException serviceHoursException = CommonServiceUtils.getBusinessTmbCommonException(
                ResponseCode.UNAVAILABLE_SERVICE_HOUR
        );
        doThrow(serviceHoursException)
                .when(baseBillPayValidator)
                .validateServiceHours(any(MasterBillerResponse.class));

        try (MockedStatic<ServiceHoursUtils> mockedServiceHoursUtils = mockServiceHours(true);
             MockedStatic<Transaction> mockedTransaction = mockStatic(Transaction.class)) {

            mockedTransaction.when(() -> Transaction.getTransactionId(anyString(), anyInt()))
                    .thenReturn("12345");

            TMBCommonException exception = assertThrows(TMBCommonException.class,
                    () -> creditCardValidationProcessor.executeValidate(request, headers, cacheData));

            assertEquals(ResponseCode.UNAVAILABLE_SERVICE_HOUR.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.OK, exception.getStatus());
        }
    }


    @Test
    void testGivenDailyLimitExceededWhenExecuteThenThrowDailyLimitExceededException() throws TMBCommonException {
        String ownAccount = DEFAULT_ACCOUNT_ID;
        cacheData = initializeCacheDataForCreditCard();
        request = initializeDefaultRequest();
        request.getDeposit().setAccountNumber(ownAccount);
        setupBasicMocks();

        mockGetAccountByAccountNumber();
        mockTargetCreditCardDetail();
        TMBCommonException dailyLimitException = CommonServiceUtils.getBusinessTmbCommonException(
                ResponseCode.DAILY_LIMIT_EXCEEDED);
        doThrow(dailyLimitException).when(dailyLimitService).validateDailyLimitExceeded(any(), any(), any());

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> creditCardValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGivenTMBCommonExceptionWhenHandleExceptionThenReturnOriginalException() {
        TMBCommonException originalException = new TMBCommonException(
                ResponseCode.BILLER_EXPIRED.getCode(),
                ResponseCode.BILLER_EXPIRED.getMessage(),
                ResponseCode.BILLER_EXPIRED.getService(),
                HttpStatus.BAD_REQUEST,
                null);

        TMBCommonException result = assertThrows(TMBCommonException.class,
                () -> creditCardValidationProcessor.handleException(
                        request, headers, cacheData, null, null, null, originalException));

        assertEquals(originalException, result);
    }

    @Test
    void testGivenRuntimeExceptionWhenHandleExceptionThenReturnFailedV2Exception() {
        request = initializeDefaultRequest();
        RuntimeException runtimeException = new RuntimeException("Test runtime exception");

        TMBCommonException result = assertThrows(TMBCommonException.class,
                () -> creditCardValidationProcessor.handleException(
                        request, headers, cacheData, null, null, null, runtimeException));

        assertEquals(ResponseCode.FAILED_V2.getCode(), result.getErrorCode());
    }

    @Test
    void testGivenPaymentAmountWhenGetSequencePaymentIdThenReturnCorrectFormat() {
        try (MockedStatic<Transaction> mockedTransaction = mockStatic(Transaction.class)) {
            mockedTransaction.when(() -> Transaction.getTransactionId(anyString(), anyInt()))
                    .thenReturn("12345");

            String result = creditCardValidationProcessor.getSequencePaymentId();

            assertTrue(result.matches("\\d{14}12345"), "Payment ID should match pattern yyyyMMddHHmmss+12345");
            assertEquals(19, result.length(), "Payment ID should be 19 characters long");
        }
    }

    @Test
    void testHandlePrepareDataExceptionWhenExecutionExceptionWithTMBCommonExceptionThenThrowOriginal() {
        TMBCommonException tmbException = new TMBCommonException("CODE", "msg", "service", HttpStatus.BAD_REQUEST, null);
        ExecutionException executionException = new ExecutionException(tmbException);

        TMBCommonException thrown = assertThrows(TMBCommonException.class, () ->
                TestUtils.invokeMethodWithThrow(creditCardValidationProcessor, "handlePrepareDataException", executionException)
        );
        assertEquals(tmbException, thrown);
    }

    @Test
    void testHandlePrepareDataExceptionWhenInterruptedExceptionThenInterruptAndThrowUnhandled() {
        TMBCommonException tmbException = CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.FAILED_V2);
        InterruptedException interruptedException = new InterruptedException("interrupt");

        TMBCommonException thrown = assertThrows(TMBCommonException.class, () ->
                TestUtils.invokeMethodWithThrow(creditCardValidationProcessor, "handlePrepareDataException", interruptedException)
        );
        assertEquals(tmbException.getErrorCode(), thrown.getErrorCode());
    }

    @Test
    void testHandlePrepareDataExceptionWhenOtherExceptionThenThrowUnhandled() {
        TMBCommonException tmbException = CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.FAILED_V2);
        Exception genericException = new Exception("other error");

        TMBCommonException thrown = assertThrows(TMBCommonException.class, () ->
                TestUtils.invokeMethodWithThrow(creditCardValidationProcessor, "handlePrepareDataException", genericException)
        );
        assertEquals(tmbException.getErrorCode(), thrown.getErrorCode());
    }

    @Test
    void testFetchCreditCardByAccountIdWhenSuccessThenReturnCompletableFutureWithDetail() throws Exception {
        String correlationId = "corrId";
        String accountId = "accId";
        CreditCardDetail expectedDetail = new CreditCardDetail();
        expectedDetail.setAccountId(accountId);
        when(creditCardService.getCreditCardDetailByAccountId(accountId, correlationId)).thenReturn(expectedDetail);
        setUpAsyncHelperExecuteMethodAsync();

        Object result = TestUtils.invokeMethod(
                creditCardValidationProcessor, "fetchCreditCardByAccountId", correlationId, accountId);
        assertInstanceOf(CompletableFuture.class, result);
    }

    @Test
    void testFetchCreditCardByAccountIdWhenThrowTMBCommonExceptionThenReturnFailedFuture() throws Exception {
        String correlationId = "corrId";
        String accountId = "accId";
        TMBCommonException tmbEx = new TMBCommonException("code", "msg", "service", HttpStatus.BAD_REQUEST, null);
        when(creditCardService.getCreditCardDetailByAccountId(accountId, correlationId)).thenThrow(tmbEx);
        setUpAsyncHelperExecuteMethodAsync();

        Object result = TestUtils.invokeMethod(
                creditCardValidationProcessor, "fetchCreditCardByAccountId", correlationId, accountId);

        assertInstanceOf(CompletableFuture.class, result);

    }

    @Test
    void testGetCreditCardDetailFromReferenceWhenReferenceIsValidCreditCardNumberThenReturnByCardId() throws Exception {
        CommonPaymentDraftCache cache = initializeCacheDataForCreditCard();
        String correlationId = "correlationId";
        String cardId = DEFAULT_REF1;
        ProductDetail productDetail = cache.getPaymentInformation().getProductDetail();
        productDetail.setProductRef1(cardId);
        productDetail.setProductRef2("shouldNotBeUsed");
        CreditCardDetail mockDetail = mock(CreditCardDetail.class);
        when(creditCardService.getCreditCardDetailByCardId(cardId, correlationId)).thenReturn(mockDetail);
        CreditCardValidationProcessor spyProcessor = Mockito.spy(creditCardValidationProcessor);
        Object result = TestUtils.invokeMethod(
                spyProcessor, "getCreditCardDetailFromReference", correlationId, cache);
        assertEquals(mockDetail, result);
        Mockito.verify(creditCardService).getCreditCardDetailByCardId(cardId, correlationId);
    }

    @Test
    void testGetCreditCardDetailFromReferenceWhenReferenceIsNotValidCreditCardNumberThenReturnByAccountId() throws Exception {
        CommonPaymentDraftCache cache = initializeCacheDataForCreditCard();
        String correlationId = "correlationId";
        String cardId = "notValidCardNumber";
        String accountId = DEFAULT_REF2;
        ProductDetail productDetail = cache.getPaymentInformation().getProductDetail();
        productDetail.setProductRef1(cardId);
        productDetail.setProductRef2(accountId);
        CreditCardDetail mockDetail = mock(CreditCardDetail.class);
        when(creditCardService.getCreditCardDetailByAccountId(accountId, correlationId)).thenReturn(mockDetail);
        CreditCardValidationProcessor spyProcessor = Mockito.spy(creditCardValidationProcessor);
        Object result = TestUtils.invokeMethod(
                spyProcessor, "getCreditCardDetailFromReference", correlationId, cache);
        assertEquals(mockDetail, result);
        Mockito.verify(creditCardService).getCreditCardDetailByAccountId(accountId, correlationId);
    }

    @Test
    void testGetCreditCardDetailFromReferenceWhenCreditCardServiceThrowsTMBCommonExceptionThenThrowRuntimeException() throws Exception {
        CommonPaymentDraftCache cache = initializeCacheDataForCreditCard();
        String correlationId = "correlationId";
        String cardId = "notValidCardNumber";
        String accountId = DEFAULT_REF2;
        ProductDetail productDetail = cache.getPaymentInformation().getProductDetail();
        productDetail.setProductRef1(cardId);
        productDetail.setProductRef2(accountId);
        TMBCommonException tmbEx = new TMBCommonException("code", "msg", "service", HttpStatus.BAD_REQUEST, null);
        when(creditCardService.getCreditCardDetailByAccountId(accountId, correlationId)).thenThrow(tmbEx);
        CreditCardValidationProcessor spyProcessor = Mockito.spy(creditCardValidationProcessor);
        RuntimeException ex = assertThrows(RuntimeException.class, () ->
                TestUtils.invokeMethod(spyProcessor, "getCreditCardDetailFromReference", correlationId, cache));
        assertEquals(tmbEx, ex.getCause());
    }

    @Test
    void testGetCreditCardDetailFromReferenceWhenDraftCacheIsNullThenThrowTMBCommonException() {
        String correlationId = "correlationId";
        TMBCommonException ex = assertThrows(TMBCommonException.class, () ->
                TestUtils.invokeMethodWithThrow(creditCardValidationProcessor, "getCreditCardDetailFromReference", correlationId, null));
        assertEquals(ResponseCode.FAILED_V2.getCode(), ex.getErrorCode());
    }

    @Nested
    class HandleExceptionTest {
        @Test
        void testHandleException_WhenFailedTMBCommonException_ShouldThrowTMBCommonException() {
            assertThrows(TMBCommonException.class, () -> creditCardValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonException("ignore message")));
        }

        @Test
        void testHandleException_WhenFailedTMBCommonExceptionWithResponse_ShouldThrowTMBCommonExceptionWithResponse() {
            assertThrows(TMBCommonExceptionWithResponse.class, () -> creditCardValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonExceptionWithResponse("", "", "", HttpStatus.OK, null, null)));
        }
    }

    private MockedStatic<ServiceHoursUtils> mockServiceHours(boolean isDuringServiceHours) {
        MockedStatic<ServiceHoursUtils> mockedServiceHoursUtils = mockStatic(ServiceHoursUtils.class);
        mockedServiceHoursUtils.when(() -> ServiceHoursUtils.isDuringServiceHours(any(), any()))
                .thenReturn(isDuringServiceHours);
        return mockedServiceHoursUtils;
    }

    private void setupForSuccessCase() throws TMBCommonException {
        cacheData = initializeCacheDataForCreditCard();
        request = initializeDefaultRequest();

        setupBasicMocks();
        mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();
        mockTargetCreditCardDetail();
    }

    private void setupBasicMocks() throws TMBCommonException {
        setUpAsyncHelperExecuteRequestAsync();
        setUpAsyncHelperExecuteMethodAsync();
        mockFetchMasterBiller();
        mockFetchCustomerKYC();
        mockFetchCustomerCrmProfile();
    }


    private void assertSuccessfulResponse(ValidationCommonPaymentResponse response) {
        assertNotNull(response.getFee());
        assertNotNull(response.getTransactionId());
        assertNotNull(response.getTotalAmount());
        assertTrue(response.getIsRequireCommonAuthen());
        assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, response.getCommonAuthenticationInformation().getFeatureId());
        assertEquals(COMMON_AUTH_BILL_FLOW_NAME, response.getCommonAuthenticationInformation().getFlowName());
        assertNotNull(response.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
    }

    private void assertShouldNotValidateDailyLimit() throws TMBCommonException {
        Mockito.verify(dailyLimitService, never()).validateDailyLimitExceeded(any(), any(), any());
    }

    private CommonPaymentDraftCache initializeCacheDataForCreditCard() {
        PaymentInformation paymentInfo = new PaymentInformation();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setProductRef1(DEFAULT_REF1);
        productDetail.setProductRef2(DEFAULT_REF2);
        productDetail.setProductNameTh("บัตรเครดิตทดสอบ");
        productDetail.setProductNameEn("Test Credit Card");
        paymentInfo.setCompCode("CITI");
        paymentInfo.setProductDetail(productDetail);

        AmountDetail amountDetail = new AmountDetail();
        amountDetail.setAmountValue(DEFAULT_PAYMENT_AMOUNT);
        amountDetail.setAmountLabelTh("จำนวนเงิน");
        amountDetail.setAmountLabelEn("Amount");
        paymentInfo.setAmountDetail(amountDetail);

        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        ValidationCommonPaymentRequest request = new ValidationCommonPaymentRequest();
        cache.setPaymentInformation(paymentInfo);
        cache.setCrmId(crmId);
        cache.setValidateRequest(request);

        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();

        cache.setValidateDraftCache(validateDraftCache);
        cache.setPaymentInformation(paymentInfo);
        cache.setDeepLinkRequest(deepLinkRequest);

        return cache;
    }

    private ValidationCommonPaymentRequest initializeDefaultRequest() {
        ValidationCommonPaymentRequest req = new ValidationCommonPaymentRequest();

        DepositValidationCommonPaymentRequest deposit = new DepositValidationCommonPaymentRequest();
        deposit.setPayWithDepositFlag(true);
        deposit.setAccountNumber(DEFAULT_DEPOSIT_ACCOUNT);
        deposit.setAmount(DEFAULT_PAYMENT_AMOUNT);
        req.setDeposit(deposit);

        CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
        creditCard.setPayWithCreditCardFlag(false);
        req.setCreditCard(creditCard);

        req.setTransactionId(DEFAULT_TRANSACTION_ID);
        return req;
    }

    private void setUpAsyncHelperExecuteRequestAsync() throws TMBCommonException {
        when(asyncHelper.executeRequestAsync(any())).thenAnswer(invocation -> {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
            ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
            return CompletableFuture.completedFuture(Objects.requireNonNull(feignResponseEntity.getBody()).getData());
        });
    }

    private void setUpAsyncHelperExecuteMethodAsync() throws TMBCommonException {
        when(asyncHelper.executeMethodAsync(any())).thenAnswer(invocation -> {
            ThrowableSupplier<?> supplier = invocation.getArgument(0);
            return CompletableFuture.completedFuture(supplier.get());
        });
    }

    private void mockFetchMasterBiller() throws TMBCommonException {
        MasterBillerResponse masterBiller = new MasterBillerResponse();
        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        billerInfo.setStartTime("09:00");
        billerInfo.setEndTime("17:00");
        billerInfo.setToAccountId(DEFAULT_DEPOSIT_ACCOUNT);
        billerInfo.setBillerCompCode("CITI");
        billerInfo.setFee(BigDecimal.valueOf(0));
        billerInfo.setBillerCategoryCode(BILLER_CATEGORY_CODE_CREDIT_CARD);
        masterBiller.setBillerInfo(billerInfo);
        masterBiller.setRef1(new ReferenceResponse());

        when(paymentService.getMasterBiller(anyString(), anyString())).thenReturn(masterBiller);
    }

    private void mockFetchCustomerKYC() throws TMBCommonException {
        CustomerKYCResponse customerKYC = new CustomerKYCResponse();
        customerKYC.setIdNo("*************");
        customerKYC.setCustomerFirstNameEn("John");
        customerKYC.setCustomerLastNameEn("Doe");

        when(customerService.getCustomerKYC(anyString(), anyString())).thenReturn(customerKYC);
    }

    private void mockFetchCustomerCrmProfile() {
        CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile();
        customerCrmProfile.setPaymentAccuUsgAmt(BigDecimal.valueOf(100.50));
        customerCrmProfile.setBillpayAccuUsgAmt(BigDecimal.valueOf(2000.50));
        customerCrmProfile.setCrmId(crmId);

        TmbServiceResponse<CustomerCrmProfile> response = new TmbServiceResponse<>();
        response.setStatus(CommonServiceUtils.getStatusSuccess());
        response.setData(customerCrmProfile);

        when(customerServiceClient.fetchCustomerCrmProfile(anyString(), anyString())).thenReturn(ResponseEntity.ok(response));
    }

    private void mockGetAccountByAccountNumber() throws TMBCommonException {
        DepositAccount depositAccount = new DepositAccount();
        depositAccount.setAccountNumber(DEFAULT_DEPOSIT_ACCOUNT);
        depositAccount.setAccountType("SDA");
        depositAccount.setAvailableBalance(BigDecimal.valueOf(10000.00));
        depositAccount.setWaiveFeeForBillpay("0");

        when(billPayAccountCommonPaymentService.getAccountByAccountNumber(anyString(), any(HttpHeaders.class))).thenReturn(depositAccount);
    }


    private void mockTargetCreditCardDetail() throws TMBCommonException {
        CreditCardDetail creditCardDetail = new CreditCardDetail();
        creditCardDetail.setAccountId(DEFAULT_ACCOUNT_ID);
        creditCardDetail.setCardId(DEFAULT_CREDIT_CARD_ID);

        CardInfo cardInfo = new CardInfo();
        cardInfo.setCardEmbossingName1("JOHN DOE");
        cardInfo.setExpiredBy("12/25");
        creditCardDetail.setCardInfo(cardInfo);

        CardStatus cardStatus = new CardStatus();
        cardStatus.setCardPloanFlag("Y");
        creditCardDetail.setCardStatus(cardStatus);

        creditCardDetail.setProductId("VBWC");

        SilverlakeCustomerDetail customer = new SilverlakeCustomerDetail();
        customer.setRmId("DIFFERENT_CRM_ID");
        creditCardDetail.setCustomer(customer);

        lenient().when(creditCardService.getCreditCardDetailByAccountId(anyString(), anyString())).thenReturn(creditCardDetail);
        lenient().when(creditCardService.getCreditCardDetailByCardId(anyString(), anyString())).thenReturn(creditCardDetail);
    }

    private void mockValidateDailyLimitExceededDoNothing() throws TMBCommonException {
        doNothing().when(baseBillPayValidator).validateDailyLimit(any(ValidationCommonPaymentRequest.class), any(MasterBillerResponse.class), any(CustomerCrmProfile.class));
    }

    private void mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth() throws TMBCommonException {
        CommonAuthenResult result = new CommonAuthenResult();
        result.setRequireCommonAuthen(true);

        when(dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(
                any(HttpHeaders.class), any(BigDecimal.class), anyBoolean(), any(CustomerCrmProfile.class)))
                .thenReturn(result);
    }
}
