package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.customer.AccumulateUsageRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.model.notification.ENotificationSettingResponse;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CustomerServiceTest {

    @Mock
    private FeignClientHelper feignClientHelper;

    @InjectMocks
    private CustomerService customerService;

    private String correlationId;
    private String crmId;

    @BeforeEach
    void setUp() {
        correlationId = "correlationId";
        crmId = "crmId";
    }

    @Test
    void testGetCustomerCrmProfileWhenSuccessShouldReturnProfile() throws TMBCommonException {
        CustomerCrmProfile expectedProfile = new CustomerCrmProfile();
        expectedProfile.setCrmId(crmId);
        expectedProfile.setEbMaxLimitAmtCurrent(1000);

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn(expectedProfile);

        CustomerCrmProfile result = customerService.getCustomerCrmProfile(correlationId, crmId);

        assertNotNull(result);
        assertEquals(crmId, result.getCrmId());
        assertEquals(1000, result.getEbMaxLimitAmtCurrent());
    }

    @Test
    void testGetCustomerCrmProfileWhenFailedShouldThrowException() throws TMBCommonException {
        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenThrow(new TMBCommonException(ResponseCode.FAILED_V2.getCode(), ResponseCode.FAILED_V2.getMessage(), ResponseCode.FAILED_V2.getService(), null, null));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> customerService.getCustomerCrmProfile(correlationId, crmId));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testUpdateUsageAccumulationWhenSuccessShouldReturnString() throws TMBCommonException {
        String expectedResponse = "success";
        AccumulateUsageRequest request = new AccumulateUsageRequest();
        request.setDailyAccumulateUsageAmount(new BigDecimal("100.00"));

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn(expectedResponse);

        String result = customerService.updateUsageAccumulation(correlationId, crmId, request);

        assertEquals(expectedResponse, result);
    }

    @Test
    void testUpdatePinFreeCountWhenSuccessShouldNotThrowException() throws TMBCommonException {
        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn("success");

        assertDoesNotThrow(() -> customerService.updatePinFreeCount(crmId, 5, correlationId));
    }

    @Test
    void testGetENotificationSettingWhenSuccessShouldReturnSettings() {
        ENotificationSettingResponse expectedResponse = new ENotificationSettingResponse();
        expectedResponse.setTransactionNotification(true);

        when(feignClientHelper.executeRequestSafely(any())).thenReturn(expectedResponse);

        ENotificationSettingResponse result = customerService.getENotificationSetting(crmId, correlationId);

        assertNotNull(result);
        assertTrue(result.isTransactionNotification());
    }

    @Test
    void testGetCustomerKYCWhenSuccessShouldReturnKYCResponse() throws TMBCommonException {
        CustomerKYCResponse expectedResponse = new CustomerKYCResponse();
        expectedResponse.setRmId(crmId);
        expectedResponse.setCustomerFirstNameEn("John");
        expectedResponse.setCustomerLastNameEn("Doe");

        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenReturn(expectedResponse);

        CustomerKYCResponse result = customerService.getCustomerKYC(correlationId, crmId);

        assertNotNull(result);
        assertEquals(crmId, result.getRmId());
        assertEquals("John", result.getCustomerFirstNameEn());
        assertEquals("Doe", result.getCustomerLastNameEn());
    }

    @Test
    void testGetCustomerKYCWhenFailedShouldThrowException() throws TMBCommonException {
        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenThrow(new TMBCommonException(ResponseCode.FAILED_V2.getCode(), ResponseCode.FAILED_V2.getMessage(), ResponseCode.FAILED_V2.getService(), null, null));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> customerService.getCustomerKYC(correlationId, crmId));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }
}