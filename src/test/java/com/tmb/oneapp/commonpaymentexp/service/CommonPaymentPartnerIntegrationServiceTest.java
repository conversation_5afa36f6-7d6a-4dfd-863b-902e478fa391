package com.tmb.oneapp.commonpaymentexp.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jose.jwk.RSAKey;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.security.PublicKeyResponse;
import com.tmb.oneapp.commonpaymentexp.utils.JoseUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.text.ParseException;
import java.util.Arrays;
import java.util.Map;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonPaymentPartnerIntegrationServiceTest {

    @Mock
    private JwkSetProvider jwkSetProvider;
    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private CommonPaymentPartnerIntegrationService commonPaymentPartnerIntegrationService;

    private JWKSet jwkSet;
    private String partnerName = "test-partner";

    @BeforeEach
    void setUp() throws ParseException {
        String jwkSetJsonString = "{\"keys\":[{\"p\":\"...\",\"kty\":\"RSA\",\"q\":\"...\",\"d\":\"...\",\"e\":\"AQAB\",\"use\":\"sig\",\"kid\":\"test-key\",\"qi\":\"...\",\"dp\":\"...\",\"alg\":\"RS256\",\"dq\":\"...\",\"n\":\"...\"}]}";
        jwkSet = JWKSet.parse(jwkSetJsonString);
    }

    @Test
    void testGetPublicKeySuccess() throws TMBCommonException {
        String partnerName = "test-partner";
        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSet);

        PublicKeyResponse response = commonPaymentPartnerIntegrationService.getPublicKey(partnerName);

        assertNotNull(response);
        Map<String, Object> expectedMap = jwkSet.toPublicJWKSet().toJSONObject();
        assertEquals(expectedMap, response.getJwkSet());
    }

    @Test
    void testGetPublicKeyNotFound() throws TMBCommonException {
        String partnerName = "unknown-partner";
        TMBCommonException givenException = new TMBCommonException(
                ResponseCode.FAILED_V2.getCode(),
                "JWKSet not found",
                ResponseCode.FAILED_V2.getService(),
                HttpStatus.BAD_REQUEST,
                null
        );

        when(jwkSetProvider.getJwkSet(partnerName)).thenThrow(givenException);

        TMBCommonException thrownException = assertThrows(TMBCommonException.class, () -> commonPaymentPartnerIntegrationService.getPublicKey(partnerName));

        assertEquals(givenException.getErrorCode(), thrownException.getErrorCode());
        assertEquals(givenException.getErrorMessage(), thrownException.getErrorMessage());
    }

    @Test
    void testDecryptInitialPayloadSuccess() throws TMBCommonException {
        String encryptedPayload = "jwe-payload";
        String decryptedJson = "{\"key\":\"value\"}";
        InitializationCommonPaymentRequest expectedRequest = new InitializationCommonPaymentRequest();

        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSet);

        try (MockedStatic<JoseUtils> mockedJoseUtils = mockStatic(JoseUtils.class);
             MockedStatic<TMBUtils> mockedTmbUtils = mockStatic(TMBUtils.class)) {

            mockedJoseUtils.when(() -> JoseUtils.decrypt(encryptedPayload, jwkSet)).thenReturn(decryptedJson);
            mockedTmbUtils.when(() -> TMBUtils.convertStringToJavaObjWithTypeReference(anyString(), any()))
                    .thenReturn(expectedRequest);

            InitializationCommonPaymentRequest actualRequest = commonPaymentPartnerIntegrationService.decryptInitialPayload(encryptedPayload, partnerName);

            assertNotNull(actualRequest);
            assertEquals(expectedRequest, actualRequest);
        }
    }

    @Test
    void testDecryptInitialPayloadWhenDecryptionFails() throws TMBCommonException {
        String encryptedPayload = "jwe-payload";
        TMBCommonException givenException = new TMBCommonException(ResponseCode.JWE_DECRYPTION_FAILED.getCode(), ResponseCode.JWE_DECRYPTION_FAILED.getMessage(), ResponseCode.JWE_DECRYPTION_FAILED.getService(), HttpStatus.INTERNAL_SERVER_ERROR, null);

        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSet);

        try (MockedStatic<JoseUtils> mockedJoseUtils = mockStatic(JoseUtils.class)) {
            mockedJoseUtils.when(() -> JoseUtils.decrypt(encryptedPayload, jwkSet)).thenThrow(givenException);

            TMBCommonException thrownException = assertThrows(TMBCommonException.class,
                    () -> commonPaymentPartnerIntegrationService.decryptInitialPayload(encryptedPayload, partnerName));
            assertEquals(givenException.getErrorCode(), thrownException.getErrorCode());
        }
    }

    @Test
    void testDecryptInitialPayloadWhenDeserializationFails() throws TMBCommonException {
        String encryptedPayload = "jwe-payload";
        String decryptedJson = "invalid-json";

        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSet);

        try (MockedStatic<JoseUtils> mockedJoseUtils = mockStatic(JoseUtils.class);
             MockedStatic<TMBUtils> mockedTmbUtils = mockStatic(TMBUtils.class)) {

            mockedJoseUtils.when(() -> JoseUtils.decrypt(encryptedPayload, jwkSet)).thenReturn(decryptedJson);
            mockedTmbUtils.when(() -> TMBUtils.convertStringToJavaObjWithTypeReference(anyString(), any()))
                    .thenThrow(JsonProcessingException.class);

            TMBCommonException thrownException = assertThrows(TMBCommonException.class,
                    () -> commonPaymentPartnerIntegrationService.decryptInitialPayload(encryptedPayload, partnerName));
            assertEquals(ResponseCode.PAYLOAD_DESERIALIZATION_FAILED.getCode(), thrownException.getErrorCode());
        }
    }

    @Test
    void testSignDataSuccess() throws TMBCommonException, ParseException {
        String partnerName = "test-partner";
        Map<String, Object> payload = Map.of("data", "test");
        String expectedJws = "signed-jws-string";

        String privateJwkJsonString = "{\"p\":\"u-p-value\",\"kty\":\"RSA\",\"q\":\"u-q-value\",\"d\":\"u-d-value\",\"e\":\"AQAB\",\"use\":\"sig\",\"kid\":\"test-key\",\"qi\":\"u-qi-value\",\"dp\":\"u-dp-value\",\"alg\":\"RS256\",\"dq\":\"u-dq-value\",\"n\":\"u-n-value\"}";
        RSAKey privateKey = RSAKey.parse(privateJwkJsonString);
        JWKSet jwkSetWithPrivateKey = new JWKSet(privateKey);

        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSetWithPrivateKey);

        try (MockedStatic<JoseUtils> mockedJoseUtils = mockStatic(JoseUtils.class)) {
            mockedJoseUtils.when(() -> JoseUtils.generateJws(any(RSAKey.class), any(Map.class))).thenReturn(expectedJws);

            String actualJws = commonPaymentPartnerIntegrationService.signData(partnerName, payload);

            assertNotNull(actualJws);
            assertEquals(expectedJws, actualJws);
        }
    }

    @Test
    void testSignDataWhenPrivateKeyNotFound() throws TMBCommonException {
        String partnerName = "test-partner";
        Map<String, Object> payload = Map.of("data", "test");

        JWKSet jwkSetWithoutPrivateKey = new JWKSet();
        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSetWithoutPrivateKey);

        TMBCommonException thrownException = assertThrows(TMBCommonException.class,
                () -> commonPaymentPartnerIntegrationService.signData(partnerName, payload));

        assertEquals(ResponseCode.JWK_KEY_NOT_FOUND.getCode(), thrownException.getErrorCode());
    }

    @Test
    void testSignServiceResponseSuccess() throws TMBCommonException, ParseException, JsonProcessingException {
        TmbServiceResponse<String> serviceResponse = new TmbServiceResponse<>();
        serviceResponse.setData("test-data");

        Map<String, Object> responseMap = Map.of("status", Map.of(), "data", "test-data");
        String expectedJws = "signed-service-response-jws";

        String privateJwkJsonString = "{\"p\":\"u-p-value\",\"kty\":\"RSA\",\"q\":\"u-q-value\",\"d\":\"u-d-value\",\"e\":\"AQAB\",\"use\":\"sig\",\"kid\":\"test-key\",\"qi\":\"u-qi-value\",\"dp\":\"u-dp-value\",\"alg\":\"RS256\",\"dq\":\"u-dq-value\",\"n\":\"u-n-value\"}";
        RSAKey privateKey = RSAKey.parse(privateJwkJsonString);
        JWKSet jwkSetWithPrivateKey = new JWKSet(privateKey);

        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSetWithPrivateKey);
        when(objectMapper.convertValue(any(TmbServiceResponse.class), any(TypeReference.class))).thenReturn(responseMap);

        try (MockedStatic<JoseUtils> mockedJoseUtils = mockStatic(JoseUtils.class)) {
            mockedJoseUtils.when(() -> JoseUtils.generateJws(any(RSAKey.class), any(Map.class))).thenReturn(expectedJws);

            String actualJws = commonPaymentPartnerIntegrationService.signServiceResponse(partnerName, serviceResponse);

            assertNotNull(actualJws);
            assertEquals(expectedJws, actualJws);
        }
    }

    @Test
    void testSignServiceResponseWhenConversionFails() throws TMBCommonException {
        TmbServiceResponse<String> serviceResponse = new TmbServiceResponse<>();
        serviceResponse.setData("test-data");

        when(objectMapper.convertValue(any(TmbServiceResponse.class), any(TypeReference.class)))
                .thenThrow(new IllegalArgumentException("Conversion failed"));

        TMBCommonException thrownException = assertThrows(TMBCommonException.class,
                () -> commonPaymentPartnerIntegrationService.signServiceResponse(partnerName, serviceResponse));

        assertEquals(ResponseCode.PAYLOAD_SERIALIZATION_FAILED.getCode(), thrownException.getErrorCode());
    }

    @Test
    void testGetPrivateKeyForSigningWhenMultiplePrivateKeysFound() throws ParseException, TMBCommonException {
        String partnerName = "test-partner";

        String privateJwkJsonString1 = "{\"p\":\"p1\",\"kty\":\"RSA\",\"q\":\"q1\",\"d\":\"d1\",\"e\":\"AQAB\",\"use\":\"sig\",\"kid\":\"key1\",\"qi\":\"qi1\",\"dp\":\"dp1\",\"alg\":\"RS256\",\"dq\":\"dq1\",\"n\":\"n1\"}";
        String privateJwkJsonString2 = "{\"p\":\"p2\",\"kty\":\"RSA\",\"q\":\"q2\",\"d\":\"d2\",\"e\":\"AQAB\",\"use\":\"sig\",\"kid\":\"key2\",\"qi\":\"qi2\",\"dp\":\"dp2\",\"alg\":\"RS256\",\"dq\":\"dq2\",\"n\":\"n2\"}";

        RSAKey privateKey1 = RSAKey.parse(privateJwkJsonString1);
        RSAKey privateKey2 = RSAKey.parse(privateJwkJsonString2);

        List<JWK> privateKeys = Arrays.asList(privateKey1, privateKey2);
        JWKSet jwkSetWithMultiplePrivateKeys = new JWKSet(privateKeys);

        when(jwkSetProvider.getJwkSet(partnerName)).thenReturn(jwkSetWithMultiplePrivateKeys);

        TMBCommonException thrownException = assertThrows(TMBCommonException.class,
                () -> commonPaymentPartnerIntegrationService.signData(partnerName, Map.of("data", "test")));

        assertEquals(ResponseCode.JWK_KEY_NOT_FOUND.getCode(), thrownException.getErrorCode());
    }
}
