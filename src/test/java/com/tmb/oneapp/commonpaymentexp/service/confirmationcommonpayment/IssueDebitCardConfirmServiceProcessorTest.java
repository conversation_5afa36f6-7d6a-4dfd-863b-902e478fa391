package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class IssueDebitCardConfirmServiceProcessorTest {
    @InjectMocks
    private IssueDebitCardConfirmServiceProcessor issueDebitCardConfirmServiceProcessor;

    @BeforeEach
    void setUp() {
    }

    @Test
    void getProcessorType_ShouldReturnIssueDebitCard() {
        assertEquals("issue_debit_card", issueDebitCardConfirmServiceProcessor.getProcessorType());
    }

    @Test
    void confirm_ShouldReturnEmptyResponse() throws Exception {
        ConfirmationCommonPaymentResponse response = issueDebitCardConfirmServiceProcessor.executeConfirm(null, null, null);

        assertNotNull(response);
    }
}