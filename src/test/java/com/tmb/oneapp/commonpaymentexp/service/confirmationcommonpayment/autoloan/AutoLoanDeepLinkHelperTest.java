package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment.autoloan;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.client.HpExpServiceFeignClient;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.CreditCardSupplementaryInCache;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.confirmation.ActivityAutoLoanBillPayServiceEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.DeepLinkRequestInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.AutoLoanExternalConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AccountBalance;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.AutoLoanOCPResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.DeepLinkRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpAccount;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETETransaction;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2AddServiceRequest;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2AddServiceRequestResponse;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2UpdatePaymentFlagRequest;
import com.tmb.oneapp.commonpaymentexp.model.hpexpservice.K2UpdatePaymentFlagResponse;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import feign.FeignException;
import feign.Request;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_FAILURE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ACTIVITY_SUCCESS;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.ERROR_DESC;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.FAIL_REASON;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.FROM_ACCOUNT;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CORRELATION_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.HEADER_CRM_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.PAYMENT_STATUS;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.SR_NO;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant.CHANNEL;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AutoLoanDeepLinkHelperTest {
    @InjectMocks
    private AutoLoanDeepLinkHelper autoLoanDeepLinkHelper;

    @Mock
    private HpExpServiceFeignClient hpExpServiceFeignClient;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private CacheMapper cacheMapper;

    private String crmId;
    private String correlationId;
    private HttpHeaders headers;
    private CommonPaymentDraftCache draftCache;
    private AutoLoanExternalConfirmResponse autoLoanExternalConfirmResponse;
    private TopUpETETransaction topUpETETransaction;
    private TopUpETEResponse topUpETEResponse;
    private K2AddServiceRequestResponse k2AddServiceResponse;
    private TmbOneServiceResponse<K2AddServiceRequestResponse> tmbAddServiceResponse;
    private TmbOneServiceResponse<K2UpdatePaymentFlagResponse> tmbUpdatePaymentFlagResponse;

    @Captor
    private ArgumentCaptor<K2UpdatePaymentFlagRequest> updatePaymentFlagRequestCaptor;
    @Captor
    private ArgumentCaptor<ActivityAutoLoanBillPayServiceEvent> activityLogCaptor;


    @BeforeEach
    void setUp() {
        crmId = "crm-test-123";
        correlationId = UUID.randomUUID().toString();
        String ipAddress = "127.0.0.1";

        headers = new HttpHeaders();
        headers.set(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        headers.set(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.set(CommonPaymentExpConstant.HEADER_IP_ADDRESS, ipAddress);
        headers.set(CommonPaymentExpConstant.HEADER_ACCEPT_LANGUAGE, "en");
        headers.set(CommonPaymentExpConstant.HEADER_APP_VERSION, "5.12.0");

        topUpETETransaction = new TopUpETETransaction();
        topUpETETransaction.setPaymentId("pay-id-123");
        topUpETETransaction.setPaymentDateTime("2025-04-01 21:00:00");
        TopUpAccount fromAccount = new TopUpAccount();
        fromAccount.setAccountId("**********");
        fromAccount.setAccountType("SDA");
        AccountBalance balance = new AccountBalance();
        balance.setAvailable("5000.00");
        fromAccount.setBalances(balance);
        topUpETETransaction.setFromAccount(fromAccount);

        topUpETEResponse = new TopUpETEResponse();
        topUpETEResponse.setTransaction(topUpETETransaction);

        autoLoanExternalConfirmResponse = new AutoLoanExternalConfirmResponse();
        autoLoanExternalConfirmResponse.setTopUpETEResponse(topUpETEResponse);
        autoLoanExternalConfirmResponse.setAutoLoanOCPResponse(new AutoLoanOCPResponse());

        k2AddServiceResponse = new K2AddServiceRequestResponse();
        k2AddServiceResponse.setCode(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE);
        k2AddServiceResponse.setSrId("sr-id-111");
        k2AddServiceResponse.setSrNo("sr-no-222");

        K2UpdatePaymentFlagResponse k2UpdatePaymentFlagResponse = new K2UpdatePaymentFlagResponse();
        k2UpdatePaymentFlagResponse.setCode(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE);

        tmbAddServiceResponse = new TmbOneServiceResponse<>();
        tmbAddServiceResponse.setData(k2AddServiceResponse);

        tmbUpdatePaymentFlagResponse = new TmbOneServiceResponse<>();
        tmbUpdatePaymentFlagResponse.setData(k2UpdatePaymentFlagResponse);

        setupDraftCache();
    }

    private void setupDraftCache() {
        CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
        ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
        TopUpETEPaymentRequest topUpRequest = new TopUpETEPaymentRequest();
        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        PaymentInformation paymentInformation = new PaymentInformation();
        ValidationCommonPaymentRequest validateRequest = new ValidationCommonPaymentRequest();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setProductRef1("A1123456789");
        productDetail.setProductRef2("Ref2");
        productDetail.setProductRef3("Ref3");
        AmountDetail amountDetail = new AmountDetail();
        amountDetail.setAmountValue(new BigDecimal("1000.00"));
        amountDetail.setAmountLabelEn("Amount Label En");
        amountDetail.setAmountLabelTh("Amount Label Th");
        DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setTransId("transId_111_222_987654321");
        deepLinkRequest.setAmount("1000.00");

        topUpRequest.setAmount(new BigDecimal("1000.00"));
        topUpRequest.setEpayCode("epay-test-001");
        TopUpAccount fromAccount = new TopUpAccount();
        fromAccount.setAccountId("**********");
        fromAccount.setAccountType("SDA");
        topUpRequest.setFromAccount(fromAccount);
        TopUpAccount toAccount = new TopUpAccount();
        toAccount.setAccountId("**********");
        toAccount.setAccountType("HP");
        topUpRequest.setToAccount(toAccount);
        topUpRequest.setReference1("A1123456789");
        topUpRequest.setReference2("Ref2");
        topUpRequest.setReference3("Ref3");

        BillerInfoResponse billerInfo = new BillerInfoResponse();
        billerInfo.setBillerGroupType("0");
        billerInfo.setBillerCompCode(BILL_COMP_CODE_TTB_DRIVE_CAR_LOAN);
        billerInfo.setNameEn("Auto Loan Biller");
        billerInfo.setPaymentMethod("5");
        billerInfo.setBillerMethod("AUTO_LOAN_METHOD");
        masterBillerResponse.setBillerInfo(billerInfo);
        masterBillerResponse.setRef1(new ReferenceResponse());

        paymentInformation.setCompCode("comp-test-001");
        paymentInformation.setProductDetail(productDetail);
        paymentInformation.setAmountDetail(amountDetail);

        validateRequest.setCreditCard(new CreditCardValidationCommonPaymentRequest());
        validateRequest.getCreditCard().setAccountId("6789********1234");
        validateRequest.setDeposit(new DepositValidationCommonPaymentRequest());
        validateRequest.getDeposit().setAccountNumber("**********");

        paymentInformation.setCompCode("comp-test-001");
        paymentInformation.setProductDetail(productDetail);
        paymentInformation.setAmountDetail(amountDetail);

        validateDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setTopUpETEPaymentRequest(topUpRequest));
        when(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse)).thenReturn(new MasterBillerResponseInCache());
        validateDraftCache.setMasterBillerResponse(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.setFromDepositAccount(new DepositAccountInCache());
        validateDraftCache.getFromDepositAccount().setAccountNumber("**********");

        CreditCardSupplementary creditCardDetail = new CreditCardSupplementary();
        creditCardDetail.setProductNameEn("Mock Credit Card Product Name EN");
        creditCardDetail.setProductNameTh("ชื่อผลิตภัณฑ์บัตรเครดิตจำลอง");
        creditCardDetail.setCardNo("4111********1111");
        creditCardDetail.setAccountId("6789********1234");
        when(cacheMapper.toCreditCardSupplementaryInCache(creditCardDetail)).thenReturn(new CreditCardSupplementaryInCache());
        validateDraftCache.setFromCreditCardDetail(cacheMapper.toCreditCardSupplementaryInCache(creditCardDetail));
        CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig();

        cache.setCommonPaymentConfig(commonPaymentConfig);
        cache.setValidateDraftCache(validateDraftCache);
        cache.setValidateRequest(validateRequest);
        cache.setPaymentInformation(paymentInformation);
        when(cacheMapper.toDeepLinkRequestInCache(deepLinkRequest)).thenReturn(new DeepLinkRequestInCache());
        cache.setDeepLinkRequest(cacheMapper.toDeepLinkRequestInCache(deepLinkRequest));

        draftCache = cache;
    }

    @Test
    void testHandleServiceRequestWhenInstallmentPayThenReturnNull() {
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("00");

        K2AddServiceRequestResponse result = autoLoanDeepLinkHelper.handleServiceRequest(deepLinkRequest, headers, "**********", draftCache);

        assertNull(result);
        verify(hpExpServiceFeignClient, never()).addServiceRequest(anyString(), any(K2AddServiceRequest.class));
    }

    @Test
    void testHandleServiceRequestWhenSuccessThenReturnResponse() {
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setTransId("transId123");

        when(hpExpServiceFeignClient.addServiceRequest(eq(correlationId), any(K2AddServiceRequest.class))).thenReturn(tmbAddServiceResponse);

        K2AddServiceRequestResponse result = autoLoanDeepLinkHelper.handleServiceRequest(deepLinkRequest, headers, "**********", draftCache);

        assertNotNull(result);
        assertEquals(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE, result.getCode());
    }

    @Test
    void testPerformAutoLoanActivityLogWhenHasFailReasonThenSetActivityFailure() {
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setTransId("transId_111_222_987654321");

        deepLinkRequest.setAmount("1000.00");

        Map<String, String> params = new HashMap<>();
        params.put(FROM_ACCOUNT, "**********");
        params.put(HEADER_CORRELATION_ID, correlationId);
        params.put(FAIL_REASON, "Service unavailable");

        TestUtils.invokeMethod(
                autoLoanDeepLinkHelper,
                "performAutoLoanActivityLog",
                params,
                headers,
                deepLinkRequest,
                draftCache);

        verify(logEventPublisherService, times(1)).saveActivityLog(activityLogCaptor.capture());
        ActivityAutoLoanBillPayServiceEvent capturedLog = activityLogCaptor.getValue();
        assertEquals(ACTIVITY_FAILURE, capturedLog.getActivityStatus());
        assertEquals("Service unavailable", capturedLog.getFailReason());
        assertEquals("Failed", capturedLog.getResult());
    }

    @Test
    void testPerformAutoLoanActivityLogWhenNoFailReasonThenSetActivitySuccess() {
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setTransId("transId_111_222_987654321");

        deepLinkRequest.setAmount("1000.00");

        Map<String, String> params = new HashMap<>();
        params.put(FROM_ACCOUNT, "**********");
        params.put(HEADER_CORRELATION_ID, correlationId);
        params.put(SR_NO, "sr-no-222");

        TestUtils.invokeMethod(
                autoLoanDeepLinkHelper,
                "performAutoLoanActivityLog",
                params,
                headers,
                deepLinkRequest,
                draftCache);

        verify(logEventPublisherService, times(1)).saveActivityLog(activityLogCaptor.capture());
        ActivityAutoLoanBillPayServiceEvent capturedLog = activityLogCaptor.getValue();
        assertEquals(ACTIVITY_SUCCESS, capturedLog.getActivityStatus());
        assertNull(capturedLog.getFailReason());
        assertEquals("Completed", capturedLog.getResult());
        assertEquals("sr-no-222", capturedLog.getRequestNo());
    }

    @Test
    void testHandleServiceRequestWhenAddServiceRequestThrowsFeignExceptionThenLogsAndThrows() {
        Request mockRequest = mock(Request.class);
        FeignException feignException = new FeignException.InternalServerError(
                "Internal Server Error",
                mockRequest,
                null,
                Collections.emptyMap()
        );
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setTransId("transId_feign_test");

        deepLinkRequest.setAmount("1000.00");

        when(hpExpServiceFeignClient.addServiceRequest(eq(correlationId), any(K2AddServiceRequest.class)))
                .thenThrow(feignException);

        FeignException thrown = assertThrows(FeignException.class, () -> autoLoanDeepLinkHelper.handleServiceRequest(deepLinkRequest, headers, "**********", draftCache));

        assertEquals(feignException.getMessage(), thrown.getMessage());

        verify(logEventPublisherService, times(1)).saveActivityLog(activityLogCaptor.capture());
        ActivityAutoLoanBillPayServiceEvent capturedLog = activityLogCaptor.getValue();
        assertEquals(ACTIVITY_FAILURE, capturedLog.getActivityStatus());
        assertTrue(capturedLog.getFailReason().contains("service down for add service request"));
    }

    @Test
    void testHandleServiceRequestWhenJsonProcessingExceptionOccursThenLogsAndContinues() {
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setTransId("transId_json_error");

        deepLinkRequest.setAmount("1000.00");

        String fromAccount = "**********";

        JsonProcessingException mockException = new JsonProcessingException("Mock JSON error") {
        };
        try (MockedStatic<TMBUtils> mockedStatic = mockStatic(TMBUtils.class)) {
            mockedStatic.when(() -> TMBUtils.convertJavaObjectToString(deepLinkRequest))
                    .thenThrow(mockException);

            when(hpExpServiceFeignClient.addServiceRequest(eq(correlationId), any(K2AddServiceRequest.class)))
                    .thenReturn(tmbAddServiceResponse);

            K2AddServiceRequestResponse result = autoLoanDeepLinkHelper.handleServiceRequest(deepLinkRequest, headers, fromAccount, draftCache);

            verify(hpExpServiceFeignClient).addServiceRequest(eq(correlationId), any(K2AddServiceRequest.class));
            assertNotNull(result);
            assertEquals(AUTO_LOAN_SERVICE_REQUEST_STATUS_SUCCESS_CODE, result.getCode());

            mockedStatic.verify(() -> TMBUtils.convertJavaObjectToString(deepLinkRequest));
        }
    }

    @Test
    void testHandleErrorWhenServiceRequestFailsThenLogError() {
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setTransId("transId_error_test");

        deepLinkRequest.setAmount("1000.00");

        FeignException feignException = new FeignException.InternalServerError(
                "Test exception",
                mock(Request.class),
                null,
                Collections.emptyMap()
        );

        when(hpExpServiceFeignClient.addServiceRequest(eq(correlationId), any(K2AddServiceRequest.class)))
                .thenThrow(feignException);

        assertThrows(FeignException.class, () -> autoLoanDeepLinkHelper.handleServiceRequest(deepLinkRequest, headers, "**********", draftCache));

        verify(logEventPublisherService, times(1)).saveActivityLog(activityLogCaptor.capture());
        ActivityAutoLoanBillPayServiceEvent capturedLog = activityLogCaptor.getValue();
        assertEquals(ACTIVITY_FAILURE, capturedLog.getActivityStatus());
    }

    @Test
    void testHandleServiceRequestWhenFeignExceptionThenThrowException() {
        Request mockRequest = mock(Request.class);
        FeignException feignServiceUnavailableException = new FeignException.ServiceUnavailable(
                "Service unavailable",
                mockRequest,
                null,
                Collections.emptyMap()
        );
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setTransId("transId123");

        deepLinkRequest.setAmount("1000.00");

        when(hpExpServiceFeignClient.addServiceRequest(eq(correlationId), any(K2AddServiceRequest.class)))
                .thenThrow(feignServiceUnavailableException);

        FeignException exception = assertThrows(FeignException.class, () -> autoLoanDeepLinkHelper.handleServiceRequest(deepLinkRequest, headers, "**********", draftCache));

        assertEquals("Service unavailable", exception.getMessage());
    }

    @Test
    void testHandleServiceRequestWhenResponseNotSuccessThenReturnNull() {
        DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
        deepLinkRequest.setCallFrom("autoLoan");
        deepLinkRequest.setTransType("01");
        deepLinkRequest.setTransId("transId123");

        deepLinkRequest.setAmount("1000.00");

        K2AddServiceRequestResponse failResponse = new K2AddServiceRequestResponse();
        failResponse.setCode("FAIL_CODE");
        failResponse.setSrId("sr-id-111");
        failResponse.setSrNo("sr-no-222");

        TmbOneServiceResponse<K2AddServiceRequestResponse> tmbFailResponse = new TmbOneServiceResponse<>();
        tmbFailResponse.setData(failResponse);

        when(hpExpServiceFeignClient.addServiceRequest(eq(correlationId), any(K2AddServiceRequest.class)))
                .thenReturn(tmbFailResponse);

        K2AddServiceRequestResponse result = autoLoanDeepLinkHelper.handleServiceRequest(deepLinkRequest, headers, "**********", draftCache);

        assertNull(result);
    }

    @Nested
    class HandleUpdatePaymentStatusTest {
        @Test
        void testHandleUpdatePaymentStatusWhenSuccessThenCallLogEventPublisher() {
            DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
            deepLinkRequest.setCallFrom("autoLoan");
            deepLinkRequest.setTransType("01");
            deepLinkRequest.setTransId("transId123");

            deepLinkRequest.setAmount("1000.00");

            Map<String, String> params = new HashMap<>();
            params.put(FROM_ACCOUNT, "**********");
            params.put(HEADER_CRM_ID, crmId);
            params.put(HEADER_CORRELATION_ID, correlationId);
            params.put(PAYMENT_STATUS, "01");

            when(hpExpServiceFeignClient.updatePaymentFlag(any(HttpHeaders.class), any(K2UpdatePaymentFlagRequest.class))).thenReturn(tmbUpdatePaymentFlagResponse);

            assertDoesNotThrow(() -> autoLoanDeepLinkHelper.handleUpdatePaymentStatus(params, headers, deepLinkRequest, autoLoanExternalConfirmResponse, k2AddServiceResponse, draftCache));
        }

        @Test
        void testHandleUpdatePaymentStatusWhenFailThenAddFailReason() {
            DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
            deepLinkRequest.setCallFrom("autoLoan");
            deepLinkRequest.setTransType("01");

            deepLinkRequest.setAmount("1000.00");

            Map<String, String> params = new HashMap<>();
            params.put(FROM_ACCOUNT, "**********");
            params.put(HEADER_CRM_ID, crmId);
            params.put(HEADER_CORRELATION_ID, correlationId);
            params.put(PAYMENT_STATUS, "02");
            params.put(ERROR_DESC, "Payment failed");

            k2AddServiceResponse.setCode("FAILED");

            assertDoesNotThrow(() -> autoLoanDeepLinkHelper.handleUpdatePaymentStatus(params, headers, deepLinkRequest, autoLoanExternalConfirmResponse, k2AddServiceResponse, draftCache));

            verify(hpExpServiceFeignClient, never()).updatePaymentFlag(any(), any());
        }

        @Test
        void testHandleUpdatePaymentStatusWhenJsonProcessingExceptionOccursThenAddUnexpectedErrorReason() {
            DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
            deepLinkRequest.setCallFrom("autoLoan");
            deepLinkRequest.setTransType("01");
            deepLinkRequest.setTransId("trans_id");
            deepLinkRequest.setAmount("1000.00");

            Map<String, String> params = new HashMap<>();
            params.put(FROM_ACCOUNT, "**********");
            params.put(HEADER_CRM_ID, crmId);
            params.put(HEADER_CORRELATION_ID, correlationId);
            params.put(PAYMENT_STATUS, "01");

            headers.set(CHANNEL, "mb");
            headers.set("device-id", "test-device");
            headers.set("app-version", "1.0.0");

            JsonProcessingException mockException = new JsonProcessingException("Mock JSON error for update status") {
            };
            try (MockedStatic<TMBUtils> mockedStatic = mockStatic(TMBUtils.class)) {
                mockedStatic.when(() -> TMBUtils.convertJavaObjectToString(any(K2AddServiceRequestResponse.class)))
                        .thenReturn("{\"code\":\"0000\",\"srId\":\"sr-id-111\",\"srNo\":\"sr-no-222\"}");
                if (topUpETETransaction != null) {
                    mockedStatic.when(() -> TMBUtils.convertJavaObjectToString(topUpETETransaction))
                            .thenReturn("{\"paymentId\":\"pay-id-123\"}");
                }
                mockedStatic.when(() -> TMBUtils.convertJavaObjectToString(any(K2UpdatePaymentFlagRequest.class)))
                        .thenThrow(mockException);


                autoLoanDeepLinkHelper.handleUpdatePaymentStatus(params, headers, deepLinkRequest, autoLoanExternalConfirmResponse, k2AddServiceResponse, draftCache);

                mockedStatic.verify(() -> TMBUtils.convertJavaObjectToString(any(K2UpdatePaymentFlagRequest.class)));
            }
        }

        @Test
        void testHandleUpdatePaymentStatusWhenUpdateFlagResponseNotSuccessThenAddCannotUpdateReason() {
            DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
            deepLinkRequest.setCallFrom("autoLoan");
            deepLinkRequest.setTransId("transId_error_test");

            deepLinkRequest.setAmount("1000.00");

            Map<String, String> params = new HashMap<>();
            params.put(FROM_ACCOUNT, "**********");
            params.put(HEADER_CRM_ID, crmId);
            params.put(HEADER_CORRELATION_ID, correlationId);
            params.put(PAYMENT_STATUS, "01");

            headers.set(CHANNEL, "mb");
            headers.set("device-id", "test-device");
            headers.set("app-version", "1.0.0");

            K2UpdatePaymentFlagResponse failUpdateResponse = new K2UpdatePaymentFlagResponse();
            failUpdateResponse.setCode("9999");
            TmbOneServiceResponse<K2UpdatePaymentFlagResponse> tmbFailUpdateResponse = new TmbOneServiceResponse<>();
            tmbFailUpdateResponse.setData(failUpdateResponse);

            when(hpExpServiceFeignClient.updatePaymentFlag(any(HttpHeaders.class), any(K2UpdatePaymentFlagRequest.class))).thenReturn(tmbFailUpdateResponse);

            assertDoesNotThrow(() -> autoLoanDeepLinkHelper.handleUpdatePaymentStatus(params, headers, deepLinkRequest, autoLoanExternalConfirmResponse, k2AddServiceResponse, draftCache));

            verify(logEventPublisherService, times(1)).saveActivityLog(any(ActivityAutoLoanBillPayServiceEvent.class));

        }

        @Test
        void testHandleUpdatePaymentStatusWhenFeignExceptionThenAddFailReasonWithServiceDown() {
            Request mockRequest = mock(Request.class);
            FeignException feignServiceUnavailableException = new FeignException.ServiceUnavailable(
                    "Service unavailable",
                    mockRequest,
                    null,
                    Collections.emptyMap()
            );

            DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
            deepLinkRequest.setCallFrom("autoLoan");
            deepLinkRequest.setTransType("01");
            deepLinkRequest.setTransId("transId_111_222_987654321");

            deepLinkRequest.setAmount("1000.00");

            Map<String, String> params = new HashMap<>();
            params.put(FROM_ACCOUNT, "**********");
            params.put(HEADER_CRM_ID, crmId);
            params.put(HEADER_CORRELATION_ID, correlationId);
            params.put(PAYMENT_STATUS, "01");

            when(hpExpServiceFeignClient.updatePaymentFlag(any(HttpHeaders.class), any(K2UpdatePaymentFlagRequest.class))).thenThrow(feignServiceUnavailableException);

            assertDoesNotThrow(() -> autoLoanDeepLinkHelper.handleUpdatePaymentStatus(params, headers, deepLinkRequest, autoLoanExternalConfirmResponse, k2AddServiceResponse, draftCache));

            verify(logEventPublisherService, times(1)).saveActivityLog(activityLogCaptor.capture());
            ActivityAutoLoanBillPayServiceEvent capturedLog = activityLogCaptor.getValue();
            assertEquals(ACTIVITY_FAILURE, capturedLog.getActivityStatus());
            assertTrue(capturedLog.getFailReason().contains("service down for update payment flag"));
        }

        @Test
        void testHandleUpdatePaymentStatusWhenNoPaymentTransactionThenSetCurrentDate() {
            String paymentDateTimeNull = null;
            topUpETEResponse.getTransaction().setPaymentDateTime(paymentDateTimeNull);

            DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
            deepLinkRequest.setCallFrom("autoLoan");
            deepLinkRequest.setTransType("01");
            deepLinkRequest.setTransId("transId_111_222_987654321");

            deepLinkRequest.setAmount("1000.00");

            Map<String, String> params = new HashMap<>();
            params.put(FROM_ACCOUNT, "**********");
            params.put(HEADER_CORRELATION_ID, correlationId);
            params.put(PAYMENT_STATUS, "01");

            when(hpExpServiceFeignClient.updatePaymentFlag(any(HttpHeaders.class), updatePaymentFlagRequestCaptor.capture())).thenReturn(tmbUpdatePaymentFlagResponse);

            assertDoesNotThrow(() -> autoLoanDeepLinkHelper.handleUpdatePaymentStatus(params, headers, deepLinkRequest, autoLoanExternalConfirmResponse, k2AddServiceResponse, draftCache));

            assertNotNull(updatePaymentFlagRequestCaptor.getValue().getPaymentDateTime());
        }

        @Test
        void testHandleUpdatePaymentStatusWhenFailedStatusThenSetPaymentStatusDesc() {
            DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
            deepLinkRequest.setCallFrom("autoLoan");
            deepLinkRequest.setTransType("01");
            deepLinkRequest.setTransId("transId_111_222_987654321");

            deepLinkRequest.setAmount("1000.00");

            Map<String, String> params = new HashMap<>();
            params.put(FROM_ACCOUNT, "**********");
            params.put(HEADER_CRM_ID, crmId);
            params.put(HEADER_CORRELATION_ID, correlationId);
            params.put(PAYMENT_STATUS, "02");
            params.put(ERROR_DESC, "Payment failed with error");

            when(hpExpServiceFeignClient.updatePaymentFlag(any(HttpHeaders.class), any(K2UpdatePaymentFlagRequest.class)))
                    .thenReturn(tmbUpdatePaymentFlagResponse);


            assertDoesNotThrow(() -> autoLoanDeepLinkHelper.handleUpdatePaymentStatus(params, headers, deepLinkRequest, autoLoanExternalConfirmResponse, k2AddServiceResponse, draftCache));

            verify(hpExpServiceFeignClient, times(1)).updatePaymentFlag(any(HttpHeaders.class), updatePaymentFlagRequestCaptor.capture());
            K2UpdatePaymentFlagRequest capturedRequest = updatePaymentFlagRequestCaptor.getValue();
            assertEquals("02", capturedRequest.getPaymentStatus());
            assertEquals("Payment failed with error", capturedRequest.getPaymentStatusDesc());
        }
    }

    @Nested
    class UpdateAutoLoanPaymentStatusSuccessTest {
        @Test
        void testHandleUpdatePaymentStatusWhenSuccessThenCallLogEventPublisher() {
            DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
            deepLinkRequest.setCallFrom("autoLoan");
            deepLinkRequest.setTransType("01");
            deepLinkRequest.setTransId("transId123");

            deepLinkRequest.setAmount("1000.00");

            Map<String, String> params = new HashMap<>();
            params.put(FROM_ACCOUNT, "**********");
            params.put(HEADER_CRM_ID, crmId);
            params.put(HEADER_CORRELATION_ID, correlationId);
            params.put(PAYMENT_STATUS, "01");

            when(hpExpServiceFeignClient.updatePaymentFlag(any(HttpHeaders.class), updatePaymentFlagRequestCaptor.capture())).thenReturn(tmbUpdatePaymentFlagResponse);

            assertDoesNotThrow(() -> autoLoanDeepLinkHelper.updateAutoLoanPaymentStatusSuccess(headers, draftCache, autoLoanExternalConfirmResponse, k2AddServiceResponse));

            String expectShouldNotHasAnyError = updatePaymentFlagRequestCaptor.getValue().getPaymentStatusDesc();
            assertNull(expectShouldNotHasAnyError);
        }

        @Test
        void testHandleUpdateWowPaymentStatusWhenSuccessThenCallLogEventPublisher() {
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);

            WowPointRedeemConfirmRequest wowConfirmRequest = new WowPointRedeemConfirmRequest();
            wowConfirmRequest.setAmount(BigDecimal.valueOf(1200));
            wowConfirmRequest.setTxnAmount(BigDecimal.valueOf(1150));

            draftCache.setCommonPaymentRule(commonPaymentRule);
            draftCache.getValidateRequest().setWowPoint(new WowPointValidationCommonPaymentRequest());
            draftCache.getValidateDraftCache().setWowPointRedeemConfirmRequest(wowConfirmRequest);

            DeepLinkRequestInCache deepLinkRequest = new DeepLinkRequestInCache();
            deepLinkRequest.setCallFrom("autoLoan");
            deepLinkRequest.setTransType("01");
            deepLinkRequest.setTransId("transId123");

            deepLinkRequest.setAmount("1000.00");
            OCPBillRequest ocpBillRequest = new OCPBillRequest();
            ocpBillRequest.setPaymentId("**********");
            draftCache.getValidateDraftCache().setExternalConfirmRequest(new ExternalConfirmRequest().setOcpBillPaymentConfirmRequest(ocpBillRequest));

            Map<String, String> params = new HashMap<>();
            params.put(FROM_ACCOUNT, "**********");
            params.put(HEADER_CRM_ID, crmId);
            params.put(HEADER_CORRELATION_ID, correlationId);
            params.put(PAYMENT_STATUS, "01");

            AutoLoanOCPResponse autoLoanResponse = new AutoLoanOCPResponse();
            autoLoanResponse.setPaymentId("**********");

            autoLoanExternalConfirmResponse.setAutoLoanOCPResponse(autoLoanResponse);

            when(hpExpServiceFeignClient.updatePaymentFlag(any(HttpHeaders.class), updatePaymentFlagRequestCaptor.capture())).thenReturn(tmbUpdatePaymentFlagResponse);

            assertDoesNotThrow(() -> autoLoanDeepLinkHelper.updateAutoLoanPaymentStatusSuccess(headers, draftCache, autoLoanExternalConfirmResponse, k2AddServiceResponse));

            String expectShouldNotHasAnyError = updatePaymentFlagRequestCaptor.getValue().getPaymentStatusDesc();
            assertNull(expectShouldNotHasAnyError);
        }
    }
}

