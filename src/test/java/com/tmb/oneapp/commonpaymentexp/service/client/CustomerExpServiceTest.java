package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.CustomerExpServiceClient;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardResponse;
import feign.FeignException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CustomerExpServiceTest {

    @Mock
    private CustomerExpServiceClient customerExpServiceClient;

    @InjectMocks
    private CustomerExpService customerExpService;

    private static final String CORRELATION_ID = "correlationId123";
    private static final String CRM_ID = "crmId123";
    private static final String ACCEPT_LANGUAGE = "en";
    private static final String APP_VERSION = "1.0.0";

    private TmbServiceResponse<CreditCardResponse> creditCardResponse;
    private TmbServiceResponse<Void> deleteCacheResponse;

    @BeforeEach
    void setUp() {
        creditCardResponse = new TmbServiceResponse<>();
        Status status = new Status();
        status.setCode("0000");
        status.setMessage("Success");
        status.setService("customer-exp-service");
        creditCardResponse.setStatus(status);
        creditCardResponse.setData(new CreditCardResponse());

        deleteCacheResponse = new TmbServiceResponse<>();
        deleteCacheResponse.setStatus(status);
    }

    @Test
    void testGetCustomerCreditCardsWhenCalledShouldReturnCreditCardResponse() {
        when(customerExpServiceClient.getAccountsCreditCard(CORRELATION_ID, CRM_ID))
                .thenReturn(ResponseEntity.ok(creditCardResponse));

        TmbServiceResponse<CreditCardResponse> result = customerExpService.getCustomerCreditCards(CORRELATION_ID, CRM_ID);

        assertNotNull(result);
        assertEquals("0000", result.getStatus().getCode());
        assertEquals("Success", result.getStatus().getMessage());
        verify(customerExpServiceClient, times(1)).getAccountsCreditCard(CORRELATION_ID, CRM_ID);
    }

    @Test
    void testGetCustomerCreditCardsAsyncWhenCalledShouldReturnCompletableFuture() throws ExecutionException, InterruptedException {
        when(customerExpServiceClient.getAccountsCreditCard(CORRELATION_ID, CRM_ID))
                .thenReturn(ResponseEntity.ok(creditCardResponse));

        CompletableFuture<TmbServiceResponse<CreditCardResponse>> future =
                customerExpService.getCustomerCreditCardsAsync(CORRELATION_ID, CRM_ID);
        TmbServiceResponse<CreditCardResponse> result = future.get();

        assertNotNull(result);
        assertEquals("0000", result.getStatus().getCode());
        assertEquals("Success", result.getStatus().getMessage());
        verify(customerExpServiceClient, times(1)).getAccountsCreditCard(CORRELATION_ID, CRM_ID);
    }

    @Test
    void testDeleteCreditCardCacheWhenCalledShouldReturnSuccessResponse() {
        when(customerExpServiceClient.deleteCreditCardCache(CORRELATION_ID, ACCEPT_LANGUAGE, APP_VERSION, CRM_ID))
                .thenReturn(ResponseEntity.ok(deleteCacheResponse));

        assertDoesNotThrow(() -> customerExpService.deleteCreditCardCache(
                CORRELATION_ID, ACCEPT_LANGUAGE, APP_VERSION, CRM_ID));

        verify(customerExpServiceClient, times(1))
                .deleteCreditCardCache(CORRELATION_ID, ACCEPT_LANGUAGE, APP_VERSION, CRM_ID);
    }

    @Test
    void testDeleteCreditCardCacheWhenFailedFeignExceptionShouldDoesNotThrow() {
        when(customerExpServiceClient.deleteCreditCardCache(CORRELATION_ID, ACCEPT_LANGUAGE, APP_VERSION, CRM_ID))
                .thenThrow(FeignException.class);

        assertDoesNotThrow(() -> customerExpService.deleteCreditCardCache(
                CORRELATION_ID, ACCEPT_LANGUAGE, APP_VERSION, CRM_ID));
    }

    @Test
    void testGetCustomerCreditCardsWhenClientReturnsErrorShouldPropagateError() {
        TmbServiceResponse<CreditCardResponse> errorResponse = new TmbServiceResponse<>();
        Status errorStatus = new Status();
        errorStatus.setCode("1000");
        errorStatus.setMessage("Error");
        errorStatus.setService("customer-exp-service");
        errorResponse.setStatus(errorStatus);

        when(customerExpServiceClient.getAccountsCreditCard(CORRELATION_ID, CRM_ID))
                .thenReturn(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse));

        TmbServiceResponse<CreditCardResponse> result = customerExpService.getCustomerCreditCards(CORRELATION_ID, CRM_ID);

        assertNotNull(result);
        assertEquals("1000", result.getStatus().getCode());
        assertEquals("Error", result.getStatus().getMessage());
    }
}