package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.common.model.creditcard.CardStatus;
import com.tmb.oneapp.commonpaymentexp.client.RetailLendingBizClient;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CreditCardPointMapper;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.CreditCardPoint;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardFormatedResponse;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.function.Supplier;

import static com.tmb.oneapp.commonpaymentexp.service.AccountCreditCardService.DATE_INVALID_EMPTY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AccountCreditCardServiceTest {
    @InjectMocks
    AccountCreditCardService accountCreditCardService;

    @Mock
    FeignClientHelper feignClientHelper;
    @Mock
    RetailLendingBizClient retailLendingBizClient;
    
    @Mock
    CreditCardPointMapper creditCardPointMapper;

    String crmId;
    String correlationId;
    String accountId;
    HttpHeaders headers;
    CreditCardFormatedResponse creditCardFormatedResponse;

    @BeforeEach
    void setUp() {
        accountCreditCardService = new AccountCreditCardService(feignClientHelper, retailLendingBizClient, creditCardPointMapper);
        crmId = "crmId";
    }

    @Test
    void testGetCreditCardAccounts_WhenGetOnlyCreditCard_ShouldReturnCreditCardAccountSuccess() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();
        mockGetAccountCreditCardReturnTwoAccount();

        List<CreditCardSupplementary> actual = accountCreditCardService.getCreditCardAccounts(correlationId, crmId);

        assertEquals(2, actual.size());
    }

    @Test
    void testGetCreditCardAccounts_WhenGetCreditCardPendingForActivation_ShouldThrowTMBCommonException() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();
        mockGetCreditCareReturnPendingForActivation();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCreditCardService.getCreditCardAccounts(correlationId, crmId));

        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_CREDIT_CARD_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardAccounts_WhenGetCreditCardInactive_ShouldThrowTMBCommonException() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();
        mockGetCreditCardReturnCardFlagInactive();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCreditCardService.getCreditCardAccounts(correlationId, crmId));

        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_CREDIT_CARD_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardAccounts_WhenGetCreditCardReturnAccountStatusInactive_ShouldThrowTMBCommonException() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();
        mockGetCreditCardReturnAccountStatusInactive();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCreditCardService.getCreditCardAccounts(correlationId, crmId));

        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_CREDIT_CARD_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardAccountsWithAccountId_WhenAccountMatching_ShouldReturnTheCreditCardAccountSuccess() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequestOrElseThrow();
        mockGetAccountCreditCardReturnTwoAccount();

        CreditCardSupplementary actual = accountCreditCardService.getCreditCardAccountWithAccountId(correlationId, crmId, accountId);

        assertNotNull(actual);
    }

    @Test
    void testGetCreditCardAccountsWithAccountId_WhenAccountNotMatched_ShouldThrowTMBCommonException() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequestOrElseThrow();
        mockGetAccountCreditCardReturnTwoAccount();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCreditCardService.getCreditCardAccountWithAccountId(correlationId, crmId, "incorrect-account-id"));

        assertEquals(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardAccountsWithAccountId_WhenFailedFeignExceptionGetCreditCardWithSupplementary_ShouldThrowTMBCommonException() throws TMBCommonException {
        when(retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null)).thenThrow(FeignException.class);
        setUpFeignClientHelperExecuteRequestOrElseThrow();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCreditCardService.getCreditCardAccountWithAccountId(correlationId, crmId, "incorrect-account-id"));

        assertEquals(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardAccounts_WhenGetCreditCardInvalidPreviousExpiryDate_ShouldThrowTMBCommonException() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();
        mockGetAccountCreditCardReturnInvalidPreviousExpiryDate();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCreditCardService.getCreditCardAccounts(correlationId, crmId));

        assertEquals(ResponseCode.NO_ELIGIBLE_CREDIT_CARD_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    private void mockGetAccountCreditCardReturnInvalidPreviousExpiryDate() {
        CreditCardSupplementary c = new CreditCardSupplementary();
        c.setDueAmount(new BigDecimal(0));
        c.setCreditSpend(new BigDecimal(0));
        c.setCreditRemain(new BigDecimal(0));
        c.setShortcuts(null);
        c.setPointRemain(new BigDecimal(0));
        c.setCreditLimit(new BigDecimal(0));
        c.setAllowFromForBillPayTopUpEpayment("1");
        c.setCardStatus(new CardStatus());
        c.getCardStatus().setAccountStatus("000");
        c.getCardStatus().setCardActiveFlag("ACTIVE");
        c.getCardStatus().setPreviousExpiryDate("0000");
        c.getCardStatus().setActivatedDate(DATE_INVALID_EMPTY);

        Calendar cal = Calendar.getInstance();
        DecimalFormat mFormat = new DecimalFormat("00");
        int currentYearMonth = Integer.parseInt(mFormat.format(cal.get(Calendar.YEAR)).substring(2, 4) + mFormat.format(cal.get(Calendar.MONTH) + 1)) + 1;
        c.getCardStatus().setPreviousExpiryDate(String.valueOf(currentYearMonth - 1));

        CreditCardFormatedResponse response = new CreditCardFormatedResponse();
        response.setCreditCards(List.of(c));

        TmbServiceResponse<CreditCardFormatedResponse> t = new TmbServiceResponse<>();
        t.setData(response);
        t.setStatus(CommonServiceUtils.getStatusSuccess());
        when(retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null)).thenReturn(ResponseEntity.ok(t));
    }

    private void setUpFeignClientHelperExecuteRequest() throws TMBCommonException {
        when(feignClientHelper.executeRequest(any())).thenAnswer(invocation -> {
            try {
                Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
                ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
                return feignResponseEntity.getBody().getData();
            } catch (Exception e) {
                throw new TMBCommonException("");
            }
        });
    }

    private void setUpFeignClientHelperExecuteRequestOrElseThrow() throws TMBCommonException {
        when(feignClientHelper.executeRequestOrElseThrow(any(), any())).thenAnswer(invocation -> {
            try {
                Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
                ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
                return feignResponseEntity.getBody().getData();
            } catch (Exception e) {
                Supplier<TMBCommonException> exceptionSupplier = invocation.getArgument(1);
                throw exceptionSupplier.get();
            }
        });
    }

    private void mockGetAccountCreditCardReturnTwoAccount() {
        CreditCardSupplementary c1 = new CreditCardSupplementary();
        c1.setDueAmount(new BigDecimal(0));
        c1.setCreditSpend(new BigDecimal(0));
        c1.setCreditRemain(new BigDecimal(0));
        c1.setShortcuts(null);
        c1.setPointRemain(new BigDecimal(0));
        c1.setCreditLimit(new BigDecimal(0));
        c1.setAllowFromForBillPayTopUpEpayment("1");
        c1.setCardStatus(new CardStatus());
        c1.getCardStatus().setAccountStatus("000");
        c1.getCardStatus().setCardActiveFlag("ACTIVE");
        c1.getCardStatus().setPreviousExpiryDate("0000");
        c1.getCardStatus().setActivatedDate("2025-01-01");
        c1.setCardType("PRI");
        c1.setAccountId(accountId);

        CreditCardSupplementary c2 = new CreditCardSupplementary();
        c2.setDueAmount(new BigDecimal(0));
        c2.setCreditSpend(new BigDecimal(0));
        c2.setCreditRemain(new BigDecimal(0));
        c2.setShortcuts(null);
        c2.setPointRemain(new BigDecimal(0));
        c2.setCreditLimit(new BigDecimal(0));
        c2.setAllowFromForBillPayTopUpEpayment("1");
        c2.setCardStatus(new CardStatus());
        c2.getCardStatus().setAccountStatus("000");
        c2.getCardStatus().setCardActiveFlag("ACTIVE");
        c2.getCardStatus().setPreviousExpiryDate("0000");
        c2.getCardStatus().setActivatedDate("2025-01-01");
        c2.setCardType("PRI");

        CreditCardFormatedResponse response = new CreditCardFormatedResponse();
        response.setCreditCards(List.of(c1, c2));

        TmbServiceResponse<CreditCardFormatedResponse> t = new TmbServiceResponse<>();
        t.setData(response);
        t.setStatus(CommonServiceUtils.getStatusSuccess());
        when(retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null)).thenReturn(ResponseEntity.ok(t));
    }

    private void mockGetCreditCareReturnPendingForActivation() {
        CreditCardSupplementary creditCardAccount = new CreditCardSupplementary();
        creditCardAccount.setDueAmount(new BigDecimal(0));
        creditCardAccount.setCreditSpend(new BigDecimal(0));
        creditCardAccount.setCreditRemain(new BigDecimal(0));
        creditCardAccount.setShortcuts(null);
        creditCardAccount.setPointRemain(new BigDecimal(0));
        creditCardAccount.setCreditLimit(new BigDecimal(0));
        creditCardAccount.setAllowFromForBillPayTopUpEpayment("1");
        creditCardAccount.setCardStatus(new CardStatus());
        creditCardAccount.getCardStatus().setAccountStatus("000");
        creditCardAccount.getCardStatus().setCardActiveFlag("ACTIVE");
        creditCardAccount.getCardStatus().setPreviousExpiryDate("0000");
        creditCardAccount.getCardStatus().setActivatedDate(DATE_INVALID_EMPTY);
        creditCardAccount.setCardType("SUP");

        CreditCardFormatedResponse response = new CreditCardFormatedResponse();
        response.setCreditCards(List.of(creditCardAccount));

        TmbServiceResponse<CreditCardFormatedResponse> t = new TmbServiceResponse<>();
        t.setData(response);
        t.setStatus(CommonServiceUtils.getStatusSuccess());
        when(retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null)).thenReturn(ResponseEntity.ok(t));
    }

    private void mockGetCreditCardReturnCardFlagInactive() {
        String cardFlag = "INACTIVE";
        String accountStatusActive = "000";
        CreditCardSupplementary creditCardAccount = new CreditCardSupplementary();
        creditCardAccount.setDueAmount(new BigDecimal(0));
        creditCardAccount.setCreditSpend(new BigDecimal(0));
        creditCardAccount.setCreditRemain(new BigDecimal(0));
        creditCardAccount.setShortcuts(null);
        creditCardAccount.setPointRemain(new BigDecimal(0));
        creditCardAccount.setCreditLimit(new BigDecimal(0));
        creditCardAccount.setAllowFromForBillPayTopUpEpayment("1");
        creditCardAccount.setCardStatus(new CardStatus());
        creditCardAccount.getCardStatus().setAccountStatus(accountStatusActive);
        creditCardAccount.getCardStatus().setCardActiveFlag(cardFlag);
        creditCardAccount.getCardStatus().setPreviousExpiryDate("0000");
        creditCardAccount.getCardStatus().setActivatedDate("2022-01-02");
        creditCardAccount.setCardType("PRI");

        CreditCardFormatedResponse response = new CreditCardFormatedResponse();
        response.setCreditCards(List.of(creditCardAccount));


        TmbServiceResponse<CreditCardFormatedResponse> t = new TmbServiceResponse<>();
        t.setData(response);
        t.setStatus(CommonServiceUtils.getStatusSuccess());
        when(retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null)).thenReturn(ResponseEntity.ok(t));
    }

    private void mockGetCreditCardReturnAccountStatusInactive() {
        String accountStatusInactive = "001";
        CreditCardSupplementary creditCardAccount = new CreditCardSupplementary();
        creditCardAccount.setDueAmount(new BigDecimal(0));
        creditCardAccount.setCreditSpend(new BigDecimal(0));
        creditCardAccount.setCreditRemain(new BigDecimal(0));
        creditCardAccount.setShortcuts(null);
        creditCardAccount.setPointRemain(new BigDecimal(0));
        creditCardAccount.setCreditLimit(new BigDecimal(0));
        creditCardAccount.setAllowFromForBillPayTopUpEpayment("1");
        creditCardAccount.setCardStatus(new CardStatus());
        creditCardAccount.getCardStatus().setAccountStatus(accountStatusInactive);
        creditCardAccount.getCardStatus().setCardActiveFlag("ACTIVE");
        creditCardAccount.getCardStatus().setPreviousExpiryDate("0000");
        creditCardAccount.getCardStatus().setActivatedDate("2023-01-01");
        creditCardAccount.setCardType("PRI");

        CreditCardFormatedResponse response = new CreditCardFormatedResponse();
        response.setCreditCards(List.of(creditCardAccount));

        TmbServiceResponse<CreditCardFormatedResponse> t = new TmbServiceResponse<>();
        t.setData(response);
        t.setStatus(CommonServiceUtils.getStatusSuccess());
        when(retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null)).thenReturn(ResponseEntity.ok(t));
    }

    @Test
    void testGetCreditCardAccounts_WhenAllowFromForBillPayTopUpEpaymentNotOne_ShouldThrowTMBCommonException() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();

        CreditCardSupplementary card = createValidCreditCard();
        card.setAllowFromForBillPayTopUpEpayment("0");

        CreditCardFormatedResponse response = new CreditCardFormatedResponse();
        response.setCreditCards(List.of(card));

        TmbServiceResponse<CreditCardFormatedResponse> t = new TmbServiceResponse<>();
        t.setData(response);
        t.setStatus(CommonServiceUtils.getStatusSuccess());
        when(retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null)).thenReturn(ResponseEntity.ok(t));

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCreditCardService.getCreditCardAccounts(correlationId, crmId));

        assertEquals(ResponseCode.NO_ELIGIBLE_CREDIT_CARD_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardAccounts_WhenCardNotExpired_ShouldReturnCardSuccess() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();

        CreditCardSupplementary card = createValidCreditCard();

        Calendar cal = Calendar.getInstance();
        DecimalFormat mFormat = new DecimalFormat("00");
        String nextYear = mFormat.format(cal.get(Calendar.YEAR) + 1).substring(2, 4);
        String currentMonth = mFormat.format(cal.get(Calendar.MONTH) + 1);
        card.getCardStatus().setPreviousExpiryDate(nextYear + currentMonth);

        CreditCardFormatedResponse response = new CreditCardFormatedResponse();
        response.setCreditCards(List.of(card));

        TmbServiceResponse<CreditCardFormatedResponse> t = new TmbServiceResponse<>();
        t.setData(response);
        t.setStatus(CommonServiceUtils.getStatusSuccess());
        when(retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null)).thenReturn(ResponseEntity.ok(t));

        List<CreditCardSupplementary> result = accountCreditCardService.getCreditCardAccounts(correlationId, crmId);

        assertEquals(1, result.size());
    }

    @Test
    void testGetCreditCardAccounts_WhenMultipleCards_ShouldSortByProductOrderAndCardNo() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();

        CreditCardSupplementary card1 = createValidCreditCard();
        card1.setProductOrder("2");
        card1.setCardNo("1234");

        CreditCardSupplementary card2 = createValidCreditCard();
        card2.setProductOrder("1");
        card2.setCardNo("5678");

        CreditCardSupplementary card3 = createValidCreditCard();
        card3.setProductOrder("1");
        card3.setCardNo("1234");

        CreditCardFormatedResponse response = new CreditCardFormatedResponse();
        response.setCreditCards(List.of(card1, card2, card3));

        TmbServiceResponse<CreditCardFormatedResponse> t = new TmbServiceResponse<>();
        t.setData(response);
        t.setStatus(CommonServiceUtils.getStatusSuccess());
        when(retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null)).thenReturn(ResponseEntity.ok(t));

        List<CreditCardSupplementary> result = accountCreditCardService.getCreditCardAccounts(correlationId, crmId);

        assertEquals(3, result.size());
        assertEquals("1234", result.get(0).getCardNo());
        assertEquals("5678", result.get(1).getCardNo());
        assertEquals("1234", result.get(2).getCardNo());
        assertEquals("1", result.get(0).getProductOrder());
        assertEquals("1", result.get(1).getProductOrder());
        assertEquals("2", result.get(2).getProductOrder());
    }

    @Test
    void testGetCreditCardAccounts_WhenResponseIsNull_ShouldThrowTMBCommonException() throws TMBCommonException {
        when(feignClientHelper.executeRequest(any())).thenReturn(new CreditCardFormatedResponse());

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class,
                () -> accountCreditCardService.getCreditCardAccounts(correlationId, crmId));

        assertEquals(ResponseCode.NO_ELIGIBLE_CREDIT_CARD_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardAccounts_WhenEmptyCardList_ShouldThrowTMBCommonException() throws TMBCommonException {
        CreditCardFormatedResponse response = new CreditCardFormatedResponse();
        response.setCreditCards(new ArrayList<>());
        when(feignClientHelper.executeRequest(any())).thenReturn(response);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class,
                () -> accountCreditCardService.getCreditCardAccounts(correlationId, crmId));

        assertEquals(ResponseCode.NO_ELIGIBLE_CREDIT_CARD_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCardPointCreditCard_ShouldReturnCardPointSuccess() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();

        CreditCardSupplementary c1 = createValidCreditCard();
        c1.setPointRemain(new BigDecimal("1000.00"));

        CreditCardSupplementary c2 = createValidCreditCard();
        c2.setPointRemain(new BigDecimal("2000.00"));

        CreditCardFormatedResponse response = new CreditCardFormatedResponse();
        response.setCreditCards(List.of(c1, c2));

        TmbServiceResponse<CreditCardFormatedResponse> t = new TmbServiceResponse<>();
        t.setData(response);
        t.setStatus(CommonServiceUtils.getStatusSuccess());
        when(retailLendingBizClient.getCreditCardWithSupplementary(correlationId, crmId, null, null, null))
                .thenReturn(ResponseEntity.ok(t));

        // Mock the mapper behavior
        CreditCardPoint point1 = new CreditCardPoint();
        point1.setPointRemain("1000.00");
        CreditCardPoint point2 = new CreditCardPoint();
        point2.setPointRemain("2000.00");
        when(creditCardPointMapper.mapToCreditCardPointList(anyList()))
                .thenReturn(List.of(point1, point2));

        List<CreditCardPoint> actual = accountCreditCardService.getCardPointCreditCard(correlationId, crmId);

        assertEquals(2, actual.size());
        actual.forEach(point -> {
            assertNotNull(point.getPointRemain());
            assertTrue(new BigDecimal(point.getPointRemain()).compareTo(BigDecimal.ZERO) >= 0);
        });
    }

    private CreditCardSupplementary createValidCreditCard() {
        CreditCardSupplementary card = new CreditCardSupplementary();
        card.setDueAmount(new BigDecimal(1000));
        card.setCreditSpend(new BigDecimal(5000));
        card.setCreditRemain(new BigDecimal(45000));
        card.setShortcuts(null);
        card.setPointRemain(new BigDecimal(1000));
        card.setCreditLimit(new BigDecimal(50000));
        card.setAllowFromForBillPayTopUpEpayment("1");
        card.setCardStatus(new CardStatus());
        card.getCardStatus().setAccountStatus("000");
        card.getCardStatus().setCardActiveFlag("ACTIVE");
        card.getCardStatus().setPreviousExpiryDate("2512");
        card.getCardStatus().setActivatedDate("2023-01-01");
        card.setCardType("PRI");
        card.setAccountId(accountId);
        return card;
    }
}