package com.tmb.oneapp.commonpaymentexp.service.cache;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PhraseDetail;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RedisCacheServiceTest {

    @Mock
    private RedisTemplate<String, String> redisTemplate;

    @Mock
    private ValueOperations<String, String> valueOperations;

    @Mock
    private ObjectMapper objectWithTimeMapper;

    @Mock
    private HashOperations<String, String, String> hashOperations;

    @InjectMocks
    private RedisCacheService redisCacheService;

    @BeforeEach
    void setUp() {
        lenient().doReturn(valueOperations).when(redisTemplate).opsForValue();
        lenient().doReturn(hashOperations).when(redisTemplate).opsForHash();
    }

    @Test
    void testSetWithKeyValueAndTtlSuccess() throws JsonProcessingException {
        String key = "testKey";
        String value = "testValue";
        int ttlSecond = 60;
        String convertedValue = TMBUtils.convertJavaObjectToString(value);

        redisCacheService.set(key, value, ttlSecond);

        verify(valueOperations).set(eq(key), eq(convertedValue), eq(Duration.ofSeconds(ttlSecond)));
    }

    @Test
    void testSetWithKeyValueSuccess() throws JsonProcessingException {
        String key = "testKey";
        String value = "testValue";
        String convertedValue = TMBUtils.convertJavaObjectToString(value);

        redisCacheService.set(key, value);

        verify(valueOperations).set(eq(key), eq(convertedValue));
    }

    @Test
    void testSetWithKeyValueButGotConnectionErrorShouldThrowsException() {
        String key = "testKey";
        String value = "testValue";

        doThrow(new RuntimeException("Redis connection error"))
                .when(valueOperations).set(anyString(), anyString());

        assertThrows(RuntimeException.class, () -> redisCacheService.set(key, value));

    }

    @Test
    void testSetWithHashKeyValueAndTtlSuccess() throws JsonProcessingException {
        String key = "testKey";
        String hashKey = "testHashKey";
        String value = "testValue";
        int ttlSecond = 60;
        String convertedValue = TMBUtils.convertJavaObjectToString(value);

        redisCacheService.set(key, hashKey, value, ttlSecond);

        verify(hashOperations).put(eq(key), eq(hashKey), eq(convertedValue));
        verify(redisTemplate).expire(eq(key), eq(Duration.ofSeconds(ttlSecond)));
    }

    @Test
    void testSetWithHashKeyValueSuccess() throws JsonProcessingException {
        String key = "testKey";
        String hashKey = "testHashKey";
        String value = "testValue";
        String convertedValue = TMBUtils.convertJavaObjectToString(value);

        redisCacheService.set(key, hashKey, value);

        verify(hashOperations).put(eq(key), eq(hashKey), eq(convertedValue));
    }

    @Test
    void testSetWithHashKeyValueButGorConnectErrorShouldThrowsException() {
        String key = "testKey";
        String hashKey = "testHashKey";
        String value = "testValue";

        doThrow(RuntimeException.class).when(hashOperations).put(anyString(), anyString(), any());

        assertThrows(RuntimeException.class, () -> redisCacheService.set(key, hashKey, value));

    }

    @Test
    void testGetWithValidKeySuccessShouldNotBeNull() throws Exception {
        String key = "testKey";
        String rawData = "{\"label_en\":\"test\"}";
        PhraseDetail expectedResult = new PhraseDetail();
        expectedResult.setLabelEn("test");

        when(valueOperations.get(key)).thenReturn(rawData);
        when(objectWithTimeMapper.readValue(rawData, PhraseDetail.class)).thenReturn(expectedResult);

        PhraseDetail result = redisCacheService.get(key, PhraseDetail.class);

        assertNotNull(result);
        verify(valueOperations).get(key);
        verify(objectWithTimeMapper).readValue(rawData, PhraseDetail.class);
    }

    @Test
    void testGetWithInvalidKeyShouldThrowsTmbCommonException() {
        String key = "testKey";
        when(valueOperations.get(key)).thenThrow(new IllegalArgumentException());

        assertThrows(TMBCommonException.class, () -> redisCacheService.get(key, String.class));
    }

    @Test
    void testGetHashWithValidKey() throws Exception {
        String key = "testKey";
        String hashKey = "testHashKey";
        String rawData = "{\"label_en\":\"test\"}";
        PhraseDetail expectedResult = new PhraseDetail();
        expectedResult.setLabelEn("test");

        when(hashOperations.get(key, hashKey)).thenReturn(rawData);
        when(objectWithTimeMapper.readValue(rawData, PhraseDetail.class)).thenReturn(expectedResult);

        PhraseDetail result = redisCacheService.get(key, hashKey, PhraseDetail.class);

        assertNotNull(result);
        verify(hashOperations).get(key, hashKey);
        verify(objectWithTimeMapper).readValue(rawData, PhraseDetail.class);
    }

    @Test
    void testGetHashWithValidKeyButGotConnectionErrorShouldThrowsException() throws Exception {
        String key = "testKey";
        String hashKey = "testHashKey";
        doThrow(RuntimeException.class).when(hashOperations).get(key, hashKey);

        assertThrows(TMBCommonException.class, () -> redisCacheService.get(key, hashKey, PhraseDetail.class));
    }

    @Test
    void testGetHashWithNullDataShouldNotThrowsException() throws Exception {
        String key = "testKey";
        String hashKey = "testHashKey";
        when(hashOperations.get(key, hashKey)).thenReturn(null);

        String result = redisCacheService.get(key, hashKey, String.class);

        assertNull(result);
    }

    @Test
    void testDeleteKeySuccess() {
        String key = "testKey";
        when(redisTemplate.delete(key)).thenReturn(true);

        boolean result = redisCacheService.delete(key);

        assertTrue(result);
        verify(redisTemplate).delete(key);
    }

    @Test
    void testDeleteHashKeySuccess() {
        String key = "testKey";
        String hashKey = "testHashKey";
        when(hashOperations.delete(key, hashKey)).thenReturn(1L);

        boolean result = redisCacheService.delete(key, hashKey);

        assertTrue(result);
        verify(hashOperations).delete(key, hashKey);
    }

    @Test
    void testDeleteKeyFailedShouldReturnFalse() {
        String key = "testKey";
        when(redisTemplate.delete(key)).thenReturn(false);

        boolean result = redisCacheService.delete(key);

        assertFalse(result);
    }

    @Test
    void testDeleteHashKeyButGotConnectionErrorThrowsException() {
        String key = "testKey";
        String hashKey = "testHashKey";
        doThrow(RuntimeException.class).when(hashOperations).delete(anyString(), anyString());

        assertThrows(RuntimeException.class, () -> redisCacheService.delete(key, hashKey));
    }

    @Test
    void testDeleteKeyButGotConnectionErrorShouldThrowsException() {
        String key = "testKey";
        doThrow(RuntimeException.class).when(redisTemplate).delete(anyString());

        assertThrows(RuntimeException.class, () -> redisCacheService.delete(key));
    }

    @Test
    void testDeleteHashKeyFailedShouldReturnFalse() {
        String key = "testKey";
        String hashKey = "testHashKey";
        when(hashOperations.delete(key, hashKey)).thenReturn(0L);

        boolean result = redisCacheService.delete(key, hashKey);

        assertFalse(result);
    }

    @Test
    void testSetWithKeyValueAndTtlButCannotParseToJsonShouldThrowsJsonProcessingException() {
        String key = "testKey";
        Object value = new Object();
        int ttlSecond = 60;

        assertThrows(JsonProcessingException.class, () ->
                redisCacheService.set(key, value, ttlSecond)
        );
    }

    @Test
    void testSetWithKeyValueAndTtlButGotConnectionErrorShouldThrowsException() {
        String key = "testKey";
        String value = "testValue";
        int ttlSecond = 60;

        doThrow(new RuntimeException("Redis connection error"))
                .when(valueOperations).set(anyString(), anyString(), any(Duration.class));

        assertThrows(RuntimeException.class, () ->
                redisCacheService.set(key, value, ttlSecond)
        );
    }

    @Test
    void testSetHashWithKeyValueAndTtlButCannotParseToJsonShouldThrowsJsonProcessingException() {
        String key = "testKey";
        String hashKey = "testHashKey";
        Object value = new Object();
        int ttlSecond = 60;

        assertThrows(JsonProcessingException.class, () ->
                redisCacheService.set(key, hashKey, value, ttlSecond)
        );
    }

    @Test
    void testSetHashWithKeyValueAndTtlButGotConnectionErrorShouldThrowsException() {
        String key = "testKey";
        String hashKey = "testHashKey";
        String value = "testValue";
        int ttlSecond = 60;

        doThrow(new RuntimeException("Redis connection error"))
                .when(hashOperations).put(anyString(), anyString(), anyString());

        assertThrows(RuntimeException.class, () ->
                redisCacheService.set(key, hashKey, value, ttlSecond)
        );
    }

    @Test
    void testGetWithInvalidDataTypeShouldThrowsException() throws Exception {
        String key = "testKey";
        String rawData = "{invalid json}";
        when(valueOperations.get(key)).thenReturn(rawData);
        when(objectWithTimeMapper.readValue(rawData, String.class)).thenThrow(new JsonProcessingException("Invalid JSON") {
        });

        assertThrows(RuntimeException.class, () -> redisCacheService.get(key, String.class));
    }

    @Test
    void testGetHashWithInvalidDataTypeShouldThrowsException() throws JsonProcessingException {
        String key = "testKey";
        String hashKey = "testHashKey";
        String rawData = "{invalid json}s";
        when(hashOperations.get(key, hashKey)).thenReturn(rawData);
        when(objectWithTimeMapper.readValue(anyString(), any(Class.class))).thenThrow(new JsonProcessingException("Cannot parse JSON") {
        });

        assertThrows(RuntimeException.class, () -> redisCacheService.get(key, hashKey, PhraseDetail.class));
    }

    @Test
    void testPutIfAbsentWhenSuccessShouldReturnTrue() {
        String key = "testKey";
        String hashKey = "testHashKey";
        String value = "testValue";

        when(hashOperations.putIfAbsent(key, hashKey, value)).thenReturn(true);

        boolean result = redisCacheService.putIfAbsent(key, hashKey, value);

        assertTrue(result);
        verify(hashOperations).putIfAbsent(eq(key), eq(hashKey), eq(value));
    }

    @Test
    void testPutIfAbsentWhenFailShouldReturnFalse() {
        String key = "testKey";
        String hashKey = "testHashKey";
        String value = "testValue";

        when(hashOperations.putIfAbsent(key, hashKey, value)).thenReturn(false);

        boolean result = redisCacheService.putIfAbsent(key, hashKey, value);

        assertFalse(result);
        verify(hashOperations).putIfAbsent(eq(key), eq(hashKey), eq(value));
    }

    @Test
    void testPutIfAbsentWhenExceptionShouldThrowsException() {
        String key = "testKey";
        String hashKey = "testHashKey";
        String value = "testValue";

        doThrow(new RuntimeException("Redis connection error")).when(hashOperations).putIfAbsent(key, hashKey, value);

        assertThrows(RuntimeException.class, () -> redisCacheService.putIfAbsent(key, hashKey, value));

        verify(hashOperations).putIfAbsent(eq(key), eq(hashKey), eq(value));
    }

    @Test
    void testGetWithValidKeyShouldReturnDataSuccess() throws TMBCommonException {
        String key = "test-key";
        String expectedValue = "test-value";
        when(valueOperations.get(key)).thenReturn(expectedValue);
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);

        String actualValue = redisCacheService.get(key);

        assertEquals(expectedValue, actualValue);
        verify(valueOperations).get(key);
    }

    @Test
    void testGetWithExceptionShouldReturnNull() throws TMBCommonException {
        String key = "test-key";
        when(valueOperations.get(key)).thenThrow(new RuntimeException("Redis connection error"));
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);

        String actualValue = redisCacheService.get(key);

        assertNull(actualValue);
        verify(valueOperations).get(key);
    }

    @Test
    void testGetWithNullDataShouldReturnNull() throws TMBCommonException {
        String key = "test-key";
        when(valueOperations.get(key)).thenReturn(null);
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);


        String actualValue = redisCacheService.get(key);

        assertNull(actualValue);
        verify(valueOperations).get(key);
    }
}