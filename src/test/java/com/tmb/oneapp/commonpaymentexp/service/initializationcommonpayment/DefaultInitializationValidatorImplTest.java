package com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.DeepLinkRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitialPrepareData;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.initialization.InitializationCommonPaymentRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_CALL_FROM_AUTO_LOAN;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class DefaultInitializationValidatorImplTest {
    @InjectMocks
    private DefaultInitializationValidatorImpl defaultInitializationValidator;

    private CommonPaymentConfig commonPaymentConfig;
    private InitializationCommonPaymentRequest request;
    private HttpHeaders headers;
    private InitialPrepareData prepareData;
    private MasterBillerResponse masterBillerResponse;
    private DeepLinkRequest deepLinkRequest;
    private String correlationId;

    @BeforeEach
    void setUp() {
        correlationId = "test-correlation-id";

        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);
        request = initialCommonPaymentRequest();
        deepLinkRequest = null;
        masterBillerResponse = initialMasterBiller();
        commonPaymentConfig = initialCommonPaymentConfigWithExpireDate("2099-12-02 23:59:59");
        prepareData = new InitialPrepareData()
                .setMasterBillerResponse(masterBillerResponse)
                .setCommonPaymentConfig(commonPaymentConfig)
                .setDeepLinkRequest(deepLinkRequest);

    }

    @Test
    void testValidateData_WhenFailedProductExpired_ShouldThrowsException() {
        LocalDateTime expiryDate = parseDateTime("2014-12-02 23:59:59");
        commonPaymentConfig.setExpiryDate(expiryDate);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> defaultInitializationValidator.validateData(request, headers, prepareData));

        assertEquals(ResponseCode.PRODUCT_EXPIRED_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testValidateData_WhenNotBillPayTransaction_ShouldSkipMasterBillerCheckToggle() {
        request.getPaymentInformation().setTransactionType("OTHER_TRANSACTION_TYPE");
        masterBillerResponse.getBillerInfo().setAllowCallCommonPayment(false);

        assertDoesNotThrow(() ->
                defaultInitializationValidator.validateData(request, headers, prepareData)
        );
    }

    @Nested
    class TransactionTypeBillPayTest {

        @Test
        void testValidateData_WhenFailedMasterBillerNotAllowCommonPayment_ShouldThrowException() {
            ReflectionTestUtils.setField(defaultInitializationValidator, "enableCheckToggleAllowCommonPaymentFromProperty", true);
            request.getPaymentInformation().setTransactionType(COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY);
            masterBillerResponse.getBillerInfo().setAllowCallCommonPayment(false);
            prepareData.setMasterBillerResponse(masterBillerResponse);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    defaultInitializationValidator.validateData(request, headers, prepareData)
            );

            assertEquals(ResponseCode.NOT_ALLOW_COMMON_PAYMENT_ERROR.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.OK, exception.getStatus());
        }

        @Test
        void testValidateData_WhenToggleCheckDisabled_ShouldSkipMasterBillerCheck() {
            ReflectionTestUtils.setField(defaultInitializationValidator, "enableCheckToggleAllowCommonPaymentFromProperty", false);
            masterBillerResponse.getBillerInfo().setAllowCallCommonPayment(false);

            assertDoesNotThrow(() ->
                    defaultInitializationValidator.validateData(request, headers, prepareData)
            );
        }
    }

    @Nested
    class TransactionBillFromDeeplinkCallFromAutoLoanTest {
        @BeforeEach
        void setUp() {
            deepLinkRequest = new DeepLinkRequest();
            deepLinkRequest.setRef1("REF1");
            deepLinkRequest.setRef2("REF2");
            deepLinkRequest.setCallFrom(BILLER_CALL_FROM_AUTO_LOAN);

            prepareData.setDeepLinkRequest(deepLinkRequest);

            request.getPaymentInformation().setDeepLinkTransactionId("DEEP_LINK_TRANSACTION_ID");
        }

        @Test
        void testValidateReferenceIdWithDeepLinkRequest_WhenValidRefs_ShouldNotThrowException() {
            ProductDetail productDetail = ProductDetail.builder()
                    .productRef1("REF1")
                    .productRef2("REF2")
                    .build();
            request.getPaymentInformation().setProductDetail(productDetail);

            assertDoesNotThrow(() -> defaultInitializationValidator.validateData(request, headers, prepareData));
        }

        @Test
        void testValidateReferenceIdWithDeepLinkRequestWhenRef1MismatchShouldThrowException() {
            ProductDetail productDetail = ProductDetail.builder()
                    .productRef1("OTHER_REF1")
                    .productRef2("REF2")
                    .build();
            request.getPaymentInformation().setProductDetail(productDetail);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    defaultInitializationValidator.validateData(request, headers, prepareData)
            );

            assertEquals(ResponseCode.INCORRECT_AUTO_LOAN_REF_1.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        }

        @Test
        void testValidateReferenceIdWithDeepLinkRequestWhenRef2MismatchShouldThrowException() {
            ProductDetail productDetail = ProductDetail.builder()
                    .productRef1("REF1")
                    .productRef2("OTHER_REF2")
                    .build();
            request.getPaymentInformation().setProductDetail(productDetail);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    defaultInitializationValidator.validateData(request, headers, prepareData)
            );

            assertEquals(ResponseCode.INCORRECT_AUTO_LOAN_REF_2.getCode(), exception.getErrorCode());
            assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
        }

        @Test
        void testValidateReferenceIdWithDeepLinkRequestWhenRefsNullShouldThrowException() {
            DeepLinkRequest deepLinkRequest = new DeepLinkRequest();
            deepLinkRequest.setRef1(null);
            deepLinkRequest.setRef2(null);
            deepLinkRequest.setCallFrom(BILLER_CALL_FROM_AUTO_LOAN);
            prepareData.setDeepLinkRequest(deepLinkRequest);
            ProductDetail productDetail = ProductDetail.builder()
                    .productRef1("REF1")
                    .productRef2("REF2")
                    .build();
            request.getPaymentInformation().setProductDetail(productDetail);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                    defaultInitializationValidator.validateData(request, headers, prepareData)
            );
            assertEquals(ResponseCode.MISSING_REQUIRED_FIELD_ERROR.getCode(), exception.getErrorCode());
        }
    }

    private static CommonPaymentConfig initialCommonPaymentConfigWithExpireDate(String date) {
        CommonPaymentConfig commonPaymentConfig = new CommonPaymentConfig();
        commonPaymentConfig.setCallbackUrls(Collections.singletonList("intra.ttb.com/callback"));
        commonPaymentConfig.setExpiryDate(parseDateTime(date));
        return commonPaymentConfig;
    }

    private static LocalDateTime parseDateTime(String date) {
        return LocalDateTime.parse(date, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private MasterBillerResponse initialMasterBiller() {
        return new MasterBillerResponse().setBillerInfo(
                        new BillerInfoResponse()
                                .setBillerGroupType(BILLER_GROUP_TYPE_BILL)
                )
                .setRef1(new ReferenceResponse().setIsMobile(false));
    }

    private InitializationCommonPaymentRequest initialCommonPaymentRequest() {
        return new InitializationCommonPaymentRequest()
                .setPaymentInformation(new PaymentInformation()
                        .setEntryId("entry-id")
                        .setCompCode("comp-code")
                        .setTransactionType(COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY)
                        .setProductDetail(new ProductDetail()
                                .setProductRef1("ref1")
                                .setProductRef2("ref2"))
                        .setAmountDetail(new AmountDetail()
                                .setAmountValue(new BigDecimal("1000.00")))
                );
    }
}