package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.kafka.service.KafkaProducerService;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PartnerPaymentStatusCallback;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class PaymentStatusPublisherServiceTest {

    @Mock
    private KafkaProducerService kafkaProducerService;

    private PaymentStatusPublisherService paymentStatusPublisherService;

    private final String paymentStatusTopic = "payment_status_topic";
    private final String partnerPaymentStatusTopic = "partner_payment_status_topic";

    @BeforeEach
    void setUp() {
        paymentStatusPublisherService = new PaymentStatusPublisherService(kafkaProducerService, paymentStatusTopic, partnerPaymentStatusTopic);
    }

    @Test
    @SuppressWarnings("unchecked")
    void testPublishInternal_ShouldPublishToInternalTopic() {
        PartnerPaymentStatusCallback callback = PartnerPaymentStatusCallback.builder()
                .url("https://internal.callback.url")
                .data(PartnerPaymentStatusCallback.DataPayload.builder()
                        .payload("{\"transactionId\":\"test-tx-123\"}")
                        .build())
                .build();

        paymentStatusPublisherService.publishInternal(callback, "identifier-123");

        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> keyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> payloadCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Map<String, String>> headersCaptor = ArgumentCaptor.forClass((Class<Map<String, String>>) (Class<?>) Map.class);
        verify(kafkaProducerService).sendMessageAsync(topicCaptor.capture(), keyCaptor.capture(), payloadCaptor.capture(), headersCaptor.capture());

        assertEquals(paymentStatusTopic, topicCaptor.getValue());
        assertEquals("identifier-123", headersCaptor.getValue().get("entry_id"));
    }


    @Test
    @SuppressWarnings("unchecked")
    void testPublishPartner_ShouldPublishToPartnerTopic() {
        PartnerPaymentStatusCallback callback = PartnerPaymentStatusCallback.builder()
                .url("https://partner.callback.url")
                .data(PartnerPaymentStatusCallback.DataPayload.builder()
                        .payload("{\"transactionId\":\"test-tx-456\"}")
                        .build())
                .build();

        paymentStatusPublisherService.publishPartner(callback, "partner-identifier-456");

        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> keyCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> payloadCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Map<String, String>> headersCaptor = ArgumentCaptor.forClass((Class<Map<String, String>>) (Class<?>) Map.class);
        verify(kafkaProducerService).sendMessageAsync(topicCaptor.capture(), keyCaptor.capture(), payloadCaptor.capture(), headersCaptor.capture());

        assertEquals(partnerPaymentStatusTopic, topicCaptor.getValue());
        assertEquals("partner-identifier-456", headersCaptor.getValue().get("entry_id"));
    }

    @Test
    void testPublishInternal_WhenKafkaThrowsException_ShouldHandleGracefully() {
        PartnerPaymentStatusCallback callback = PartnerPaymentStatusCallback.builder()
                .url("https://error.callback.url")
                .data(PartnerPaymentStatusCallback.DataPayload.builder()
                        .payload("{\"transactionId\":\"test-tx-error-123\"}")
                        .build())
                .build();
        String identifier = "identifier-error-123";
        doThrow(new RuntimeException("Kafka is down")).when(kafkaProducerService).sendMessageAsync(anyString(), anyString(), anyString(), anyMap());

        assertDoesNotThrow(() -> paymentStatusPublisherService.publishInternal(callback, identifier));
    }

    @Test
    void testPublishPartner_WhenKafkaThrowsException_ShouldHandleGracefully() {
        PartnerPaymentStatusCallback callback = PartnerPaymentStatusCallback.builder()
                .url("https://partner-error.callback.url")
                .data(PartnerPaymentStatusCallback.DataPayload.builder()
                        .payload("{\"transactionId\":\"test-tx-error-456\"}")
                        .build())
                .build();
        String identifier = "partner-identifier-error-456";
        doThrow(new RuntimeException("Kafka is down")).when(kafkaProducerService).sendMessageAsync(anyString(), anyString(), anyString(), anyMap());

        assertDoesNotThrow(() -> paymentStatusPublisherService.publishPartner(callback, identifier));
    }

}