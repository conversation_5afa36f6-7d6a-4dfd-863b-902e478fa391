package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class DStatementConfirmServiceProcessorTest {
    @InjectMocks
    private DStatementConfirmServiceProcessor dStatementConfirmServiceProcessor;

    @BeforeEach
    void setUp() {
    }

    @Test
    void getProcessorType_ShouldReturnDStatement() {
        assertEquals("d_statement", dStatementConfirmServiceProcessor.getProcessorType());
    }

    @Test
    void confirm_ShouldReturnEmptyResponse() throws TMBCommonException {
        ConfirmationCommonPaymentResponse response = dStatementConfirmServiceProcessor.executeConfirm(null, null, null);

        assertNotNull(response);
    }

}