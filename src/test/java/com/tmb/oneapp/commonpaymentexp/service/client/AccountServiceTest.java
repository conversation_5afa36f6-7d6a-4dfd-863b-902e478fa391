package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.AccountServiceClient;
import com.tmb.oneapp.commonpaymentexp.model.loanaccount.AccountLoanDetailRequest;
import com.tmb.oneapp.commonpaymentexp.model.loanaccount.HomeLoanFullInfoResponse;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.function.Supplier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AccountServiceTest {
    @Mock
    private FeignClientHelper feignClientHelper;
    @Mock
    private AccountServiceClient accountServiceClient;

    @InjectMocks
    private AccountService accountService;

    private String correlationId;

    @BeforeEach
    void setUp() throws TMBCommonException {
        correlationId = "correlationId";
        setUpFeignClientHelperExecuteRequest();
    }

    @Test
    void testFetchLoanAccountByLoanAccountIDShouldSuccess() throws TMBCommonException {
        TmbServiceResponse<HomeLoanFullInfoResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new HomeLoanFullInfoResponse());
        when(accountServiceClient.getAccountLoanDetail(eq(correlationId), any(AccountLoanDetailRequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        HomeLoanFullInfoResponse actual = accountService.fetchLoanAccountByLoanAccountID(correlationId, "loanAccountID");

        Assertions.assertNotNull(actual);
    }

    private void setUpFeignClientHelperExecuteRequest() throws TMBCommonException {
        lenient().when(feignClientHelper.executeRequest(any())).thenAnswer(invocation -> {
            try {
                Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
                ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
                return feignResponseEntity.getBody().getData();
            } catch (Exception e) {
                throw new TMBCommonException("");
            }
        });
    }
}