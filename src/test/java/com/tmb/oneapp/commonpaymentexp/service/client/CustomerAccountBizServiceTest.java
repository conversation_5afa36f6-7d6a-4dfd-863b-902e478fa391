package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.CustomerAccountBizClient;
import com.tmb.oneapp.commonpaymentexp.model.AccountSaving;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class CustomerAccountBizServiceTest {
    @InjectMocks
    private CustomerAccountBizService customerAccountBizService;

    @Mock
    private CustomerAccountBizClient customerAccountBizClient;
    @Mock
    private FeignClientHelper feignClientHelper;

    private String crmId;
    private String correlationId;

    @BeforeEach
    void setUp() throws TMBCommonException {
        TestUtils.setUpFeignClientHelperExecuteRequest(feignClientHelper);

        crmId = "crmId";
        correlationId = "correlationId";
    }

    @Test
    void testFetchLoanAccount_WhenSuccess_ShouldDoesNotThrows() {
        TmbServiceResponse<AccountSaving> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new AccountSaving());
        Mockito.when(customerAccountBizClient.getAccountList(correlationId, crmId)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> customerAccountBizService.fetchLoanAccount(correlationId, crmId));
    }

    @Test
    void testFetchLoanAccount_WhenFailedFeignException_ShouldThrowsTMBCommonException() {
        Mockito.when(customerAccountBizClient.getAccountList(correlationId, crmId)).thenThrow(FeignException.class);

        Assertions.assertThrows(TMBCommonException.class, () -> customerAccountBizService.fetchLoanAccount(correlationId, crmId));
    }
}