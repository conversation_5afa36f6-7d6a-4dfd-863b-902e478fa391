package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.model.Description;
import com.tmb.common.model.Status;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.PaymentServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.ExternalErrorCustomDescription;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.TopUpETETransaction;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETERequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.PromptPayETEValidateResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.creditcard.CreditCardConfirmResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.BillPaymentCreditCard;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.creditcard.CreditCardConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpaymenttoggle.CommonPaymentToggle;
import com.tmb.oneapp.commonpaymentexp.service.client.ErrorMappingHelper;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import feign.FeignException;
import io.netty.handler.timeout.TimeoutException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.function.Supplier;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PaymentServiceTest {
    @Mock
    FeignClientHelper feignClientHelper;

    @Mock
    PaymentServiceClient paymentServiceClient;
    @Mock
    ErrorMappingHelper errorMappingHelper;

    @InjectMocks
    PaymentService paymentService;

    private String correlationId;
    private String osVersion;
    private String crmId;
    private String entryId;
    private String transactionId;
    private OCPBillRequest ocpBillRequest;
    private TopUpETEPaymentRequest topUpETEPaymentRequest;
    private CreditCardConfirmRequest creditCardConfirmRequest;

    @BeforeEach
    void setUp() throws TMBCommonException {
        TestUtils.setUpFeignClientHelperExecuteRequest(feignClientHelper);
        TestUtils.setUpFeignClientHelperExecuteRequestOrElseThrow(feignClientHelper);

        correlationId = "correlationId";
        crmId = "crmId";
        entryId = "bill_pay";
        transactionId = "transactionId";
        osVersion = "iOS123";

        ocpBillRequest = new OCPBillRequest();
        ocpBillRequest.setAmount("1000.00");
        ocpBillRequest.setCompCode("compCode");
        ocpBillRequest.setRef1("ref1");
        ocpBillRequest.setRef2("ref2");

        topUpETEPaymentRequest = new TopUpETEPaymentRequest();
        topUpETEPaymentRequest.setAmount(new BigDecimal("1500.00"));
        topUpETEPaymentRequest.setCompCode("autoLoanCompCode");
        topUpETEPaymentRequest.setReference1("autoRef1");
        topUpETEPaymentRequest.setReference2("autoRef2");

        BillPaymentCreditCard billPayment = new BillPaymentCreditCard();
        billPayment.setAmount("2000.00");
        billPayment.setCompCode("creditCardCompCode");
        billPayment.setRef1("creditCardRef1");
        billPayment.setRef2("creditCardRef2");

        creditCardConfirmRequest = new CreditCardConfirmRequest();
        creditCardConfirmRequest.setBillPayment(billPayment);

    }

    @Test
    public void testGetCommonPaymentConfigWhenSuccessShouldNotThrowsException() throws TMBCommonException {
        CommonPaymentConfig expectedConfig = new CommonPaymentConfig();

        TmbServiceResponse<CommonPaymentConfig> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(expectedConfig);
        when(paymentServiceClient.fetchCommonPaymentConfig(correlationId, entryId)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        CommonPaymentConfig actualConfig = paymentService.getCommonPaymentConfig(correlationId, entryId);

        assertNotNull(actualConfig);
        assertEquals(expectedConfig, actualConfig);

        verify(feignClientHelper).executeRequestOrElseThrow(any(), any());
    }

    @Test
    public void testGetCommonPaymentConfigWhenDataNotFoundShouldThrowsTmbCommonException() throws TMBCommonException {
        when(paymentServiceClient.fetchCommonPaymentConfig(correlationId, entryId)).thenThrow(FeignException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> paymentService.getCommonPaymentConfig(correlationId, entryId));

        assertEquals("data not found", exception.getMessage());

        verify(feignClientHelper).executeRequestOrElseThrow(any(), any());
    }

    @Test
    void testGetBillPayConfig_Success_ShouldReturn() throws TMBCommonException {
        BillPayConfiguration expectedResponse = new BillPayConfiguration()
                .setId("id");

        TmbServiceResponse<BillPayConfiguration> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(expectedResponse);
        when(paymentServiceClient.fetchBillPayConfig(correlationId))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));

        BillPayConfiguration actual = paymentService.getBillPayConfig(correlationId);

        Assertions.assertEquals("id", actual.getId());

    }

    @Test
    void testGetMasterBiller_Success_ShouldReturn() throws TMBCommonException {
        String compCode = "2704";
        MasterBillerResponse expectedResponse = new MasterBillerResponse()
                .setBillerInfo(new BillerInfoResponse());

        TmbServiceResponse<MasterBillerResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(expectedResponse);
        when(paymentServiceClient.fetchMasterBillerByCompCode(correlationId, compCode))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));

        MasterBillerResponse actual = paymentService.getMasterBiller(correlationId, compCode);

        Assertions.assertNotNull(actual.getBillerInfo());
    }

    @Test
    void testGetMasterBillerOrElseThrow_Success_ShouldReturn() throws TMBCommonException {
        String compCode = "2704";
        MasterBillerResponse expectedResponse = new MasterBillerResponse()
                .setBillerInfo(new BillerInfoResponse());

        TmbServiceResponse<MasterBillerResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(expectedResponse);
        when(paymentServiceClient.fetchMasterBillerByCompCode(correlationId, compCode, osVersion, null))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));

        MasterBillerResponse actual = paymentService.getMasterBillerOrElseThrow(correlationId, compCode, osVersion, null, null);

        Assertions.assertNotNull(actual.getBillerInfo());
    }

    @Test
    void testGetMasterBillerOrElseThrow_WhenFailedFeignException_ShouldThrowSpecificException() {
        Supplier<TMBCommonException> specificException = () -> CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.MASTER_BILLER_NOT_FOUND_ERROR);
        String compCode = "2704";

        when(paymentServiceClient.fetchMasterBillerByCompCode(correlationId, compCode, osVersion, null))
                .thenThrow(FeignException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> paymentService.getMasterBillerOrElseThrow(correlationId, compCode, osVersion, null, specificException));

        assertEquals(ResponseCode.MASTER_BILLER_NOT_FOUND_ERROR.getCode(), exception.getErrorCode());
    }

    @Test
    void validateOCPBillPayment_Success_ShouldReturnOCPBillPayment() throws TMBCommonException, TMBCommonExceptionWithResponse {
        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setAmount("1000.00");
        ocpBillPayment.setCompCode("compCode");

        OCPBillPaymentResponse expectedResponse = new OCPBillPaymentResponse();
        expectedResponse.setData(ocpBillPayment);

        TmbServiceResponse<OCPBillPaymentResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(expectedResponse);
        when(paymentServiceClient.validateOcpPayment(correlationId, crmId, ocpBillRequest)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        OCPBillPayment result = paymentService.validateOCPBillPayment(correlationId, crmId, ocpBillRequest).getData();

        assertNotNull(result);
        assertEquals("1000.00", result.getAmount());
        assertEquals("compCode", result.getCompCode());
    }

    @Test
    void validateOCPBillPayment_Failed_WhenStatusCodeNotSuccess_ShouldThrowTMBCommonExceptionWithSpecificErrorCode() {
        String errorCodeFromETE = "cbd_557";
        mockGetErrorTitle(errorCodeFromETE);
        mockGetErrorCustomDescription(errorCodeFromETE);
        TmbServiceResponse<OCPBillPaymentResponse> tmbServiceResponse = new TmbServiceResponse<>();
        Description errorDescriptionFromETE = new Description();
        errorDescriptionFromETE.setEn("error EN from ETE after mapping error phrase");
        errorDescriptionFromETE.setTh("error TH from ETE after mapping error phrase");
        tmbServiceResponse.setStatus(new Status(errorCodeFromETE, ResponseCode.FAILED_V2.getMessage(), ResponseCode.FAILED_V2.getService(), errorDescriptionFromETE));
        tmbServiceResponse.setData(null);
        when(paymentServiceClient.validateOcpPayment(correlationId, crmId, ocpBillRequest)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonExceptionWithResponse exception = assertThrows(TMBCommonExceptionWithResponse.class, () -> paymentService.validateOCPBillPayment(correlationId, crmId, ocpBillRequest));

        Assertions.assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
        ExternalErrorCustomDescription actualCustomError = (ExternalErrorCustomDescription) exception.getData();
        assertEquals("custom description EN", actualCustomError.getCustomDescription().getEn());
        assertEquals("custom description TH", actualCustomError.getCustomDescription().getTh());
        assertEquals("title EN", actualCustomError.getTitle().getEn());
        assertEquals("title TH", actualCustomError.getTitle().getTh());
    }

    @Test
    void validateOCPBillPayment_Failed_WhenResponseDataIsNull_ShouldThrowTMBCommonException() {
        TmbServiceResponse<OCPBillPaymentResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(null);
        when(paymentServiceClient.validateOcpPayment(correlationId, crmId, ocpBillRequest)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> paymentService.validateOCPBillPayment(correlationId, crmId, ocpBillRequest));

        Assertions.assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void validateOCPBillPayment_Failed_WhenFeignException_ShouldThrowException() {
        when(paymentServiceClient.validateOcpPayment(correlationId, crmId, ocpBillRequest)).thenThrow(FeignException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                paymentService.validateOCPBillPayment(correlationId, crmId, ocpBillRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void confirmOCPBillPayment_Success_ShouldReturnOCPBillPayment() throws TMBCommonException, TMBCommonExceptionWithResponse {
        OCPBillPaymentResponse expectedResponse = new OCPBillPaymentResponse();
        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setAmount("1000.00");
        ocpBillPayment.setCompCode("compCode");
        ocpBillPayment.setEpayCode("epayCode123");
        expectedResponse.setData(ocpBillPayment);

        TmbServiceResponse<OCPBillPaymentResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(expectedResponse);
        when(paymentServiceClient.confirmOcpPayment(correlationId, crmId, transactionId, ocpBillRequest)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        OCPBillPayment result = paymentService.confirmOCPBillPayment(correlationId, crmId, transactionId, ocpBillRequest).getData();

        assertNotNull(result);
        assertEquals("1000.00", result.getAmount());
        assertEquals("compCode", result.getCompCode());
        assertEquals("epayCode123", result.getEpayCode());
    }

    @Test
    void confirmOCPBillPayment_Failed_ShouldThrowException() {
        when(paymentServiceClient.confirmOcpPayment(eq(correlationId), eq(crmId), eq(transactionId), any(OCPBillRequest.class))).thenThrow(FeignException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                paymentService.confirmOCPBillPayment(correlationId, crmId, transactionId, ocpBillRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void confirmOCPBillPayment_FailedNullException_ShouldThrowException() {
        when(paymentServiceClient.confirmOcpPayment(eq(correlationId), eq(crmId), eq(transactionId), any(OCPBillRequest.class))).thenThrow(TimeoutException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                paymentService.confirmOCPBillPayment(correlationId, crmId, transactionId, ocpBillRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void confirmOCPBillWowPointPayment_Success_ShouldReturnOCPBillPayment() throws TMBCommonException, TMBCommonExceptionWithResponse {
        OCPBillPaymentResponse expectedResponse = new OCPBillPaymentResponse();
        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setAmount("1000.00");
        ocpBillPayment.setCompCode("compCode");
        ocpBillPayment.setFee(new OCPFee().setFeeType("WOW").setBillPmtFee("0.00").setFlagFeeReg("I"));
        ocpBillPayment.setEpayCode("epayCode123");
        expectedResponse.setData(ocpBillPayment);

        TmbServiceResponse<OCPBillPaymentResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(expectedResponse);
        when(paymentServiceClient.confirmOcpWowPointPayment(correlationId, crmId, transactionId, ocpBillRequest)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        OCPBillPayment result = paymentService.confirmOCPBillWowPointPayment(correlationId, crmId, transactionId, ocpBillRequest).getData();

        assertNotNull(result);
        assertEquals("1000.00", result.getAmount());
        assertEquals("compCode", result.getCompCode());
        assertEquals("epayCode123", result.getEpayCode());
    }

    @Test
    void confirmOCPBillWowPointPayment_Failed_WhenStatusCodeNotSuccess_ShouldThrowWowTMBCommonExceptionTest() {
        String errorCodeFromETE = "wow_0059";
        mockGetErrorTitle(errorCodeFromETE);
        mockGetErrorCustomDescription(errorCodeFromETE);
        TmbServiceResponse<OCPBillPaymentResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(new Status(errorCodeFromETE, "serial_code is invalid", ResponseCode.FAILED_V2.getService(), new Description()));
        tmbServiceResponse.setData(null);
        when(paymentServiceClient.confirmOcpWowPointPayment(correlationId, crmId, transactionId, ocpBillRequest)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> paymentService.confirmOCPBillWowPointPayment(correlationId, crmId, transactionId, ocpBillRequest));

        Assertions.assertEquals(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), exception.getErrorCode());
        Assertions.assertEquals("wow_0059 : serial_code is invalid", exception.getErrorMessage());
    }

    @Test
    void confirmOCPBillWowPointPayment_Failed_WhenStatusCodeNotSuccess_ShouldThrowSpecificErrorCodeTMBCommonExceptionTest() {
        String errorCodeFromETE = "any_biller_0059";
        mockGetErrorTitle(errorCodeFromETE);
        mockGetErrorCustomDescription(errorCodeFromETE);
        Description errorDescriptionFromETE = new Description();
        errorDescriptionFromETE.setEn("error EN from ETE after mapping error phrase");
        errorDescriptionFromETE.setTh("error TH from ETE after mapping error phrase");
        TmbServiceResponse<OCPBillPaymentResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(new Status(errorCodeFromETE, "serial_code is invalid", ResponseCode.FAILED_V2.getService(), errorDescriptionFromETE));
        tmbServiceResponse.setData(null);
        when(paymentServiceClient.confirmOcpWowPointPayment(correlationId, crmId, transactionId, ocpBillRequest)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonExceptionWithResponse exception = assertThrows(TMBCommonExceptionWithResponse.class, () -> paymentService.confirmOCPBillWowPointPayment(correlationId, crmId, transactionId, ocpBillRequest));

        Assertions.assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
        ExternalErrorCustomDescription actualCustomError = (ExternalErrorCustomDescription) exception.getData();
        assertEquals("custom description EN", actualCustomError.getCustomDescription().getEn());
        assertEquals("custom description TH", actualCustomError.getCustomDescription().getTh());
        assertEquals("title EN", actualCustomError.getTitle().getEn());
        assertEquals("title TH", actualCustomError.getTitle().getTh());
    }

    @Test
    void confirmOCPBillWowPointPayment_FailedNullException_ShouldThrowException() {
        when(paymentServiceClient.confirmOcpWowPointPayment(eq(correlationId), eq(crmId), eq(transactionId), any(OCPBillRequest.class))).thenThrow(FeignException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                paymentService.confirmOCPBillWowPointPayment(correlationId, crmId, transactionId, ocpBillRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void confirmOCPBillWowPointHomeLoanPayment_Success_ShouldReturnOCPBillPayment() throws TMBCommonException, TMBCommonExceptionWithResponse {
        OCPBillPaymentResponse expectedResponse = new OCPBillPaymentResponse();
        OCPBillPayment ocpBillPayment = new OCPBillPayment();
        ocpBillPayment.setAmount("1000.00");
        ocpBillPayment.setCompCode("compCode");
        ocpBillPayment.setFee(new OCPFee().setFeeType("WOW").setBillPmtFee("0.00").setFlagFeeReg("I"));
        ocpBillPayment.setEpayCode("epayCode123");
        expectedResponse.setData(ocpBillPayment);

        TmbServiceResponse<OCPBillPaymentResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(expectedResponse);
        when(paymentServiceClient.confirmOcpWowPointHomeLoanPayment(correlationId, crmId, transactionId, ocpBillRequest)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        OCPBillPayment result = paymentService.confirmOCPBillWowPointHomeLoanPayment(correlationId, crmId, transactionId, ocpBillRequest).getData();

        assertNotNull(result);
        assertEquals("1000.00", result.getAmount());
        assertEquals("compCode", result.getCompCode());
        assertEquals("epayCode123", result.getEpayCode());
    }

    @Test
    void confirmOCPBillWowPointHomeLoanPayment_Failed_WhenStatusCodeNotSuccess_ShouldThrowWowTMBCommonExceptionTest() {
        String errorCodeFromETE = "wow_0059";
        mockGetErrorTitle(errorCodeFromETE);
        mockGetErrorCustomDescription(errorCodeFromETE);
        TmbServiceResponse<OCPBillPaymentResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(new Status(errorCodeFromETE, "serial_code is invalid", ResponseCode.FAILED_V2.getService(), new Description()));
        tmbServiceResponse.setData(null);
        when(paymentServiceClient.confirmOcpWowPointHomeLoanPayment(correlationId, crmId, transactionId, ocpBillRequest)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> paymentService.confirmOCPBillWowPointHomeLoanPayment(correlationId, crmId, transactionId, ocpBillRequest));

        Assertions.assertEquals(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), exception.getErrorCode());
        Assertions.assertEquals("wow_0059 : serial_code is invalid", exception.getErrorMessage());
    }

    @Test
    void confirmOCPBillWowPointHomeLoanPayment_Failed_WhenStatusCodeNotSuccess_ShouldThrowSpecificErrorCodeTMBCommonExceptionTest() {
        String errorCodeFromETE = "any_biller_0059";
        mockGetErrorTitle(errorCodeFromETE);
        mockGetErrorCustomDescription(errorCodeFromETE);
        Description errorDescriptionFromETE = new Description();
        errorDescriptionFromETE.setEn("error EN from ETE after mapping error phrase");
        errorDescriptionFromETE.setTh("error TH from ETE after mapping error phrase");
        TmbServiceResponse<OCPBillPaymentResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(new Status(errorCodeFromETE, "serial_code is invalid", ResponseCode.FAILED_V2.getService(), errorDescriptionFromETE));
        tmbServiceResponse.setData(null);
        when(paymentServiceClient.confirmOcpWowPointHomeLoanPayment(correlationId, crmId, transactionId, ocpBillRequest)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonExceptionWithResponse exception = assertThrows(TMBCommonExceptionWithResponse.class, () -> paymentService.confirmOCPBillWowPointHomeLoanPayment(correlationId, crmId, transactionId, ocpBillRequest));

        Assertions.assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
        ExternalErrorCustomDescription actualCustomError = (ExternalErrorCustomDescription) exception.getData();
        assertEquals("custom description EN", actualCustomError.getCustomDescription().getEn());
        assertEquals("custom description TH", actualCustomError.getCustomDescription().getTh());
        assertEquals("title EN", actualCustomError.getTitle().getEn());
        assertEquals("title TH", actualCustomError.getTitle().getTh());
    }

    @Test
    void confirmOCPBillWowPointHomeLoanPayment_FailedNullException_ShouldThrowException() {
        when(paymentServiceClient.confirmOcpWowPointHomeLoanPayment(eq(correlationId), eq(crmId), eq(transactionId), any(OCPBillRequest.class))).thenThrow(FeignException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () ->
                paymentService.confirmOCPBillWowPointHomeLoanPayment(correlationId, crmId, transactionId, ocpBillRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void getCommonPaymentToggle_Success_ShouldReturnCommonPaymentToggle() {
        TmbServiceResponse<CommonPaymentToggle> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new CommonPaymentToggle());
        when(paymentServiceClient.getCommonPaymentToggle(correlationId)).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        assertDoesNotThrow(() -> paymentService.getCommonPaymentToggle(correlationId));
    }

    @Test
    void getCommonPaymentToggle_FailedFeignException_ShouldThrowTMBCommonException() {
        when(paymentServiceClient.getCommonPaymentToggle(correlationId)).thenThrow(FeignException.class);

        assertThrows(TMBCommonException.class, () -> paymentService.getCommonPaymentToggle(correlationId));
    }

    @Test
    void testValidateAutoLoanPaymentWhenSuccessShouldReturnTopUpETEResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
        TopUpETEResponse expectedResponse = new TopUpETEResponse();
        TopUpETETransaction transaction = new TopUpETETransaction();
        transaction.setAmount(new BigDecimal("1500.00"));
        expectedResponse.setTransaction(transaction);
        expectedResponse.setStatus(new com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.Status("0000", "success"));

        TmbServiceResponse<TopUpETEResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(expectedResponse);
        when(paymentServiceClient.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TopUpETEResponse result = paymentService.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest);

        assertNotNull(result);
        assertNotNull(result.getTransaction());
        assertEquals(new BigDecimal("1500.00"), result.getTransaction().getAmount());
    }

    @Test
    void testValidateAutoLoanPaymentWhenStatusCodeNotSuccessShouldThrowTMBCommonExceptionWithSpecificErrorCode() {
        String errorCodeFromETE = "ete_557";
        Description errorDescriptionFromETE = new Description();
        errorDescriptionFromETE.setEn("error EN from ETE");
        errorDescriptionFromETE.setTh("error TH from ETE");

        mockGetErrorTitle(errorCodeFromETE);
        mockGetErrorCustomDescription(errorCodeFromETE);
        TmbServiceResponse<TopUpETEResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(new com.tmb.common.model.Status(errorCodeFromETE, ResponseCode.FAILED_V2.getMessage(),
                ResponseCode.FAILED_V2.getService(), errorDescriptionFromETE));
        tmbServiceResponse.setData(null);

        when(paymentServiceClient.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonExceptionWithResponse exception = assertThrows(TMBCommonExceptionWithResponse.class,
                () -> paymentService.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest));

        assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
        ExternalErrorCustomDescription actualCustomError = (ExternalErrorCustomDescription) exception.getData();
        assertEquals("custom description EN", actualCustomError.getCustomDescription().getEn());
        assertEquals("custom description TH", actualCustomError.getCustomDescription().getTh());
        assertEquals("title EN", actualCustomError.getTitle().getEn());
        assertEquals("title TH", actualCustomError.getTitle().getTh());
    }

    @Test
    void testValidateAutoLoanPaymentWhenResponseDataIsNullShouldThrowTMBCommonException() {
        TmbServiceResponse<TopUpETEResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(null);

        when(paymentServiceClient.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> paymentService.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidateAutoLoanPaymentWhenFeignExceptionShouldThrowException() {
        when(paymentServiceClient.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest))
                .thenThrow(FeignException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> paymentService.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidateAutoLoanPaymentWhenGeneralExceptionShouldThrowException() {
        when(paymentServiceClient.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest))
                .thenThrow(RuntimeException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> paymentService.validateAutoLoanPayment(correlationId, crmId, topUpETEPaymentRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testConfirmAutoLoanPaymentWhenSuccessShouldReturnTopUpETEResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
        TopUpETEResponse expectedResponse = new TopUpETEResponse();
        TopUpETETransaction transaction = new TopUpETETransaction();
        transaction.setAmount(new BigDecimal("1500.00"));
        expectedResponse.setTransaction(transaction);
        expectedResponse.setStatus(new com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.autoloan.Status("0000", "success"));

        TmbServiceResponse<TopUpETEResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(expectedResponse);

        when(paymentServiceClient.confirmAutoLoanPayment(correlationId, crmId, transactionId, topUpETEPaymentRequest))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TopUpETEResponse result = paymentService.confirmAutoLoanPayment(correlationId, crmId, transactionId, topUpETEPaymentRequest);

        assertNotNull(result);
        assertNotNull(result.getTransaction());
        assertEquals(new BigDecimal("1500.00"), result.getTransaction().getAmount());
    }

    @Test
    void testConfirmAutoLoanPaymentWhenStatusCodeNotSuccessShouldThrowTMBCommonExceptionWithSpecificErrorCode() {
        String errorCodeFromETE = "ete_557";
        Description errorDescriptionFromETE = new Description();
        errorDescriptionFromETE.setEn("error EN from ETE");
        errorDescriptionFromETE.setTh("error TH from ETE");

        mockGetErrorTitle(errorCodeFromETE);
        mockGetErrorCustomDescription(errorCodeFromETE);
        TmbServiceResponse<TopUpETEResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(new Status(errorCodeFromETE, ResponseCode.FAILED_V2.getMessage(), ResponseCode.FAILED_V2.getService(), errorDescriptionFromETE));
        tmbServiceResponse.setData(null);

        when(paymentServiceClient.confirmAutoLoanPayment(correlationId, crmId, transactionId, topUpETEPaymentRequest))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonExceptionWithResponse exception = assertThrows(TMBCommonExceptionWithResponse.class,
                () -> paymentService.confirmAutoLoanPayment(correlationId, crmId, transactionId, topUpETEPaymentRequest));

        assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
        ExternalErrorCustomDescription actualCustomError = (ExternalErrorCustomDescription) exception.getData();
        assertEquals("custom description EN", actualCustomError.getCustomDescription().getEn());
        assertEquals("custom description TH", actualCustomError.getCustomDescription().getTh());
        assertEquals("title EN", actualCustomError.getTitle().getEn());
        assertEquals("title TH", actualCustomError.getTitle().getTh());
    }

    private void mockGetErrorTitle(String errorCodeFromETE) {
        Description title = new Description();
        title.setEn("title EN");
        title.setTh("title TH");
        when(errorMappingHelper.get(errorCodeFromETE + "_title")).thenReturn(title);
    }

    private void mockGetErrorCustomDescription(String errorCodeFromETE) {
        Description customDescription = new Description();
        customDescription.setEn("custom description EN");
        customDescription.setTh("custom description TH");
        when(errorMappingHelper.get(errorCodeFromETE + "_desc")).thenReturn(customDescription);
    }

    @Test
    void testConfirmAutoLoanPaymentWhenFeignExceptionShouldThrowException() {
        when(paymentServiceClient.confirmAutoLoanPayment(correlationId, crmId, transactionId, topUpETEPaymentRequest))
                .thenThrow(FeignException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> paymentService.confirmAutoLoanPayment(correlationId, crmId, transactionId, topUpETEPaymentRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testConfirmAutoLoanPaymentWhenGeneralExceptionShouldThrowException() {
        when(paymentServiceClient.confirmAutoLoanPayment(correlationId, crmId, transactionId, topUpETEPaymentRequest))
                .thenThrow(RuntimeException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> paymentService.confirmAutoLoanPayment(correlationId, crmId, transactionId, topUpETEPaymentRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidateEWalletPaymentSuccess() {
        TmbServiceResponse<EWalletETEResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new EWalletETEResponse());
        when(paymentServiceClient.validateEWalletPayment(eq(correlationId), eq(crmId), any(EWalletETERequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> paymentService.validateEWalletPayment(correlationId, crmId, new EWalletETERequest()));
    }

    @Test
    void testValidateEWalletPaymentWhenFailedExceptionShouldThrowTMBCommonException() {
        when(paymentServiceClient.validateEWalletPayment(eq(correlationId), eq(crmId), any(EWalletETERequest.class))).thenThrow(FeignException.class);

        Assertions.assertThrows(TMBCommonException.class, () -> paymentService.validateEWalletPayment(correlationId, crmId, new EWalletETERequest()));
    }

    @Test
    void testConfirmEWalletPaymentSuccess() {
        TmbServiceResponse<EWalletETEResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new EWalletETEResponse());
        when(paymentServiceClient.confirmEWalletPayment(eq(correlationId), eq(crmId), eq(transactionId), any(EWalletETERequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> paymentService.confirmEWalletPayment(correlationId, crmId, transactionId, new EWalletETERequest()));
    }

    @Test
    void testConfirmEWalletPaymentWhenFailedExceptionShouldThrowTMBCommonException() {
        when(paymentServiceClient.confirmEWalletPayment(eq(correlationId), eq(crmId), eq(transactionId), any(EWalletETERequest.class))).thenThrow(FeignException.class);

        Assertions.assertThrows(TMBCommonException.class, () -> paymentService.confirmEWalletPayment(correlationId, crmId, transactionId, new EWalletETERequest()));
    }

    @Test
    void testConfirmCreditCardPaymentWhenSuccessShouldReturnCreditCardConfirmResponse() throws TMBCommonException, TMBCommonExceptionWithResponse {
        CreditCardConfirmResponse expectedResponse = new CreditCardConfirmResponse();
        BillPaymentCreditCard billPayment = new BillPaymentCreditCard();
        billPayment.setAmount("2000.00");
        billPayment.setCompCode("creditCardCompCode");
        expectedResponse.setBillPayment(billPayment);

        TmbServiceResponse<CreditCardConfirmResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(expectedResponse);

        when(paymentServiceClient.confirmCreditCardPayment(correlationId, crmId, transactionId, creditCardConfirmRequest))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));

        CreditCardConfirmResponse result = paymentService.confirmCreditCardPayment(correlationId, crmId, transactionId, creditCardConfirmRequest);

        assertNotNull(result);
        assertNotNull(result.getBillPayment());
        assertEquals("2000.00", result.getBillPayment().getAmount());
        assertEquals("creditCardCompCode", result.getBillPayment().getCompCode());
    }

    @Test
    void testConfirmCreditCardPaymentWhenStatusCodeNotSuccessShouldThrowTMBCommonExceptionWithSpecificErrorCode() {
        String errorCodeFromETE = "ete_557";
        Description errorDescriptionFromETE = new Description();
        errorDescriptionFromETE.setEn("error EN from ETE");
        errorDescriptionFromETE.setTh("error TH from ETE");

        mockGetErrorTitle(errorCodeFromETE);
        mockGetErrorCustomDescription(errorCodeFromETE);
        TmbServiceResponse<CreditCardConfirmResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(new Status(errorCodeFromETE, ResponseCode.FAILED_V2.getMessage(),
                ResponseCode.FAILED_V2.getService(), errorDescriptionFromETE));
        tmbServiceResponse.setData(null);

        when(paymentServiceClient.confirmCreditCardPayment(correlationId, crmId, transactionId, creditCardConfirmRequest))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonExceptionWithResponse exception = assertThrows(TMBCommonExceptionWithResponse.class,
                () -> paymentService.confirmCreditCardPayment(correlationId, crmId, transactionId, creditCardConfirmRequest));

        assertEquals(ResponseCode.ETE_ERROR.getCode(), exception.getErrorCode());
        ExternalErrorCustomDescription actualCustomError = (ExternalErrorCustomDescription) exception.getData();
        assertEquals("custom description EN", actualCustomError.getCustomDescription().getEn());
        assertEquals("custom description TH", actualCustomError.getCustomDescription().getTh());
        assertEquals("title EN", actualCustomError.getTitle().getEn());
        assertEquals("title TH", actualCustomError.getTitle().getTh());
    }

    @Test
    void testConfirmCreditCardPaymentWhenResponseDataIsNullShouldThrowTMBCommonException() {
        TmbServiceResponse<CreditCardConfirmResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(null);

        when(paymentServiceClient.confirmCreditCardPayment(correlationId, crmId, transactionId, creditCardConfirmRequest))
                .thenReturn(ResponseEntity.ok(tmbServiceResponse));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> paymentService.confirmCreditCardPayment(correlationId, crmId, transactionId, creditCardConfirmRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testConfirmCreditCardPaymentWhenFeignExceptionShouldThrowException() {
        when(paymentServiceClient.confirmCreditCardPayment(correlationId, crmId, transactionId, creditCardConfirmRequest))
                .thenThrow(FeignException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> paymentService.confirmCreditCardPayment(correlationId, crmId, transactionId, creditCardConfirmRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testConfirmCreditCardPaymentWhenGeneralExceptionShouldThrowException() {
        when(paymentServiceClient.confirmCreditCardPayment(correlationId, crmId, transactionId, creditCardConfirmRequest))
                .thenThrow(RuntimeException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> paymentService.confirmCreditCardPayment(correlationId, crmId, transactionId, creditCardConfirmRequest));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidateBillPromptPayPaymentSuccess() {
        TmbServiceResponse<PromptPayETEValidateResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new PromptPayETEValidateResponse());
        when(paymentServiceClient.validateBillPromptPayPayment(eq(correlationId), eq(crmId), any(PromptPayETEValidateRequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> paymentService.validateBillPromptPayPayment(correlationId, crmId, new PromptPayETEValidateRequest()));
    }

    @Test
    void testValidateBillPromptPayPaymentWhenFailedExceptionShouldThrowTMBCommonException() {
        when(paymentServiceClient.validateBillPromptPayPayment(eq(correlationId), eq(crmId), any(PromptPayETEValidateRequest.class))).thenThrow(FeignException.class);

        Assertions.assertThrows(TMBCommonException.class, () -> paymentService.validateBillPromptPayPayment(correlationId, crmId, new PromptPayETEValidateRequest()));
    }

    @Test
    void testConfirmBillPromptPayPaymentSuccess() {
        TmbServiceResponse<PromptPayETEConfirmResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new PromptPayETEConfirmResponse());
        when(paymentServiceClient.confirmBillPromptPayPayment(eq(correlationId), eq(crmId), eq(transactionId), any(PromptPayETEConfirmRequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> paymentService.confirmBillPromptPayPayment(correlationId, crmId, transactionId, new PromptPayETEConfirmRequest()));
    }

    @Test
    void testConfirmBillPromptPayPaymentWhenFailedExceptionShouldThrowTMBCommonException() {
        when(paymentServiceClient.confirmBillPromptPayPayment(eq(correlationId), eq(crmId), eq(transactionId), any(PromptPayETEConfirmRequest.class))).thenThrow(FeignException.class);

        Assertions.assertThrows(TMBCommonException.class, () -> paymentService.confirmBillPromptPayPayment(correlationId, crmId, transactionId, new PromptPayETEConfirmRequest()));
    }

    @Test
    void testValidateBillPromptPayISO20022PaymentSuccess() {
        TmbServiceResponse<PromptPayETEValidateResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new PromptPayETEValidateResponse());
        when(paymentServiceClient.validateBillPromptPayISO20022Payment(eq(correlationId), eq(crmId), any(PromptPayETEValidateRequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> paymentService.validateBillPromptPayISO20022Payment(correlationId, crmId, new PromptPayETEValidateRequest()));
    }

    @Test
    void testValidateBillPromptPayISO20022PaymentWhenFailedExceptionShouldThrowTMBCommonException() {
        when(paymentServiceClient.validateBillPromptPayISO20022Payment(eq(correlationId), eq(crmId), any(PromptPayETEValidateRequest.class))).thenThrow(FeignException.class);

        Assertions.assertThrows(TMBCommonException.class, () -> paymentService.validateBillPromptPayISO20022Payment(correlationId, crmId, new PromptPayETEValidateRequest()));
    }

    @Test
    void testConfirmBillPromptPayISO20022PaymentSuccess() {
        TmbServiceResponse<PromptPayETEConfirmResponse> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new PromptPayETEConfirmResponse());
        when(paymentServiceClient.confirmBillPromptPayISO20022Payment(eq(correlationId), eq(crmId), eq(transactionId), any(PromptPayETEConfirmRequest.class))).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> paymentService.confirmBillPromptPayISO20022Payment(correlationId, crmId, transactionId, new PromptPayETEConfirmRequest()));
    }

    @Test
    void testConfirmBillPromptPayISO20022PaymentWhenFailedExceptionShouldThrowTMBCommonException() {
        when(paymentServiceClient.confirmBillPromptPayISO20022Payment(eq(correlationId), eq(crmId), eq(transactionId), any(PromptPayETEConfirmRequest.class))).thenThrow(FeignException.class);

        Assertions.assertThrows(TMBCommonException.class, () -> paymentService.confirmBillPromptPayISO20022Payment(correlationId, crmId, transactionId, new PromptPayETEConfirmRequest()));
    }
}
