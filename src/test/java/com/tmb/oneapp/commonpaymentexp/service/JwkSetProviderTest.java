package com.tmb.oneapp.commonpaymentexp.service;

import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.tmb.common.exception.model.TMBCommonException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.text.ParseException;
import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.*;

class JwkSetProviderTest {

    private JwkSetProvider jwkSetProvider;
    private Map<String, JWKSet> jwkSetMap;

    private final String validJwkSetJson = "{\"keys\":[{\"kty\":\"RSA\",\"e\":\"AQAB\",\"kid\":\"test-key-1\",\"alg\":\"RS256\",\"n\":\"...\"}]}";
    private final String partnerName = "test-partner";
    private final String keyId = "test-key-1";

    @BeforeEach
    void setUp() throws ParseException {
        jwkSetMap = new ConcurrentHashMap<>();
        JWKSet jwkSet = JWKSet.parse(validJwkSetJson);
        jwkSetMap.put(partnerName, jwkSet);
        jwkSetProvider = new JwkSetProvider(jwkSetMap);
    }

    @Test
    void testGetJwkSet_Success() {
        JWKSet jwkSet = assertDoesNotThrow(() -> jwkSetProvider.getJwkSet(partnerName));

        assertNotNull(jwkSet);
        assertEquals(1, jwkSet.getKeys().size());
    }

    @Test
    void testGetJwkSet_NotFound_shouldThrowException() {
        assertThrows(TMBCommonException.class, () -> {
            jwkSetProvider.getJwkSet("unknown-partner");
        });
    }

    @Test
    void testGetKeyById_Success() {
        JWK jwk = assertDoesNotThrow(() -> jwkSetProvider.getKeyById(partnerName, keyId));

        assertNotNull(jwk);
        assertEquals(keyId, jwk.getKeyID());
    }

    @Test
    void testGetKeyById_KeyNotFound_shouldThrowException() {
        assertThrows(TMBCommonException.class, () -> {
            jwkSetProvider.getKeyById(partnerName, "unknown-key");
        });
    }

    @Test
    void testGetJwkSet_EmptyMap() {
        jwkSetProvider = new JwkSetProvider(Collections.emptyMap());
        assertThrows(TMBCommonException.class, () -> {
            jwkSetProvider.getJwkSet(partnerName);
        });
    }
}
