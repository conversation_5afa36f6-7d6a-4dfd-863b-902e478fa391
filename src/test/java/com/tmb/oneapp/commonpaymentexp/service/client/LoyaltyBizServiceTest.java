package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.LoyaltyBizClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointRedeemConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import feign.FeignException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LoyaltyBizServiceTest {

    @Mock
    private FeignClientHelper feignClientHelper;

    @Mock
    private LoyaltyBizClient loyaltyBizClient;

    @InjectMocks
    private LoyaltyBizService loyaltyBizService;

    @BeforeEach
    void setUp() throws TMBCommonException {
        TestUtils.setUpFeignClientHelperExecuteRequestNullableOrElseThrow(feignClientHelper);
    }

    @Test
    void redeemPointSuccessTest() {
        when(loyaltyBizClient.redeemPoint(any(), any())).thenReturn(ResponseEntity.ok(new TmbServiceResponse<>()));

        Assertions.assertDoesNotThrow(() -> loyaltyBizService.redeemPoint(new HttpHeaders(), new WowPointRedeemConfirmRequest()));
    }

    @Test
    void redeemPointFailedWhenFeignExceptionTest() {
        when(loyaltyBizClient.redeemPoint(any(), any())).thenThrow(FeignException.class);

        TMBCommonException actual = Assertions.assertThrows(TMBCommonException.class, () -> loyaltyBizService.redeemPoint(new HttpHeaders(), new WowPointRedeemConfirmRequest()));
        Assertions.assertEquals(ResponseCode.FAIL_TO_REDEEM_WOW_POINT.getCode(), actual.getErrorCode());
    }
}