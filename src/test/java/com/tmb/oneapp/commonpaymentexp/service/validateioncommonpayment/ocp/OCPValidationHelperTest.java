package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.BillerCreditcardMerchant;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CompleteScreenDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PhraseDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CardBalancesAsync;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.AccountCreditCardService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OCPValidationHelperTest {
    @InjectMocks
    private OCPValidationHelper ocpValidationHelper;

    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private PaymentService paymentService;
    @Mock
    private AccountCreditCardService accountCreditCardService;
    @Mock
    private CustomerService customerService;
    @Mock
    private BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;

    private String crmId;
    private String correlationId;
    private HttpHeaders headers;
    private ValidationCommonPaymentRequest request;
    private CommonPaymentDraftCache cacheData;
    private String compCode;

    @BeforeEach
    void setUp() throws TMBCommonException {
        TestUtils.setUpAsyncHelperExecuteMethodAsync(asyncHelper);

        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        compCode = "compCode";
    }

    @Nested
    class BasePrepareDataTest {
        @Test
        void testBasePrepareData_WhenSuccessIsNotPayWithCreditCard_ShouldDoesNotThrow() throws TMBCommonException {
            CommonPaymentDraftCache cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            ValidationCommonPaymentRequest request = initialDefaultRequest();
            mockFetchBillPayConfig();
            mockFetchMasterBillerByCompCode();
            mockFetchCustomerCrmProfile();
            mockGetAccountByAccountNumber();

            assertDoesNotThrow(() -> ocpValidationHelper.basePrepareData(request, headers, cacheData));

            verify(accountCreditCardService, never()).getCreditCardAccountWithAccountId(anyString(), anyString(), anyString());
        }

        @Test
        void testBasePrepareData_WhenSuccessIsPayWithCreditCard_ShouldDoesNotThrow() throws TMBCommonException {
            CommonPaymentDraftCache cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            ValidationCommonPaymentRequest request = initialDefaultRequest()
                    .setDeposit(new DepositValidationCommonPaymentRequest().setPayWithDepositFlag(false))
                    .setCreditCard(new CreditCardValidationCommonPaymentRequest()
                    .setPayWithCreditCardFlag(true)
                    .setAccountId("accountId"));
            mockFetchBillPayConfig();
            mockFetchMasterBillerByCompCode();
            mockFetchCustomerCrmProfile();
            mockGetCreditCardAccountsWithAccountId();

            assertDoesNotThrow(() -> ocpValidationHelper.basePrepareData(request, headers, cacheData));

            verify(billPayAccountCommonPaymentService, never()).getAccountList(anyString(), anyString());
        }

        @Test
        void testBasePrepareData_WhenFailedFetchBillPayConfig_ShouldThrowTMBCommonException() throws TMBCommonException {
            TMBCommonException throwException = new TMBCommonException("error_code", "error_msg", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);

            cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            request = initialDefaultRequest();

            when(paymentService.getBillPayConfig(correlationId)).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> ocpValidationHelper.basePrepareData(request, headers, cacheData));

            assertEquals(throwException.getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testBasePrepareData_WhenFailedFetchMasterBillerByCompCode_ShouldThrowTMBCommonException() throws TMBCommonException {
            TMBCommonException throwException = new TMBCommonException("error_code", "error_msg", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);

            cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            request = initialDefaultRequest();

            mockFetchBillPayConfig();
            when(paymentService.getMasterBiller(correlationId, compCode)).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> ocpValidationHelper.basePrepareData(request, headers, cacheData));

            assertEquals(throwException.getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testBasePrepareData_WhenFailedFetchCustomerCrmProfile_ShouldThrowTMBCommonException() throws TMBCommonException {
            TMBCommonException throwException = new TMBCommonException("error_code", "error_msg", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);

            cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            request = initialDefaultRequest();

            mockFetchBillPayConfig();
            mockFetchMasterBillerByCompCode();
            when(customerService.getCustomerCrmProfile(correlationId, crmId)).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> ocpValidationHelper.basePrepareData(request, headers, cacheData));

            assertEquals(throwException.getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testBasePrepareData_WhenFailedAccountByAccountNumber_ShouldThrowTMBCommonException() throws TMBCommonException {
            TMBCommonException throwException = new TMBCommonException("error_code", "error_msg", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);

            cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            request = initialDefaultRequest();

            mockFetchBillPayConfig();
            mockFetchMasterBillerByCompCode();
            mockFetchCustomerCrmProfile();
            when(billPayAccountCommonPaymentService.getAccountByAccountNumber(anyString(), eq(headers))).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> ocpValidationHelper.basePrepareData(request, headers, cacheData));

            assertEquals(throwException.getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testBasePrepareData_WhenFailedGetCreditCardAccountWithAccountId_ShouldThrowTMBCommonException() throws TMBCommonException {
            TMBCommonException throwException = new TMBCommonException("error_code", "error_msg", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);

            cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            request = initialDefaultRequest()
                    .setDeposit(null)
                    .setCreditCard(new CreditCardValidationCommonPaymentRequest()
                            .setAccountId("accountId")
                            .setPayWithCreditCardFlag(true)
                    );
            mockFetchBillPayConfig();
            mockFetchMasterBillerByCompCode();
            mockFetchCustomerCrmProfile();
            when(accountCreditCardService.getCreditCardAccountWithAccountId(correlationId, crmId, request.getCreditCard().getAccountId())).thenThrow(throwException);

            TMBCommonException exception = assertThrows(TMBCommonException.class, () -> ocpValidationHelper.basePrepareData(request, headers, cacheData));

            assertEquals(throwException.getErrorCode(), exception.getErrorCode());
        }

        @Test
        void testBasePrepareData_WhenFailedInterruptedException_ShouldThrowTMBCommonException() throws TMBCommonException {
            CompletableFuture<Object> failedFuture = CompletableFuture.failedFuture(new InterruptedException(""));

            CommonPaymentDraftCache cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            ValidationCommonPaymentRequest request = initialDefaultRequest();

            when(asyncHelper.executeMethodAsync(any())).thenReturn(failedFuture);

            assertThrows(TMBCommonException.class, () -> ocpValidationHelper.basePrepareData(request, headers, cacheData));
        }

        private void mockGetCreditCardAccountsWithAccountId() throws TMBCommonException {
            CreditCardSupplementary creditCardDetail = new CreditCardSupplementary();
            creditCardDetail.setCardNo("CardNo");
            creditCardDetail.setAccountId("0000000050083520705000171");
            creditCardDetail.setExpiredBy("ExpiredBy");
            creditCardDetail.setCardEmbossingName1("cardEmbossingName1");
            creditCardDetail.setProductCode("ProductId");
            creditCardDetail.setCardBalances(new CardBalancesAsync());
            creditCardDetail.getCardBalances().setAvailableCreditAllowance(BigDecimal.valueOf(999999.99));
            when(accountCreditCardService.getCreditCardAccountWithAccountId(eq(correlationId), eq(crmId), anyString())).thenReturn(creditCardDetail);
        }

        private void mockFetchBillPayConfig() throws TMBCommonException {
            BillerCreditcardMerchant e1 = new BillerCreditcardMerchant();
            e1.setMerchantId("merchantId");
            List<BillerCreditcardMerchant> billerCreditCardMerchant = List.of(e1);
            BillPayConfiguration billPayConfig = new BillPayConfiguration().setBillerCreditcardMerchant(billerCreditCardMerchant);

            when(paymentService.getBillPayConfig(correlationId)).thenReturn(billPayConfig);
        }

        private void mockFetchMasterBillerByCompCode() throws TMBCommonException {
            BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
            billerInfoResponse.setStartTime(null);
            billerInfoResponse.setEndTime(null);
            billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
            billerInfoResponse.setPaymentMethod("5");
            billerInfoResponse.setBillerMethod("0");
            billerInfoResponse.setNameEn("PB_PRUDENTIAL LIFE ASSURANCE (THAILAND) PCL");
            billerInfoResponse.setBillerCategoryCode("03");

            MasterBillerResponse masterBiller = new MasterBillerResponse()
                    .setBillerInfo(billerInfoResponse)
                    .setRef1(new ReferenceResponse().setIsMobile(false));
            when(paymentService.getMasterBiller(correlationId, compCode)).thenReturn(masterBiller);
        }

        private void mockGetAccountByAccountNumber() throws TMBCommonException {
            DepositAccount depositAccount = new DepositAccount()
                    .setAccountNumber("**********")
                    .setAccountType("SDA")
                    .setAvailableBalance(BigDecimal.valueOf(999999.99));
            when(billPayAccountCommonPaymentService.getAccountByAccountNumber(anyString(), any(HttpHeaders.class))).thenReturn(depositAccount);
        }

        private void mockFetchCustomerCrmProfile() throws TMBCommonException {
            CustomerCrmProfile customerCrmProfile = new CustomerCrmProfile()
                    .setPaymentAccuUsgAmt(BigDecimal.valueOf(100.50))
                    .setBillpayAccuUsgAmt(BigDecimal.valueOf(2000.50))
                    .setEbAccuUsgAmtDaily(2000.50);
            when(customerService.getCustomerCrmProfile(correlationId, crmId)).thenReturn(customerCrmProfile);
        }
    }

    private CommonPaymentDraftCache initialCacheDataForBillPayTransaction() {
        PaymentInformation p = new PaymentInformation()
                .setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY)
                .setTransactionType("bill_pay")
                .setCompCode(compCode)
                .setFundCode("ignore")
                .setRequireAddressFlag(true)
                .setProductDetail(new ProductDetail()
                        .setProductNameEn("Product-name-en-value")
                        .setProductNameTh("Product-name-th-value")
                        .setProductAttributeList(List.of(
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("1000000006")
                                        .setValueTh("1000000006"),
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("0987834783")
                                        .setValueTh("0987834783")
                        ))
                        .setProductRef1("reference1")
                        .setProductRef2("reference2")
                )
                .setAmountDetail(new AmountDetail()
                        .setAmountLabelEn("Amount-lable-en-value")
                        .setAmountLabelTh("Amount-lable-th-value")
                        .setAmountUnitEn("Amount-unit-en-value")
                        .setAmountUnitTh("Amount-unit-th-value")
                        .setAmountValue(BigDecimal.valueOf(9855.00))
                )
                .setCompleteScreenDetail(new CompleteScreenDetail()
                        .setRemarkEn("Remark-en-value")
                        .setRemarkTh("Remark-th-value")
                        .setFooterEn("Footer-en-value")
                        .setFooterTh("Footer-th-value")
                        .setBackBtnKeyEn("Back-btn-key-en-value")
                        .setBackBtnKeyTh("Back-btn-key-th-value")
                        .setBackBtnUrl("www.google.co.th")
                );

        return new CommonPaymentDraftCache()
                .setPaymentInformation(p)
                .setCrmId(crmId)
                .setCommonPaymentConfig(new CommonPaymentConfig());
    }

    private ValidationCommonPaymentRequest initialDefaultRequest() {
        request = new ValidationCommonPaymentRequest();
        request.setDeposit(new DepositValidationCommonPaymentRequest().setPayWithDepositFlag(true).setAccountNumber("**********").setAmount(BigDecimal.valueOf(9500.50)));
        request.setCreditCard(new CreditCardValidationCommonPaymentRequest().setPayWithCreditCardFlag(false));
        request.setFlow("flow");
        request.setTransactionId("transaction_id");
        return request;
    }
}