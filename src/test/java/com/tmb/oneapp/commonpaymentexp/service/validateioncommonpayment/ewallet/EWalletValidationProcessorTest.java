package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ewallet;

import com.tmb.common.cache.Transaction;
import com.tmb.common.cache.service.TransactionServices;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.CustomerServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.ThrowableSupplier;
import com.tmb.oneapp.commonpaymentexp.model.TransferConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CompleteScreenDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PhraseDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETERequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.EWalletETEResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.billpromptpay.Sender;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.client.TransferService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.AsyncHelper;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Supplier;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_TOP_UP;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_E_WALLET;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_TOP_UP_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_TOP_UP_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_TOPUP_E_WALLET;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class EWalletValidationProcessorTest {
    @InjectMocks
    private EWalletValidationProcessor eWalletValidationProcessor;

    @Mock
    private AsyncHelper asyncHelper;
    @Mock
    private PaymentService paymentService;
    @Mock
    private CustomerServiceClient customerServiceClient;
    @Mock
    private CustomerService customerService;
    @Mock
    private BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private CacheService cacheService;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    @Mock
    private TransferService transferService;
    @Mock
    private TransactionServices transactionServices;

    String crmId;
    String correlationId;
    HttpHeaders headers;
    ValidationCommonPaymentRequest request;
    CommonPaymentDraftCache cacheData;
    CommonPaymentConfig commonPaymentConfig;
    String compCode;
    String depositAccountNumber;
    MasterBillerResponse masterBillerResponse;
    TransferConfiguration transferConfiguration;

    @BeforeEach
    void setUp() {
        Transaction.setTransactionServices(transactionServices);

        ReflectionTestUtils.setField(eWalletValidationProcessor, "amloAmountValidate", new BigDecimal("700000"));

        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        compCode = BILL_COMP_CODE_E_WALLET;
        depositAccountNumber = "**********";
        cacheData = new CommonPaymentDraftCache();
        compCode = "3058";
        request = new ValidationCommonPaymentRequest();
        commonPaymentConfig = new CommonPaymentConfig();
        masterBillerResponse = new MasterBillerResponse().setBillerInfo(new BillerInfoResponse());
        transferConfiguration = new TransferConfiguration();
        transferConfiguration.setPromptPayProxyWaiveFee("Y");
    }

    @Test
    void testGetProcessorTypeWhenCalledThenReturnEWalletBillPayment() {
        String actual = eWalletValidationProcessor.getProcessorType();
        assertEquals(BILLER_PAYMENT_TOPUP_E_WALLET, actual);
    }

    @Test
    void testExecuteEWallet_WhenPayWithDeposit_ShouldSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse {
        cacheData = initialCacheDataForBillPayTransaction();
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_TOP_UP);
        setUpAsyncHelperExecuteRequestAsync();
        setUpAsyncHelperExecuteMethodAsync();

        mockFetchMasterBillerByCompCode(masterBillerResponse);
        mockFetchCustomerCrmProfile();
        mockGetAccountByAccountNumber();
        mockFetchTransferModuleConfig();
        mockValidateDailyLimitExceededDoNothing();
        mockStaticTransactionGenerateId();
        mockValidateEWalletPayment();
        mockValidateIsRequireCommonAuthForTopUpReturnRequireCommonAuth();

        ValidationCommonPaymentResponse actual = eWalletValidationProcessor.executeValidate(request, headers, cacheData);

        Assertions.assertNotNull(actual.getFee());
        Assertions.assertNotNull(actual.getTransactionId());
        Assertions.assertNotNull(actual.getTotalAmount());
        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertEquals(COMMON_PAYMENT_TOP_UP_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
        Assertions.assertEquals(COMMON_AUTH_TOP_UP_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
    }

    @Test
    void testExecuteEWallet_WhenAmountMoreThanAmloAmountValidate_ShouldGetCustomerKYCForGetTaxIdSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
        BigDecimal amountMoreThanAmlo = new BigDecimal("700001");
        cacheData = initialCacheDataForBillPayTransaction();
        request = initialDefaultRequest();
        request.getDeposit().setAmount(amountMoreThanAmlo);
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_TOP_UP);
        setUpAsyncHelperExecuteRequestAsync();
        setUpAsyncHelperExecuteMethodAsync();

        mockFetchMasterBillerByCompCode(masterBillerResponse);
        mockFetchCustomerCrmProfile();
        mockGetAccountByAccountNumber();
        mockFetchTransferModuleConfig();
        mockValidateDailyLimitExceededDoNothing();
        mockStaticTransactionGenerateId();
        mockValidateEWalletPayment();
        mockValidateIsRequireCommonAuthForTopUpReturnRequireCommonAuth();

        Assertions.assertDoesNotThrow(() -> eWalletValidationProcessor.executeValidate(request, headers, cacheData));

        verify(customerService, times(1)).getCustomerKYC(eq(correlationId), eq(crmId));
    }

    @Test
    void testExecuteEWallet_WhenFailedTMBCommonExceptionAtPrePareDateExecuteRequestAsync_ShouldThrowTMBCommonException() throws TMBCommonException {
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_BILL);
        setUpAsyncHelperExecuteMethodAsync();
        mockFetchTransferModuleConfig();
        mockFetchMasterBillerByCompCode(masterBillerResponse);
        mockGetAccountByAccountNumber();

        TMBCommonException expectedException = new TMBCommonException(ResponseCode.FAILED_V2.getCode(), "message", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);
        CompletableFuture<Object> failedFuture = CompletableFuture.failedFuture(expectedException);
        when(asyncHelper.executeRequestAsync(any())).thenReturn(failedFuture);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eWalletValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(expectedException, exception);
    }

    @Test
    void testExecuteEWallet_WhenFailedTMBCommonExceptionAtPrePareDateExecuteMethodAsync_ShouldThrowTMBCommonException() throws TMBCommonException {
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
        request = initialDefaultRequest();
        setUpAsyncHelperExecuteRequestAsync();
        mockFetchCustomerCrmProfile();

        TMBCommonException expectedException = new TMBCommonException(ResponseCode.FAILED_V2.getCode(), "message", "service", HttpStatus.INTERNAL_SERVER_ERROR, null);
        CompletableFuture<Object> failedFuture = CompletableFuture.failedFuture(expectedException);
        when(asyncHelper.executeMethodAsync(any())).thenReturn(failedFuture);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eWalletValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(expectedException, exception);
    }

    @Test
    void testExecuteEWallet_WhenFailedNullPointerExceptionAtPrePareDateExecuteMethodAsync_ShouldThrowTMBCommonException() throws TMBCommonException {
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
        request = initialDefaultRequest();
        setUpAsyncHelperExecuteRequestAsync();
        mockFetchCustomerCrmProfile();

        CompletableFuture<Object> failedFuture = CompletableFuture.failedFuture(new NullPointerException("got null on execute method async"));
        when(asyncHelper.executeMethodAsync(any())).thenReturn(failedFuture);

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eWalletValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
    }

    @Test
    void testExecuteEWallet_WhenDailyLimitExceed_ShouldThrowTMBCommonException() throws TMBCommonException {
        cacheData = initialCacheDataForBillPayTransaction();
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_TOP_UP);
        setUpAsyncHelperExecuteRequestAsync();
        setUpAsyncHelperExecuteMethodAsync();

        mockFetchMasterBillerByCompCode(masterBillerResponse);
        mockFetchCustomerCrmProfile();
        mockGetAccountByAccountNumber();
        mockFetchTransferModuleConfig();
        mockValidateDailyLimitExceededThrowTMBCommonException();

        Assertions.assertThrows(TMBCommonException.class, () -> eWalletValidationProcessor.executeValidate(request, headers, cacheData));
    }

    @Test
    void testExecuteEWallet_WhenNullPointerException_ShouldThrowTMBCommonException() throws TMBCommonException {
        cacheData = initialCacheDataForBillPayTransaction();
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_TOP_UP);
        setUpAsyncHelperExecuteRequestAsync();
        setUpAsyncHelperExecuteMethodAsync();

        mockFetchMasterBillerByCompCode(masterBillerResponse);
        mockFetchCustomerCrmProfile();
        mockGetAccountByAccountNumber();
        mockFetchTransferModuleConfig();
        doThrow(NullPointerException.class).when(dailyLimitService).validateDailyLimitExceeded(anyString(), any(CustomerCrmProfile.class), any(BigDecimal.class));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> eWalletValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
    }

    @Test
    void testCalculateFee_WhenWaiveFeeFromTransferConfig_ShouldReturnZero() {
        String waiveFromConfig = "Y";
        transferConfiguration.setPromptPayProxyWaiveFee(waiveFromConfig);
        EWalletETEResponse eteResponse = new EWalletETEResponse();
        eteResponse.setFee(new BigDecimal("10.50"));
        DepositAccount fromDepositAccount = new DepositAccount();

        assertEquals(new BigDecimal("0.00"), invokeCalculateFee(eteResponse, transferConfiguration, fromDepositAccount));
    }

    @Test
    void testCalculateFee_WhenWaiveFeeFromDepositAccount_ShouldReturnZero() {
        String waiveFromAccount = "1";
        transferConfiguration.setPromptPayProxyWaiveFee("N");
        EWalletETEResponse eteResponse = new EWalletETEResponse();
        eteResponse.setFee(new BigDecimal("10.50"));
        DepositAccount fromDepositAccount = new DepositAccount();
        fromDepositAccount.setWaiveFeeForPromptPay(waiveFromAccount);

        assertEquals(new BigDecimal("0.00"), invokeCalculateFee(eteResponse, transferConfiguration, fromDepositAccount));
    }

    @Test
    void testCalculateFee_WhenOwnAccount_ShouldReturnZero() {
        EWalletETEResponse eteResponse = new EWalletETEResponse();
        eteResponse.setFee(null);

        assertEquals(new BigDecimal("0.00"), invokeCalculateFee(eteResponse, transferConfiguration, new DepositAccount()));
    }

    @Test
    void testCalculateFee_WhenNotWaiveFee_ShouldReturnFee() {
        BigDecimal expectedFee = new BigDecimal("10.50");
        transferConfiguration.setPromptPayProxyWaiveFee("N");
        EWalletETEResponse eteResponse = new EWalletETEResponse();
        eteResponse.setFee(expectedFee);
        DepositAccount fromDepositAccount = new DepositAccount();
        fromDepositAccount.setWaiveFeeForPromptPay("0");

        assertEquals(expectedFee, invokeCalculateFee(eteResponse, transferConfiguration, fromDepositAccount));
    }

    @Nested
    class HandleExceptionTest {
        @Test
        void testHandleException_WhenFailedTMBCommonException_ShouldThrowTMBCommonException() {
            assertThrows(TMBCommonException.class, () -> eWalletValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonException("ignore message")));
        }

        @Test
        void testHandleException_WhenFailedTMBCommonExceptionWithResponse_ShouldThrowTMBCommonExceptionWithResponse() {
            assertThrows(TMBCommonExceptionWithResponse.class, () -> eWalletValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonExceptionWithResponse("", "", "", HttpStatus.OK, null, null)));
        }
    }

    private BigDecimal invokeCalculateFee(EWalletETEResponse eteResponse, TransferConfiguration transferConfiguration, DepositAccount fromDepositAccount) {
        return ReflectionTestUtils.invokeMethod(
                eWalletValidationProcessor, "calculateFee",
                eteResponse, transferConfiguration, fromDepositAccount);
    }

    private void mockFetchTransferModuleConfig() throws TMBCommonException {
        when(transferService.fetchTransferModuleConfig(correlationId)).thenReturn(transferConfiguration);
    }

    private void mockValidateEWalletPayment() throws TMBCommonException, TMBCommonExceptionWithResponse {
        EWalletETEResponse eteResponse = new EWalletETEResponse();
        eteResponse.setSender(new Sender());
        eteResponse.setAmount(new BigDecimal("100.00"));
        eteResponse.setFee(new BigDecimal("0.00"));
        when(paymentService.validateEWalletPayment(eq(correlationId), eq(crmId), any(EWalletETERequest.class))).thenReturn(eteResponse);

    }

    private void mockStaticTransactionGenerateId() {
        when(Transaction.getTransactionId(anyString(), anyInt())).thenReturn("transaction-after-generate");
    }

    private void mockValidateIsRequireCommonAuthForTopUpReturnRequireCommonAuth() throws TMBCommonException {
        when(dailyLimitPinFreeValidator.validateIsRequireCommonAuthForTopUp(any(HttpHeaders.class), any(BigDecimal.class), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(new CommonAuthenResult().setRequireCommonAuthen(true));
    }

    private void mockFetchCustomerCrmProfile() {
        TmbServiceResponse<CustomerCrmProfile> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new CustomerCrmProfile()
                .setPaymentAccuUsgAmt(BigDecimal.valueOf(100.50))
                .setBillpayAccuUsgAmt(BigDecimal.valueOf(2000.50))
                .setEbAccuUsgAmtDaily(2000.50)
        );
        when(customerServiceClient.fetchCustomerCrmProfile(correlationId, crmId)).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private void mockGetAccountByAccountNumber() throws TMBCommonException {
        DepositAccount depositAccount = new DepositAccount()
                .setAccountNumber(depositAccountNumber)
                .setAccountType("SDA")
                .setAvailableBalance(BigDecimal.valueOf(999999.99));
        when(billPayAccountCommonPaymentService.getAccountByAccountNumber(depositAccountNumber, headers)).thenReturn(depositAccount);
    }

    private void mockValidateDailyLimitExceededDoNothing() throws TMBCommonException {
        doNothing().when(dailyLimitService).validateDailyLimitExceeded(anyString(), any(CustomerCrmProfile.class), any(BigDecimal.class));
    }

    private void mockValidateDailyLimitExceededThrowTMBCommonException() throws TMBCommonException {
        doThrow(TMBCommonException.class).when(dailyLimitService).validateDailyLimitExceeded(anyString(), any(CustomerCrmProfile.class), any(BigDecimal.class));
    }

    private CommonPaymentDraftCache initialCacheDataForBillPayTransaction() {
        PaymentInformation p = new PaymentInformation()
                .setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY)
                .setTransactionType("bill_pay")
                .setCompCode(compCode)
                .setFundCode("ignore")
                .setRequireAddressFlag(true)
                .setProductDetail(new ProductDetail()
                        .setProductNameEn("Product-name-en-value")
                        .setProductNameTh("Product-name-th-value")
                        .setProductAttributeList(List.of(
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("1000000006")
                                        .setValueTh("1000000006"),
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("0987834783")
                                        .setValueTh("0987834783")
                        ))
                        .setProductRef1("reference1")
                        .setProductRef2("reference2")
                )
                .setAmountDetail(new AmountDetail()
                        .setAmountLabelEn("Amount-lable-en-value")
                        .setAmountLabelTh("Amount-lable-th-value")
                        .setAmountUnitEn("Amount-unit-en-value")
                        .setAmountUnitTh("Amount-unit-th-value")
                        .setAmountValue(BigDecimal.valueOf(9855.00))
                )
                .setCompleteScreenDetail(new CompleteScreenDetail()
                        .setRemarkEn("Remark-en-value")
                        .setRemarkTh("Remark-th-value")
                        .setFooterEn("Footer-en-value")
                        .setFooterTh("Footer-th-value")
                        .setBackBtnKeyEn("Back-btn-key-en-value")
                        .setBackBtnKeyTh("Back-btn-key-th-value")
                        .setBackBtnUrl("www.google.co.th")
                );

        return new CommonPaymentDraftCache()
                .setProcessorType(BILLER_PAYMENT_TOPUP_E_WALLET)
                .setPaymentInformation(p)
                .setCrmId(crmId)
                .setCommonPaymentConfig(commonPaymentConfig);
    }

    private ValidationCommonPaymentRequest initialDefaultRequest() {
        request = new ValidationCommonPaymentRequest();
        request.setDeposit(new DepositValidationCommonPaymentRequest().setPayWithDepositFlag(true).setAccountNumber(depositAccountNumber).setAmount(BigDecimal.valueOf(9500.50)));
        request.setCreditCard(new CreditCardValidationCommonPaymentRequest().setPayWithCreditCardFlag(false));
        request.setFlow("flow");
        request.setTransactionId("transaction_id");
        return request;
    }

    private void mockFetchMasterBillerByCompCode(MasterBillerResponse masterBiller) throws TMBCommonException {
        BillerInfoResponse billerInfoResponse = masterBiller.getBillerInfo();
        billerInfoResponse.setStartTime(null);
        billerInfoResponse.setEndTime(null);
        billerInfoResponse.setExpiredDate("9999-12-31T00:00:00.000000+07:00");
        billerInfoResponse.setBillerCompCode(compCode);
        billerInfoResponse.setNameEn("PromptPay e-Wallet");
        billerInfoResponse.setBillerGroupType("1");
        billerInfoResponse.setBillerCategoryCode("not sure");

        masterBiller.setRef1(new ReferenceResponse().setIsMobile(false));

        when(paymentService.getMasterBiller(correlationId, compCode)).thenReturn(masterBiller);
    }

    private void setUpAsyncHelperExecuteRequestAsync() throws TMBCommonException {
        when(asyncHelper.executeRequestAsync(any())).thenAnswer(invocation -> {
            Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
            ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
            return CompletableFuture.completedFuture(Objects.requireNonNull(feignResponseEntity.getBody()).getData());
        });
    }

    private void setUpAsyncHelperExecuteMethodAsync() throws TMBCommonException {
        when(asyncHelper.executeMethodAsync(any())).thenAnswer(invocation -> {
            ThrowableSupplier<?> supplier = invocation.getArgument(0);
            return CompletableFuture.completedFuture(supplier.get());
        });
    }
}