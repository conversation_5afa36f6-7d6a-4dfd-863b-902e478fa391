package com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

import java.util.ArrayList;
import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_D_STATEMENT;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DStatementAccountCommonPaymentServiceTest {

    @InjectMocks
    DStatementAccountCommonPaymentService dStatementAccountCommonPaymentService;

    @Mock
    AccountCommonPaymentHelper accountCommonPaymentHelper;

    String crmId;
    String correlationId;
    HttpHeaders headers;
    List<DepositAccount> depositAccountList;

    @BeforeEach
    void setUp() {
        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        depositAccountList = new ArrayList<>();
    }

    @Test
    void testGetKey_ShouldReturnCorrectKey() {
        String actual = dStatementAccountCommonPaymentService.getKey();

        Assertions.assertEquals(COMMON_PAYMENT_TRANSACTION_TYPE_D_STATEMENT, actual);
    }

    @Test
    void testGetAccountList_ShouldReturnDepositAccountListSuccess() throws TMBCommonException {
        when(accountCommonPaymentHelper.fetchDepositAccountList(correlationId, crmId)).thenReturn(new ArrayList<>());
        when(accountCommonPaymentHelper.setAvailableBalance(anyList(), eq(correlationId), eq(crmId))).thenReturn(depositAccountList);

        List<DepositAccount> actual = dStatementAccountCommonPaymentService.getAccountList(correlationId, crmId);

        assertObjectAreEqual(depositAccountList, actual);
    }

    private static <T> void assertObjectAreEqual(T actual, T expect) {
        org.assertj.core.api.Assertions.assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expect);
    }
}