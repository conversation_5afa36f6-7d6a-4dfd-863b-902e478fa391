package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.DailyUsageLimit;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.BillPayDailyLimitStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class BillPayDailyLimitStrategyTest {

    private BillPayDailyLimitStrategy billPayDailyLimitStrategy;
    private CustomerCrmProfile customerProfile;

    @BeforeEach
    void setUp() {
        billPayDailyLimitStrategy = new BillPayDailyLimitStrategy();
        customerProfile = new CustomerCrmProfile();
    }

    @Test
    void testCalculateLimitWhenProfileHasDataThenReturnCorrectLimit() {
        customerProfile.setBillpayAccuUsgAmt(BigDecimal.valueOf(1000.50));
        customerProfile.setBillpayMaxLimitAmt(5000);

        DailyUsageLimit result = billPayDailyLimitStrategy.calculateLimit(customerProfile);

        assertNotNull(result);
        assertEquals(BigDecimal.valueOf(1000.50), result.getCurrentUsage());
        assertEquals(BigDecimal.valueOf(5000), result.getMaxLimit());
    }

    @Test
    void testCalculateLimitWhenProfileAccumulatedUsageNullThenReturnZeroUsage() {
        customerProfile.setBillpayAccuUsgAmt(null);
        customerProfile.setBillpayMaxLimitAmt(5000);

        DailyUsageLimit result = billPayDailyLimitStrategy.calculateLimit(customerProfile);

        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result.getCurrentUsage());
        assertEquals(BigDecimal.valueOf(5000), result.getMaxLimit());
    }

    @Test
    void testCalculateLimitWhenProfileAllFieldsZeroThenReturnZeroValues() {
        customerProfile.setBillpayAccuUsgAmt(BigDecimal.ZERO);
        customerProfile.setBillpayMaxLimitAmt(0);

        DailyUsageLimit result = billPayDailyLimitStrategy.calculateLimit(customerProfile);

        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result.getCurrentUsage());
        assertEquals(BigDecimal.ZERO, result.getMaxLimit());
    }
}