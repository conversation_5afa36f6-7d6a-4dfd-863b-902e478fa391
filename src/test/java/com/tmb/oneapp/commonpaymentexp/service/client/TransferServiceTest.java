package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.TransferServiceClient;
import com.tmb.oneapp.commonpaymentexp.model.TransferConfiguration;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.util.function.Supplier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TransferServiceTest {
    @InjectMocks
    private TransferService transferService;

    @Mock
    private TransferServiceClient transferServiceClient;
    @Mock
    private FeignClientHelper feignClientHelper;

    @BeforeEach
    void setUp() {
    }

    @Test
    void testFetchTransferModuleConfigShouldSuccess() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();

        TmbServiceResponse<TransferConfiguration> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());
        tmbServiceResponse.setData(new TransferConfiguration());
        when(transferServiceClient.getTransferModuleConfig(anyString())).thenReturn(ResponseEntity.ok(tmbServiceResponse));

        Assertions.assertDoesNotThrow(() -> transferService.fetchTransferModuleConfig(""));
    }

    private void setUpFeignClientHelperExecuteRequest() throws TMBCommonException {
        when(feignClientHelper.executeRequest(any())).thenAnswer(invocation -> {
            try {
                Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
                ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
                return feignResponseEntity.getBody().getData();
            } catch (Exception e) {
                throw new TMBCommonException("");
            }
        });
    }
}