package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.AccumulateUsageRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DailyLimitServiceTest {

    @InjectMocks
    private DailyLimitService dailyLimitService;

    @Mock
    private CustomerService customerService;
    @Mock
    private CacheMapper cacheMapper;

    private CommonPaymentDraftCache draftCache;
    private CustomerCrmProfile profile;
    private ValidationCommonPaymentRequest validationRequest;
    private ValidationCommonPaymentDraftCache validateDraftCache;
    private MasterBillerResponse masterBillerResponse;
    private BillerInfoResponse billerInfo;
    private OCPBillRequest ocpBillRequest;

    @BeforeEach
    void setUp() {
        draftCache = new CommonPaymentDraftCache();
        profile = new CustomerCrmProfile();
        validationRequest = new ValidationCommonPaymentRequest();
        validateDraftCache = new ValidationCommonPaymentDraftCache();
        masterBillerResponse = new MasterBillerResponse();
        billerInfo = new BillerInfoResponse();
        ocpBillRequest = new OCPBillRequest();
        ocpBillRequest.setAmount("1000");


        billerInfo.setBillerGroupType("0");
        masterBillerResponse.setBillerInfo(billerInfo);
        
        // Create a proper MasterBillerResponseInCache with billerInfo
        MasterBillerResponseInCache cachedResponse = new MasterBillerResponseInCache();
        cachedResponse.setBillerInfo(new com.tmb.oneapp.commonpaymentexp.model.cache.BillerInfoResponseInCache());
        cachedResponse.getBillerInfo().setBillerGroupType("0");
        
        when(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse)).thenReturn(cachedResponse);
        validateDraftCache.setMasterBillerResponse(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse));
        validateDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setOcpBillPaymentConfirmRequest(ocpBillRequest));
        draftCache.setValidateDraftCache(validateDraftCache);

        CreditCardValidationCommonPaymentRequest creditCard = new CreditCardValidationCommonPaymentRequest();
        creditCard.setPayWithCreditCardFlag(false);
        validationRequest.setCreditCard(creditCard);
        draftCache.setValidateRequest(validationRequest);

        ReflectionTestUtils.setField(dailyLimitService, "frPaymentAccumulateAmountLimit", 200000);
        dailyLimitService.init();
    }

    @Test
    void testValidateDailyLimitExceededWhenAmountUnderLimitThenSuccess() {
        profile.setBillpayAccuUsgAmt(BigDecimal.valueOf(1000));
        profile.setBillpayMaxLimitAmt(5000);

        assertDoesNotThrow(() -> dailyLimitService.validateDailyLimitExceeded(masterBillerResponse.getBillerInfo().getBillerGroupType(), profile, new BigDecimal(ocpBillRequest.getAmount())));
    }

    @Test
    void testValidateDailyLimitExceededWhenBillerGroupTypeInvalidThenThrowsException() {
        profile.setBillpayAccuUsgAmt(BigDecimal.valueOf(1000));
        profile.setBillpayMaxLimitAmt(5000);

        billerInfo.setBillerGroupType("invalid_type");

        // Expect TMBCommonException to be thrown (NumberFormatException is wrapped)
        assertThrows(TMBCommonException.class,
                () -> dailyLimitService.validateDailyLimitExceeded(masterBillerResponse.getBillerInfo().getBillerGroupType(), profile, new BigDecimal(ocpBillRequest.getAmount())));
    }

    @Test
    void testValidateDailyLimitExceededWhenBillerGroupTypeNullThenThrowsException() {
        profile.setBillpayAccuUsgAmt(BigDecimal.valueOf(1000));
        profile.setBillpayMaxLimitAmt(5000);

        billerInfo.setBillerGroupType(null);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> dailyLimitService.validateDailyLimitExceeded(masterBillerResponse.getBillerInfo().getBillerGroupType(), profile, new BigDecimal(ocpBillRequest.getAmount())));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidateDailyLimitExceededWhenMasterBillerResponseNullThenThrowsException() {
        profile.setBillpayAccuUsgAmt(BigDecimal.valueOf(1000));
        profile.setBillpayMaxLimitAmt(5000);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> dailyLimitService.validateDailyLimitExceeded(null, profile, new BigDecimal(ocpBillRequest.getAmount())));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidateDailyLimitExceededWhenDraftCacheNullThenThrowsException() {
        profile.setBillpayAccuUsgAmt(BigDecimal.valueOf(1000));
        profile.setBillpayMaxLimitAmt(5000);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> dailyLimitService.validateDailyLimitExceeded(null, profile, null));

        assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidateDailyLimitExceededWhenAmountOverLimitThenThrowsException() {
        profile.setBillpayAccuUsgAmt(BigDecimal.valueOf(4500));
        profile.setBillpayMaxLimitAmt(5000);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> dailyLimitService.validateDailyLimitExceeded(masterBillerResponse.getBillerInfo().getBillerGroupType(), profile, new BigDecimal(ocpBillRequest.getAmount())));
        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), exception.getErrorCode());
    }

    @Test
    void testValidateDailyLimitExceededWhenTopUpAmountUnderLimitThenSuccess() {
        billerInfo.setBillerGroupType("1");
        profile.setEbAccuUsgAmtDaily(1000.0);
        profile.setEbMaxLimitAmtCurrent(5000);

        assertDoesNotThrow(() -> dailyLimitService.validateDailyLimitExceeded(masterBillerResponse.getBillerInfo().getBillerGroupType(), profile, new BigDecimal(ocpBillRequest.getAmount())));
    }

    @Test
    void testValidateDailyLimitExceededWhenTopUpAmountOverLimitThenThrowsException() {
        billerInfo.setBillerGroupType("1");
        profile.setEbAccuUsgAmtDaily(4500.0);
        profile.setEbMaxLimitAmtCurrent(5000);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> dailyLimitService.validateDailyLimitExceeded(masterBillerResponse.getBillerInfo().getBillerGroupType(), profile, new BigDecimal(ocpBillRequest.getAmount())));
        assertEquals(ResponseCode.DAILY_LIMIT_EXCEEDED.getCode(), exception.getErrorCode());
    }

    @Test
    void testUpdateAccumulateUsageWhenValidRequestThenUpdateSuccess() throws TMBCommonException {
        profile.setBillpayAccuUsgAmt(BigDecimal.valueOf(1000));
        String crmId = "testCrmId";
        String correlationId = "testCorrelationId";
        
        // Mock the customer service to avoid actual calls
        when(customerService.updateUsageAccumulation(anyString(), anyString(), any(AccumulateUsageRequest.class)))
                .thenReturn(null); // Assuming void return or success

        dailyLimitService.updateAccumulateUsage(draftCache, profile, crmId, correlationId);

        verify(customerService, times(1)).updateUsageAccumulation(
                eq(correlationId),
                eq(crmId),
                any(AccumulateUsageRequest.class)
        );
    }

    @Test
    void testUpdateAccumulateUsageWhenPayWithCreditCardThenSkipUpdate() throws TMBCommonException {
        draftCache.getValidateRequest().getCreditCard().setPayWithCreditCardFlag(true);
        String crmId = "testCrmId";
        String correlationId = "testCorrelationId";

        dailyLimitService.updateAccumulateUsage(draftCache, profile, crmId, correlationId);

        verify(customerService, never()).updateUsageAccumulation(any(), any(), any());
    }

    @Test
    void testUpdateAccumulateUsageWhenCustomerServiceFailsThenLogError() throws TMBCommonException {
        String crmId = "testCrmId";
        String correlationId = "testCorrelationId";
        profile.setBillpayAccuUsgAmt(BigDecimal.valueOf(1000));

        doThrow(new RuntimeException("Service unavailable"))
                .when(customerService)
                .updateUsageAccumulation(anyString(), anyString(), any(AccumulateUsageRequest.class));

        dailyLimitService.updateAccumulateUsage(draftCache, profile, crmId, correlationId);

        verify(customerService, times(1)).updateUsageAccumulation(
                eq(correlationId),
                eq(crmId),
                any(AccumulateUsageRequest.class)
        );
    }

    @Test
    void testUpdateAccumulateUsageWhenRequestDataNullThenLogError() throws TMBCommonException {
        String crmId = "testCrmId";
        String correlationId = "testCorrelationId";
        profile.setBillpayAccuUsgAmt(null);
        
        // Mock the customer service to avoid actual calls
        when(customerService.updateUsageAccumulation(anyString(), anyString(), any(AccumulateUsageRequest.class)))
                .thenReturn(null); // Assuming void return or success

        dailyLimitService.updateAccumulateUsage(draftCache, profile, crmId, correlationId);

        verify(customerService, times(1)).updateUsageAccumulation(
                eq(correlationId),
                eq(crmId),
                any(AccumulateUsageRequest.class)
        );
    }
}