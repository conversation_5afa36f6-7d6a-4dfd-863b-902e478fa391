package com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.util.ArrayList;
import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BillPayAccountCommonPaymentServiceTest {
    @InjectMocks
    BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;

    @Mock
    AccountCommonPaymentHelper accountCommonPaymentHelper;

    String crmId;
    String correlationId;
    HttpHeaders headers;
    List<DepositAccount> depositAccountList;

    @BeforeEach
    void setUp() {
        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        depositAccountList = new ArrayList<>();
    }

    @Test
    void testGetKey_ShouldReturnCorrectKey() {
        String actual = billPayAccountCommonPaymentService.getKey();

        Assertions.assertEquals(COMMON_PAYMENT_TRANSACTION_TYPE_BILL_PAY, actual);
    }

    @Test
    void testGetAccountList_ShouldReturnDepositAccountListSuccess() throws TMBCommonException {
        when(accountCommonPaymentHelper.fetchDepositAccountList(correlationId, crmId)).thenReturn(new ArrayList<>());
        when(accountCommonPaymentHelper.setAvailableBalance(anyList(), eq(correlationId), eq(crmId))).thenReturn(depositAccountList);

        List<DepositAccount> actual = billPayAccountCommonPaymentService.getAccountList(correlationId, crmId);

        assertObjectAreEqual(depositAccountList, actual);
    }

    @Test
    void testGetAccountByAccountNumber_ShouldReturnTheAccount() throws TMBCommonException {
        String accountNumber = "**********";
        DepositAccount depositResponse = new DepositAccount()
                .setRelationshipCode("PRIIND")
                .setAccountStatus("ACTIVE")
                .setAllowFromForBillPayTopUpEpayment("1")
                .setAccountNumber(accountNumber);
        when(accountCommonPaymentHelper.getDepositAccountWithBalance(accountNumber, headers)).thenReturn(depositResponse);

        DepositAccount actual = billPayAccountCommonPaymentService.getAccountByAccountNumber(accountNumber, headers);

        Assertions.assertEquals(accountNumber, actual.getAccountNumber());
    }

    @Test
    void testGetAccountByAccountNumber_WhenAccountIsCloseStatus_ShouldThrowTMBCommonException() throws TMBCommonException {
        String accountNumber = "**********";
        DepositAccount closeDepositResponse = new DepositAccount()
                .setRelationshipCode("PRIIND")
                .setAccountStatus("ACTIVE")
                .setAllowFromForBillPayTopUpEpayment("1")
                .setAccountStatus("CLOSE")
                .setAccountNumber(accountNumber);
        when(accountCommonPaymentHelper.getDepositAccountWithBalance(any(), any(HttpHeaders.class))).thenReturn(closeDepositResponse);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> billPayAccountCommonPaymentService.getAccountByAccountNumber("accountNumberNotMatched", headers));

        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetAccountByAccountNumber_WhenAccountIsDormantStatus_ShouldThrowTMBCommonException() throws TMBCommonException {
        String accountNumber = "**********";
        DepositAccount closeDepositResponse = new DepositAccount()
                .setRelationshipCode("PRIIND")
                .setAllowFromForBillPayTopUpEpayment("1")
                .setAccountStatus("DORMANT")
                .setAccountNumber(accountNumber);
        when(accountCommonPaymentHelper.getDepositAccountWithBalance(any(), any(HttpHeaders.class))).thenReturn(closeDepositResponse);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> billPayAccountCommonPaymentService.getAccountByAccountNumber("accountNumberNotMatched", headers));

        Assertions.assertEquals(ResponseCode.DEPOSIT_DORMANT_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    private static <T> void assertObjectAreEqual(T actual, T expect) {
        org.assertj.core.api.Assertions.assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expect);
    }
}