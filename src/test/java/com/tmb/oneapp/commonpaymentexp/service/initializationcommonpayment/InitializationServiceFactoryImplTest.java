package com.tmb.oneapp.commonpaymentexp.service.initializationcommonpayment;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentMapper;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class InitializationServiceFactoryImplTest {
    private InitializationServiceFactoryImpl factory;

    @Mock
    private CacheService cacheService;

    @Mock
    private PaymentService paymentService;
    
    @Mock
    private CommonPaymentMapper commonPaymentMapper;

    private static final String TEST_DEEPLINK_PREFIX = "https://test.deeplink.url";
    private static final int TEST_CACHE_EXPIRE_SECOND = 300;

    @BeforeEach
    void setUp() {
        factory = new InitializationServiceFactoryImpl(cacheService, paymentService, commonPaymentMapper);
        ReflectionTestUtils.setField(factory, "deeplinkPrefix", TEST_DEEPLINK_PREFIX);
        ReflectionTestUtils.setField(factory, "cacheExpireSecond", TEST_CACHE_EXPIRE_SECOND);
    }

    @Test
    void testCreate_WhenSuccessCreateWithDefaultInitializationValidator_ShouldReturnInitializationCommonPaymentService() {
        InitializationCommonPaymentService actual = factory.create(new DefaultInitializationValidatorImpl());

        Assertions.assertNotNull(actual);
    }

    @Test
    void testCreate_WhenSuccessCreateWithPartnerInitializationValidator_ShouldReturnInitializationCommonPaymentService() {
        InitializationCommonPaymentService actual = factory.create(new PartnerInitializationValidatorImpl());

        Assertions.assertNotNull(actual);
    }
}