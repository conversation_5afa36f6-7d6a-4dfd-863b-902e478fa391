package com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AccountCommonPaymentProcessorSelectorTest {
    AccountCommonPaymentProcessorSelector selector;

    @Mock
    AccountCommonPaymentProcessor<List<DepositAccount>> mockProcessor1;

    @Mock
    AccountCommonPaymentProcessor<List<DepositAccount>> mockProcessor2;

    String filterAccountType;

    @BeforeEach
    void setUp() {
        filterAccountType = "bill_pay";
        when(mockProcessor1.getKey()).thenReturn(filterAccountType);
        when(mockProcessor2.getKey()).thenReturn("dstatement");

        selector = new AccountCommonPaymentProcessorSelector(List.of(mockProcessor1, mockProcessor2));
    }

    @Test
    void testGetProcessor_ShouldSuccess() {
        AccountCommonPaymentProcessor<List<DepositAccount>> result = selector.getProcessor(filterAccountType);

        assertNotNull(result);
        assertEquals(mockProcessor1, result);
    }

    @Test
    void testValidateFilterAccountTypeKey_WhenKeyMatched_ShouldDoesNotThrowException() {
        Assertions.assertDoesNotThrow(() -> selector.validateFilterAccountTypeKey(filterAccountType));
    }

    @Test
    void testValidateFilterAccountTypeKey_WhenKeyNotMatched_ShouldThrowTMBCommonException() {
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> selector.validateFilterAccountTypeKey("incorrect-filter-account-type"));

        Assertions.assertEquals(ResponseCode.MISSING_REQUIRED_FIELD_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.BAD_REQUEST, exception.getStatus());
    }
}