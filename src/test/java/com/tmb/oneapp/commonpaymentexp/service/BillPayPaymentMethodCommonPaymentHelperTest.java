package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentRule;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.paymetmethod.ProcessBillPayDataTemp;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.BillPayAccountCommonPaymentService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.util.ArrayList;
import java.util.List;

import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.DEFAULT_PAYMENT_CREDIT_CARD;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.DEFAULT_PAYMENT_DEPOSIT;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class BillPayPaymentMethodCommonPaymentHelperTest {
    @InjectMocks
    BillPayPaymentMethodCommonPaymentHelper billPayPaymentMethodCommonPaymentHelper;

    @Mock
    BillPayAccountCommonPaymentService billPayAccountCommonPaymentService;

    @Mock
    AccountCreditCardService accountCreditCardService;

    String crmId;
    String correlationId;
    HttpHeaders headers;
    CommonPaymentRule commonPaymentRule;
    List<DepositAccount> depositAccountListResponse;
    List<CreditCardSupplementary> creditCardAccountListResponse;
    MasterBillerResponse masterBillerResponse;
    String defaultPayment;

    @BeforeEach
    void setUp() {
        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        commonPaymentRule = new CommonPaymentRule();
        masterBillerResponse = new MasterBillerResponse().setBillerInfo(new BillerInfoResponse());
        depositAccountListResponse = new ArrayList<>();
        creditCardAccountListResponse = new ArrayList<>();
    }

    // ========= START - DEFAULT PAYMENT DEPOSIT =========
    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultDepositPayment_ShouldReturnDepositAccountSuccess() throws TMBCommonException {
        defaultPayment = DEFAULT_PAYMENT_DEPOSIT;
        boolean creditCardFlagFromMasterBiller = true;
        commonPaymentRule.setDefaultPayment(defaultPayment);
        masterBillerResponse.getBillerInfo().setCreditCardFlag(creditCardFlagFromMasterBiller);
        mockGetBillPayAccountReturnDepositAccountSuccess(depositAccountListResponse);

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        assertObjectAreEqual(depositAccountListResponse, actual.getDepositAccountList());
        Assertions.assertNull(actual.getCreditCardList());
        assertShouldNotGetAccountCreditCard();
    }

    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultDepositPaymentGetAccountBillPayExceptionButGotCreditCardAccount_ShouldReturnCreditCardAccountSuccess() throws TMBCommonException {
        defaultPayment = DEFAULT_PAYMENT_DEPOSIT;
        boolean creditCardFlagFromMasterBiller = true;
        commonPaymentRule.setDefaultPayment(defaultPayment);
        masterBillerResponse.getBillerInfo().setCreditCardFlag(creditCardFlagFromMasterBiller);
        mockGetBillPayAccountThrowTMBCommonException();

        mockGetCreditCardAccountReturnCreditCardAccountSuccess(creditCardAccountListResponse);

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        assertObjectAreEqual(creditCardAccountListResponse, actual.getCreditCardList());
        Assertions.assertNull(actual.getDepositAccountList());
    }

    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultDepositPaymentButGetAccountBillPayFeignException_ShouldDoesNotThrow() throws TMBCommonException {
        defaultPayment = DEFAULT_PAYMENT_DEPOSIT;
        boolean creditCardFlagFromMasterBiller = false;
        commonPaymentRule.setDefaultPayment(defaultPayment);
        masterBillerResponse.getBillerInfo().setCreditCardFlag(creditCardFlagFromMasterBiller);
        mockGetBillPayAccountThrowTMBCommonException();

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        Assertions.assertNull(actual.getDepositAccountList());
        assertShouldNotGetAccountCreditCard();
    }

    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultDepositPaymentButGetAccountBillPayNoEligibleAccountException_ShouldDoesNotThrow() throws TMBCommonException {
        defaultPayment = DEFAULT_PAYMENT_DEPOSIT;
        boolean creditCardFlagFromMasterBiller = false;
        commonPaymentRule.setDefaultPayment(defaultPayment);
        masterBillerResponse.getBillerInfo().setCreditCardFlag(creditCardFlagFromMasterBiller);
        mockGetBillPayAccountThrowNoEligibleException();

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        Assertions.assertNull(actual.getDepositAccountList());
        assertShouldNotGetAccountCreditCard();
    }
    // ========= END - DEFAULT PAYMENT DEPOSIT =========

    // ========= START - DEFAULT PAYMENT CREDIT-CARD =========
    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultCreditCardPayment_ShouldReturnCreditCardAccountSuccess() throws TMBCommonException {
        defaultPayment = DEFAULT_PAYMENT_CREDIT_CARD;
        boolean creditCardFlagFromMasterBiller = true;
        commonPaymentRule.setDefaultPayment(defaultPayment);
        masterBillerResponse.getBillerInfo().setCreditCardFlag(creditCardFlagFromMasterBiller);
        mockGetCreditCardAccountReturnCreditCardAccountSuccess(creditCardAccountListResponse);

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        assertObjectAreEqual(creditCardAccountListResponse, actual.getCreditCardList());
        Assertions.assertNull(actual.getDepositAccountList());
        assertShouldNotGetAccountBillPay();
    }

    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultCreditCardPaymentButGetCreditCardAccountExceptionButGotDepositAccount_ShouldReturnDepositAccountSuccess() throws TMBCommonException {
        defaultPayment = DEFAULT_PAYMENT_CREDIT_CARD;
        boolean creditCardFlagFromMasterBiller = true;
        commonPaymentRule.setDefaultPayment(defaultPayment);
        masterBillerResponse.getBillerInfo().setCreditCardFlag(creditCardFlagFromMasterBiller);
        mockGetCreditCardAccountThrowTMBCommonException();
        mockGetBillPayAccountReturnDepositAccountSuccess(depositAccountListResponse);

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        assertObjectAreEqual(depositAccountListResponse, actual.getDepositAccountList());
        Assertions.assertNull(actual.getCreditCardList());
    }

    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultCreditCardPaymentButIsCreditCardFlagTurnOff_ShouldReturnDepositAccountSuccess() throws TMBCommonException {
        defaultPayment = DEFAULT_PAYMENT_CREDIT_CARD;
        boolean creditCardFlagFromMasterBiller = false;
        commonPaymentRule.setDefaultPayment(defaultPayment);
        masterBillerResponse.getBillerInfo().setCreditCardFlag(creditCardFlagFromMasterBiller);
        mockGetBillPayAccountReturnDepositAccountSuccess(depositAccountListResponse);

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        assertObjectAreEqual(depositAccountListResponse, actual.getDepositAccountList());
        Assertions.assertNull(actual.getCreditCardList());
        assertShouldNotGetAccountCreditCard();
    }

    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultCreditCardPaymentButIsCreditCardFlagTurnOffAndGetDepositAccountFeignException_ShouldDoesNotThrow() throws TMBCommonException {
        defaultPayment = DEFAULT_PAYMENT_CREDIT_CARD;
        boolean creditCardFlagFromMasterBiller = false;
        commonPaymentRule.setDefaultPayment(defaultPayment);
        masterBillerResponse.getBillerInfo().setCreditCardFlag(creditCardFlagFromMasterBiller);
        mockGetBillPayAccountThrowTMBCommonException();

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        Assertions.assertNull(actual.getDepositAccountList());
        Assertions.assertNull(actual.getCreditCardList());
    }

    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultCreditCardPaymentButGetCreditCardAccountsFeignException_ShouldDoesNotThrow() throws TMBCommonException {
        defaultPayment = DEFAULT_PAYMENT_CREDIT_CARD;
        boolean creditCardFlagFromMasterBiller = true;
        commonPaymentRule.setDefaultPayment(defaultPayment);
        masterBillerResponse.getBillerInfo().setCreditCardFlag(creditCardFlagFromMasterBiller);
        mockGetBillPayAccountThrowTMBCommonException();
        mockGetCreditCardAccountThrowTMBCommonException();

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        Assertions.assertNull(actual.getCreditCardList());
    }

    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayWithDefaultCreditCardPaymentButGetCreditCardAccountsNoEligibleException_ShouldDoesNotThrow() throws TMBCommonException {
        defaultPayment = DEFAULT_PAYMENT_CREDIT_CARD;
        boolean creditCardFlagFromMasterBiller = true;
        commonPaymentRule.setDefaultPayment(defaultPayment);
        masterBillerResponse.getBillerInfo().setCreditCardFlag(creditCardFlagFromMasterBiller);
        mockGetBillPayAccountThrowTMBCommonException();
        mockGetCreditCardAccountThrowNoEligibleAccountError();

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        Assertions.assertNull(actual.getCreditCardList());
        Assertions.assertNull(actual.getDepositAccountList());

    }
    // ========= END - DEFAULT PAYMENT CREDIT-CARD =========

    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayButAllDefaultPaymentException_ShouldDoesNotThrow() throws TMBCommonException {
        defaultPayment = DEFAULT_PAYMENT_CREDIT_CARD;
        boolean creditCardFlagFromMasterBiller = true;
        commonPaymentRule.setDefaultPayment(defaultPayment);
        masterBillerResponse.getBillerInfo().setCreditCardFlag(creditCardFlagFromMasterBiller);
        mockGetCreditCardAccountThrowTMBCommonException();
        mockGetBillPayAccountThrowTMBCommonException();

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        Assertions.assertNull(actual.getCreditCardList());
        Assertions.assertNull(actual.getDepositAccountList());
    }

    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayButDefaultPaymentIncorrectButGotDepositAccount_ShouldReturnDepositAccountSuccess() throws TMBCommonException {
        defaultPayment = "incorrect-default-payment";
        commonPaymentRule.setDefaultPayment(defaultPayment);
        mockGetBillPayAccountReturnDepositAccountSuccess(depositAccountListResponse);

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        assertObjectAreEqual(depositAccountListResponse, actual.getDepositAccountList());
        Assertions.assertNull(actual.getCreditCardList());
        assertShouldNotGetAccountCreditCard();
    }

    @Test
    void testGetCommonPaymentMethod_WhenTransactionBillPayButDefaultPaymentIncorrect_ShouldDoesNotThrow() throws TMBCommonException {
        defaultPayment = "incorrect-default-payment";
        commonPaymentRule.setDefaultPayment(defaultPayment);
        mockGetBillPayAccountThrowTMBCommonException();

        ProcessBillPayDataTemp actual = billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers);

        Assertions.assertNull(actual.getDepositAccountList());
    }

    @Test
    void testGetCommonPaymentMethod_WhenValidateDataProcessBillPayMissingMandatoryFields_ShouldThrowTMBCommonException() {
        commonPaymentRule.setDefaultPayment(null);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> billPayPaymentMethodCommonPaymentHelper.processBillPay(commonPaymentRule, masterBillerResponse, headers));

        Assertions.assertEquals(ResponseCode.MISSING_CONFIGURATION_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
    }

    private void mockGetCreditCardAccountThrowNoEligibleAccountError() throws TMBCommonException {
        when(accountCreditCardService.getCreditCardAccounts(anyString(), anyString())).thenThrow(CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR));
    }

    private void mockGetBillPayAccountThrowTMBCommonException() throws TMBCommonException {
        when(billPayAccountCommonPaymentService.getAccountList(correlationId, crmId)).thenThrow(TMBCommonException.class);
    }

    private void mockGetBillPayAccountThrowNoEligibleException() throws TMBCommonException {
        when(billPayAccountCommonPaymentService.getAccountList(correlationId, crmId)).thenThrow(CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR));
    }

    private void mockGetCreditCardAccountReturnCreditCardAccountSuccess(List<CreditCardSupplementary> creditCardAccountListResponse) throws TMBCommonException {
        creditCardAccountListResponse.add(new CreditCardSupplementary().setSupplementaryInfos(new ArrayList<>()));
        when(accountCreditCardService.getCreditCardAccounts(correlationId, crmId)).thenReturn(this.creditCardAccountListResponse);
    }

    private void mockGetCreditCardAccountThrowTMBCommonException() throws TMBCommonException {
        when(accountCreditCardService.getCreditCardAccounts(correlationId, crmId)).thenThrow(TMBCommonException.class);
    }

    private void mockGetBillPayAccountReturnDepositAccountSuccess(List<DepositAccount> depositAccountListResponse) throws TMBCommonException {
        depositAccountListResponse.add(new DepositAccount().setAccountName("deposit-account-response-from-get-account-bill-pay"));
        when(billPayAccountCommonPaymentService.getAccountList(correlationId, crmId)).thenReturn(depositAccountListResponse);
    }

    private void assertShouldNotGetAccountBillPay() throws TMBCommonException {
        Mockito.verify(billPayAccountCommonPaymentService, never()).getAccountList(anyString(), anyString());
    }

    private void assertShouldNotGetAccountCreditCard() throws TMBCommonException {
        Mockito.verify(accountCreditCardService, never()).getCreditCardAccounts(anyString(), anyString());
    }

    private static <T> void assertObjectAreEqual(T actual, T expect) {
        org.assertj.core.api.Assertions.assertThat(actual)
                .usingRecursiveComparison()
                .isEqualTo(expect);
    }
}