package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.PartnerPaymentStatusCallback;
import com.tmb.oneapp.commonpaymentexp.service.PaymentStatusPublisherService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
class CallbackConfirmServiceTest {

    @Mock
    private PaymentStatusPublisherService paymentStatusPublisherService;

    @InjectMocks
    private CallbackConfirmService callbackConfirmService;

    @Test
    void testCallback_GivenPaymentStatusAndIsPartner_ShouldPublishToPartnerTopic() {
        PartnerPaymentStatusCallback callback = PartnerPaymentStatusCallback.builder()
                .url("https://partner.callback.url")
                .data(PartnerPaymentStatusCallback.DataPayload.builder()
                        .payload("{\"transactionId\":\"tx-partner\"}")
                        .build())
                .build();
        PaymentInformation paymentInfo = new PaymentInformation().setEntryId("entry-partner");
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                .setPartnerName("shopee")
                .setPaymentInformation(paymentInfo);

        callbackConfirmService.callback(draftCache, callback);

        verify(paymentStatusPublisherService).publishPartner(eq(callback), eq("shopee"));
    }

    @Test
    void testCallback_GivenPaymentStatusAndIsNotPartner_ShouldPublishToInternalTopic() {
        PartnerPaymentStatusCallback callback = PartnerPaymentStatusCallback.builder()
                .url("https://internal.callback.url")
                .data(PartnerPaymentStatusCallback.DataPayload.builder()
                        .payload("{\"transactionId\":\"tx-normal\"}")
                        .build())
                .build();
        PaymentInformation paymentInfo = new PaymentInformation().setEntryId("entry-normal");
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                .setPartnerName(null)
                .setPaymentInformation(paymentInfo);

        callbackConfirmService.callback(draftCache, callback);

        verify(paymentStatusPublisherService).publishInternal(eq(callback), eq("entry-normal"));
    }


    @Test
    void testCallback_GivenNullPaymentStatus_ShouldNotPublish() {
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache();

        callbackConfirmService.callback(draftCache, null);

        verify(paymentStatusPublisherService, never()).publishInternal(any(PartnerPaymentStatusCallback.class), any(String.class));
        verify(paymentStatusPublisherService, never()).publishPartner(any(PartnerPaymentStatusCallback.class), any(String.class));
    }

    @Test
    void testCallback_GivenEntryIdIsPb_ShouldBypassAndNotPublish() {
        PartnerPaymentStatusCallback callback = PartnerPaymentStatusCallback.builder()
                .url("https://pb.callback.url")
                .data(PartnerPaymentStatusCallback.DataPayload.builder()
                        .payload("{\"transactionId\":\"tx-pb\"}")
                        .build())
                .build();
        PaymentInformation paymentInfo = new PaymentInformation().setEntryId("pb");
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                .setPartnerName(null)
                .setPaymentInformation(paymentInfo);

        callbackConfirmService.callback(draftCache, callback);

        verify(paymentStatusPublisherService, never()).publishInternal(any(PartnerPaymentStatusCallback.class), any(String.class));
        verify(paymentStatusPublisherService, never()).publishPartner(any(PartnerPaymentStatusCallback.class), any(String.class));
    }

    @Test
    void testCallback_GivenEntryIdIsPbIgnoreCase_ShouldBypassAndNotPublish() {
        PartnerPaymentStatusCallback callback = PartnerPaymentStatusCallback.builder()
                .url("https://pb.callback.url")
                .data(PartnerPaymentStatusCallback.DataPayload.builder()
                        .payload("{\"transactionId\":\"tx-PB\"}")
                        .build())
                .build();
        PaymentInformation paymentInfo = new PaymentInformation().setEntryId("PB");
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                .setPartnerName(null)
                .setPaymentInformation(paymentInfo);

        callbackConfirmService.callback(draftCache, callback);

        verify(paymentStatusPublisherService, never()).publishInternal(any(PartnerPaymentStatusCallback.class), any(String.class));
        verify(paymentStatusPublisherService, never()).publishPartner(any(PartnerPaymentStatusCallback.class), any(String.class));
    }

    @Test
    void testCallback_GivenPartnerWithEmptyName_ShouldPublishToInternalTopic() {
        PartnerPaymentStatusCallback callback = PartnerPaymentStatusCallback.builder()
                .url("https://empty-partner.callback.url")
                .data(PartnerPaymentStatusCallback.DataPayload.builder()
                        .payload("{\"transactionId\":\"tx-empty-partner\"}")
                        .build())
                .build();
        PaymentInformation paymentInfo = new PaymentInformation().setEntryId("entry-empty-partner");
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                .setPartnerName("")
                .setPaymentInformation(paymentInfo);

        callbackConfirmService.callback(draftCache, callback);

        verify(paymentStatusPublisherService).publishInternal(eq(callback), eq("entry-empty-partner"));
        verify(paymentStatusPublisherService, never()).publishPartner(any(PartnerPaymentStatusCallback.class), any(String.class));
    }

    @Test
    void testCallback_GivenPartnerWithWhitespaceOnlyName_ShouldPublishToInternalTopic() {
        PartnerPaymentStatusCallback callback = PartnerPaymentStatusCallback.builder()
                .url("https://whitespace-partner.callback.url")
                .data(PartnerPaymentStatusCallback.DataPayload.builder()
                        .payload("{\"transactionId\":\"tx-whitespace-partner\"}")
                        .build())
                .build();
        PaymentInformation paymentInfo = new PaymentInformation().setEntryId("entry-whitespace-partner");
        CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                .setPartnerName("   ")
                .setPaymentInformation(paymentInfo);

        callbackConfirmService.callback(draftCache, callback);

        verify(paymentStatusPublisherService).publishInternal(eq(callback), eq("entry-whitespace-partner"));
        verify(paymentStatusPublisherService, never()).publishPartner(any(PartnerPaymentStatusCallback.class), any(String.class));
    }
}
