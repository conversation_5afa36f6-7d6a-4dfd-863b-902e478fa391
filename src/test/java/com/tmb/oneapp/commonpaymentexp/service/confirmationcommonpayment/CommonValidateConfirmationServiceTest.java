package com.tmb.oneapp.commonpaymentexp.service.confirmationcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.cache.BillerInfoResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRefRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenVerifyRule;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.BasePrepareDataConfirm;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.confirmation.ConfirmationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.CommonAuthenticationService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.ServiceHoursUtils;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;

import java.math.BigDecimal;
import java.util.Date;

import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_CACHE_PREFIX;
import static com.tmb.oneapp.commonpaymentexp.constant.CacheConstant.COMMON_PAYMENT_HASH_KEY_IS_USED;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonValidateConfirmationServiceTest {

    @Mock
    private CacheService cacheService;
    @Mock
    private DailyLimitService dailyLimitService;

    @Mock
    private CommonAuthenticationService commonAuthenticationService;

    @Spy
    @InjectMocks
    private CommonValidateConfirmationService commonValidateConfirmationService;

    @Nested
    class ValidateServiceHoursTest {
        @Test
        void testValidateServiceHoursWhenDuringServiceHoursShouldSuccess() {
            BillerInfoResponseInCache billerInfo = new BillerInfoResponseInCache();
            billerInfo.setStartTime("08:00");
            billerInfo.setEndTime("17:00");

            MasterBillerResponseInCache masterBiller = new MasterBillerResponseInCache();
            masterBiller.setBillerInfo(billerInfo);

            ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
            validateDraftCache.setMasterBillerResponse(masterBiller);

            CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
            cache.setValidateDraftCache(validateDraftCache);

            try (MockedStatic<ServiceHoursUtils> mocked = mockStatic(ServiceHoursUtils.class)) {
                mocked.when(() -> ServiceHoursUtils.isDuringServiceHours(any(Date.class), any()))
                        .thenReturn(true);

                assertDoesNotThrow(() -> commonValidateConfirmationService.validateServiceHours(cache));
            }
        }

        @Test
        void testValidateServiceHoursWhenOutsideServiceHoursShouldThrowException() {
            BillerInfoResponseInCache billerInfo = new BillerInfoResponseInCache();
            billerInfo.setStartTime("08:00");
            billerInfo.setEndTime("17:00");

            MasterBillerResponseInCache masterBiller = new MasterBillerResponseInCache();
            masterBiller.setBillerInfo(billerInfo);

            ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
            validateDraftCache.setMasterBillerResponse(masterBiller);

            CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
            cache.setValidateDraftCache(validateDraftCache);

            TMBCommonException expected = CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.UNAVAILABLE_SERVICE_HOUR);

            try (MockedStatic<ServiceHoursUtils> mocked = mockStatic(ServiceHoursUtils.class);
                 MockedStatic<CommonServiceUtils> mockedCommon = mockStatic(CommonServiceUtils.class)) {
                mocked.when(() -> ServiceHoursUtils.isDuringServiceHours(any(Date.class), any()))
                        .thenReturn(false);
                mockedCommon.when(() -> CommonServiceUtils.getBusinessTmbCommonException(anyString(), anyString()))
                        .thenReturn(expected);

                TMBCommonException ex = assertThrows(TMBCommonException.class, () -> commonValidateConfirmationService.validateServiceHours(cache));
                assertEquals(expected.getErrorCode(), ex.getErrorCode());
            }
        }
    }

    @Nested
    class VerifyAuthenticationTest {
        @Test
        void testVerifyAuthenticationWhenNotRequireConfirmPinShouldSuccess() {
            ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
            validateDraftCache.setRequireCommonAuthen(false);

            CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
            cache.setValidateDraftCache(validateDraftCache);

            assertDoesNotThrow(() -> commonValidateConfirmationService.verifyAuthentication("txnId", cache, mock(HttpHeaders.class)));
            verifyNoInteractions(commonAuthenticationService);
        }

        @Test
        void testVerifyAuthenticationWithHeaderWhenNotRequireConfirmPinShouldSuccess() {
            ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
            validateDraftCache.setRequireCommonAuthen(false);

            CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
            cache.setValidateDraftCache(validateDraftCache);

            HttpHeaders headers = new HttpHeaders();
            assertDoesNotThrow(() -> commonValidateConfirmationService.verifyAuthentication("txnId", cache, headers));
            verifyNoInteractions(commonAuthenticationService);
        }

        @Test
        void testVerifyAuthenticationWithHeaderWhenRequireConfirmPinAndHasOcpBillPaymentRequestShouldSuccess() throws Exception {
            ValidationCommonPaymentDraftCache validateDraftCache = new ValidationCommonPaymentDraftCache();
            validateDraftCache.setRequireCommonAuthen(true);

            CommonAuthenticationValidationCommonPaymentResponse commonAuthentication = new CommonAuthenticationValidationCommonPaymentResponse()
                    .setFlowName("FLOW")
                    .setTotalPaymentAccumulateUsage(BigDecimal.valueOf(1000L))
                    .setFeatureId("FID")
                    .setBillerCompCode("COMP");
            validateDraftCache.setCommonAuthentication(commonAuthentication);

            CommonPaymentDraftCache cache = new CommonPaymentDraftCache();
            cache.setValidateDraftCache(validateDraftCache);
            cache.setValidateRequest(new ValidationCommonPaymentRequest().setDeposit(new DepositValidationCommonPaymentRequest().setAmount(new BigDecimal("500"))));

            HttpHeaders headers = new HttpHeaders();
            headers.add(CommonPaymentExpConstant.HEADER_IP_ADDRESS, "ip");
            headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, "crmId");
            headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, "corrId");

            assertDoesNotThrow(() -> commonValidateConfirmationService.verifyAuthentication("txnId", cache, headers));

            verify(commonAuthenticationService, times(1)).verifyReferenceId(
                    eq("corrId"),
                    eq("crmId"),
                    eq("ip"),
                    any(CommonAuthenVerifyRefRequest.class),
                    any(CommonAuthenVerifyRule.class)
            );
        }
    }

    @Nested
    class ValidateTransactionTest {
        @Test
        void testValidateTransactionWhenNotDuplicateShouldSuccess() {
            when(cacheService.putIfAbsent(anyString(), anyString(), anyString())).thenReturn(true);

            assertDoesNotThrow(() -> commonValidateConfirmationService.validateTransaction("cacheKey1"));
        }

        @Test
        void testValidateTransactionWhenDuplicateShouldThrowException() {
            when(cacheService.putIfAbsent(anyString(), anyString(), anyString())).thenReturn(false);

            TMBCommonException expected = CommonServiceUtils.getUnhandledTmbCommonException(ResponseCode.TRANSACTION_DUPLICATE_ERROR);

            try (MockedStatic<CommonServiceUtils> mockedCommon = mockStatic(CommonServiceUtils.class)) {
                mockedCommon.when(() -> CommonServiceUtils.getBadRequestTMBCommonException(ResponseCode.TRANSACTION_DUPLICATE_ERROR))
                        .thenReturn(expected);

                TMBCommonException ex = assertThrows(TMBCommonException.class, () -> commonValidateConfirmationService.validateTransaction("cacheKey2"));
                assertSame(expected.getErrorCode(), ex.getErrorCode());
            }
        }
    }

    @Nested
    class validateTransactionByTransactionIdTest {
        @Test
        void testValidateTransactionByTransactionIdShouldSuccess() {
            String transactionId = "transactionId";
            when(cacheService.putIfAbsent(COMMON_PAYMENT_CACHE_PREFIX + transactionId, COMMON_PAYMENT_HASH_KEY_IS_USED, "true")).thenReturn(true);

            assertDoesNotThrow(() -> commonValidateConfirmationService.validateTransactionByTransactionId(transactionId));
        }
    }

    @Nested
    class ValidateDailyLimitExceededTest {
        @Test
        void testValidateDailyLimitExceeded_WhenNotPayWithCreditCard_ShouldCallValidateDailyLimitExceeded() throws TMBCommonException {
            boolean payWithCreditCard = false;

            CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                    .setValidateRequest(new ValidationCommonPaymentRequest()
                            .setCreditCard(new CreditCardValidationCommonPaymentRequest()
                                    .setPayWithCreditCardFlag(payWithCreditCard)
                            )
                            .setDeposit(new DepositValidationCommonPaymentRequest()
                                    .setAmount(BigDecimal.valueOf(1000))
                            )
                    ).setValidateDraftCache(new ValidationCommonPaymentDraftCache()
                            .setMasterBillerResponse(new MasterBillerResponseInCache()
                                    .setBillerInfo(new BillerInfoResponseInCache()
                                            .setBillerGroupType("1"))));

            commonValidateConfirmationService.validateDailyLimitExceeded(draftCache, new CustomerCrmProfile());

            verify(dailyLimitService, times(1)).validateDailyLimitExceeded(anyString(), any(), any());
        }

        @Test
        void testValidateDailyLimitExceeded_WhenPayWithCreditCard_ShouldNotCallValidateDailyLimitExceeded() throws TMBCommonException {
            boolean payWithCreditCard = true;
            CommonPaymentDraftCache draftCache = new CommonPaymentDraftCache()
                    .setValidateRequest(new ValidationCommonPaymentRequest()
                            .setCreditCard(new CreditCardValidationCommonPaymentRequest()
                                    .setPayWithCreditCardFlag(payWithCreditCard)));

            commonValidateConfirmationService.validateDailyLimitExceeded(draftCache, new CustomerCrmProfile());

            verify(dailyLimitService, never()).validateDailyLimitExceeded(anyString(), any(), any());
        }
    }

    @Nested
    class ValidateBaseValidateDataTest {
        @Test
        void testBaseValidateDataWhenAllValidatePassShouldDoesNotThrow() throws TMBCommonException {
            var request = new ConfirmationCommonPaymentRequest().setTransactionId("transaction-id");
            var prepareData = new BasePrepareDataConfirm().setCustomerCrmProfile(new CustomerCrmProfile()).setTransactionTime("transaction-time");
            var draftCache = new CommonPaymentDraftCache();
            doNothing().when(commonValidateConfirmationService).validateServiceHours(any(CommonPaymentDraftCache.class));
            doNothing().when(commonValidateConfirmationService).verifyAuthentication(anyString(), any(CommonPaymentDraftCache.class), any(HttpHeaders.class));
            doNothing().when(commonValidateConfirmationService).validateTransactionByTransactionId(anyString());
            doNothing().when(commonValidateConfirmationService).validateDailyLimitExceeded(any(CommonPaymentDraftCache.class), any(CustomerCrmProfile.class));

            assertDoesNotThrow(() -> commonValidateConfirmationService.baseValidateData(request, new HttpHeaders(), draftCache, prepareData));

            verify(commonValidateConfirmationService, times(1)).validateServiceHours(any(CommonPaymentDraftCache.class));
            verify(commonValidateConfirmationService, times(1)).verifyAuthentication(anyString(), any(CommonPaymentDraftCache.class), any(HttpHeaders.class));
            verify(commonValidateConfirmationService, times(1)).validateTransactionByTransactionId(anyString());
            verify(commonValidateConfirmationService, times(1)).validateDailyLimitExceeded(any(CommonPaymentDraftCache.class), any(CustomerCrmProfile.class));
        }
    }
}
