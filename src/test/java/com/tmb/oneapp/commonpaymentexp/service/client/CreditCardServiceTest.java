package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.creditcard.CreditCardDetail;
import com.tmb.oneapp.commonpaymentexp.client.CreditCardServiceClient;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.GetCardResponse;
import feign.FeignException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CreditCardServiceTest {
    @InjectMocks
    CreditCardService creditCardService;

    @Mock
    CreditCardServiceClient creditCardServiceClient;

    private String correlationId;
    private String accountId;
    private String cardId;

    @BeforeEach
    void setUp() {
        correlationId = "correlationId";
        accountId = "**********";
        cardId = "****************";
    }

    @Test
    void testGetCreditCardDetailByAccountId_WhenAccountIdMatching_ShouldSuccess() {
        when(creditCardServiceClient.getCreditCardDetailsByAccountId(accountId, correlationId))
                .thenReturn(ResponseEntity.ok(new GetCardResponse().setCreditCard(new CreditCardDetail())));

        assertDoesNotThrow(() -> creditCardService.getCreditCardDetailByAccountId(accountId, correlationId));
    }

    @Test
    void testGetCreditCardDetailByAccountId_WhenFailedFeignException_ShouldThrowTMBCommonException() {
        when(creditCardServiceClient.getCreditCardDetailsByAccountId(accountId, correlationId))
                .thenThrow(FeignException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> creditCardService.getCreditCardDetailByAccountId(accountId, correlationId));

        assertEquals(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardDetailByAccountId_WhenResponseNull_ShouldThrowException() {
        when(creditCardServiceClient.getCreditCardDetailsByAccountId(accountId, correlationId))
                .thenReturn(null);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> creditCardService.getCreditCardDetailByAccountId(accountId, correlationId));

        assertEquals(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardDetailByAccountId_WhenResponseBodyNull_ShouldThrowException() {
        when(creditCardServiceClient.getCreditCardDetailsByAccountId(accountId, correlationId))
                .thenReturn(ResponseEntity.ok(null));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> creditCardService.getCreditCardDetailByAccountId(accountId, correlationId));

        assertEquals(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardDetailByAccountId_WhenCreditCardNull_ShouldThrowException() {
        when(creditCardServiceClient.getCreditCardDetailsByAccountId(accountId, correlationId))
                .thenReturn(ResponseEntity.ok(new GetCardResponse().setCreditCard(null)));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> creditCardService.getCreditCardDetailByAccountId(accountId, correlationId));

        assertEquals(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testCardNotFoundException_ShouldReturnTMBCommonException() {
        TMBCommonException exception = creditCardService.cardNotFoundException();

        assertEquals(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardDetailByCardId_WhenCardIdMatching_ShouldSuccess() {
        CreditCardDetail expectedCreditCard = new CreditCardDetail();
        when(creditCardServiceClient.getCreditCardDetailsByCardId(cardId, correlationId))
                .thenReturn(ResponseEntity.ok(new GetCardResponse().setCreditCard(expectedCreditCard)));

        CreditCardDetail result = assertDoesNotThrow(() ->
                creditCardService.getCreditCardDetailByCardId(cardId, correlationId));
        
        assertEquals(expectedCreditCard, result);
    }

    @Test
    void testGetCreditCardDetailByCardId_WhenFailedFeignException_ShouldThrowTMBCommonException() {
        when(creditCardServiceClient.getCreditCardDetailsByCardId(cardId, correlationId))
                .thenThrow(FeignException.class);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> creditCardService.getCreditCardDetailByCardId(cardId, correlationId));

        assertEquals(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardDetailByCardId_WhenResponseNull_ShouldThrowException() {
        when(creditCardServiceClient.getCreditCardDetailsByCardId(cardId, correlationId))
                .thenReturn(null);

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> creditCardService.getCreditCardDetailByCardId(cardId, correlationId));

        assertEquals(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardDetailByCardId_WhenResponseBodyNull_ShouldThrowException() {
        when(creditCardServiceClient.getCreditCardDetailsByCardId(cardId, correlationId))
                .thenReturn(ResponseEntity.ok(null));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> creditCardService.getCreditCardDetailByCardId(cardId, correlationId));

        assertEquals(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testGetCreditCardDetailByCardId_WhenCreditCardNull_ShouldThrowException() {
        when(creditCardServiceClient.getCreditCardDetailsByCardId(cardId, correlationId))
                .thenReturn(ResponseEntity.ok(new GetCardResponse().setCreditCard(null)));

        TMBCommonException exception = assertThrows(TMBCommonException.class,
                () -> creditCardService.getCreditCardDetailByCardId(cardId, correlationId));

        assertEquals(ResponseCode.CREDIT_CARD_ACCOUNT_INVALID_ERROR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testDeleteCreditCardCache_WhenSuccess_ShouldNotThrowException() {
        String crmId = "001100000000000000000012025674";
        HttpHeaders headers = new HttpHeaders();
        headers.add("correlationId", correlationId);
        when(creditCardServiceClient.deleteCreditCardAccountSummaryResCache(Mockito.any(HttpHeaders.class), Mockito.eq(crmId)))
                .thenReturn(ResponseEntity.ok(new TmbOneServiceResponse<>()));

        assertDoesNotThrow(() -> creditCardService.deleteCreditCardCache(correlationId, crmId));
        verify(creditCardServiceClient, Mockito.times(1)).deleteCreditCardAccountSummaryResCache(Mockito.any(HttpHeaders.class), Mockito.eq(crmId));
    }

    @Test
    void testDeleteCreditCardCache_WhenFeignException_ShouldLogErrorAndNotThrowException() {
        String crmId = "001100000000000000000012025674";
        FeignException feignException = Mockito.mock(FeignException.class);
        when(creditCardServiceClient.deleteCreditCardAccountSummaryResCache(Mockito.any(HttpHeaders.class), Mockito.eq(crmId)))
                .thenThrow(feignException);

        assertDoesNotThrow(() -> creditCardService.deleteCreditCardCache(correlationId, crmId));
        verify(creditCardServiceClient, Mockito.times(1)).deleteCreditCardAccountSummaryResCache(Mockito.any(HttpHeaders.class), Mockito.eq(crmId));
    }
}