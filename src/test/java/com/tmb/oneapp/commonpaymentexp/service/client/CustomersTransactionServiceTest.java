package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.CustomersTransactionFeignClient;
import com.tmb.oneapp.commonpaymentexp.model.customertransaction.TriggerCacheRequest;
import feign.FeignException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CustomersTransactionServiceTest {

    @Mock
    private CustomersTransactionFeignClient customersTransactionFeignClient;

    @InjectMocks
    private CustomersTransactionService customersTransactionService;

    private String correlationId;
    private String crmId;

    @BeforeEach
    void setUp() {
        correlationId = "correlationId";
        crmId = "crmId";
    }

    @Test
    void clearDepositCache_Success_ShouldReturnTrue() {
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        response.setData("Success");

        when(customersTransactionFeignClient.clearCache(anyString(), any(TriggerCacheRequest.class)))
                .thenReturn(ResponseEntity.ok(response));

        boolean result = customersTransactionService.clearDepositCache(correlationId, crmId);

        assertTrue(result);
        verify(customersTransactionFeignClient).clearCache(eq(correlationId), any(TriggerCacheRequest.class));
    }

    @Test
    void clearDepositCache_WhenFeignException_ShouldReturnFalse() {
        when(customersTransactionFeignClient.clearCache(anyString(), any(TriggerCacheRequest.class)))
                .thenThrow(FeignException.InternalServerError.class);

        boolean result = customersTransactionService.clearDepositCache(correlationId, crmId);

        assertFalse(result);
        verify(customersTransactionFeignClient).clearCache(eq(correlationId), any(TriggerCacheRequest.class));
    }

    @Test
    void clearDepositCache_VerifyRequestParameters() {
        TmbOneServiceResponse<String> response = new TmbOneServiceResponse<>();
        response.setData("Success");

        when(customersTransactionFeignClient.clearCache(anyString(), any(TriggerCacheRequest.class)))
                .thenReturn(ResponseEntity.ok(response));

        customersTransactionService.clearDepositCache(correlationId, crmId);

        verify(customersTransactionFeignClient).clearCache(eq(correlationId), argThat(request -> {
            assertEquals(crmId, request.getCrmId());
            assertEquals(CustomersTransactionService.CHANNEL_NAME_PB, request.getChannelName());
            assertEquals(CustomersTransactionService.PRODUCT_GROUP_DEPOSIT, request.getProductGroup());
            return true;
        }));
    }

    @Test
    void clearDepositCache_WhenGeneralException_ShouldReturnFalse() {
        when(customersTransactionFeignClient.clearCache(anyString(), any(TriggerCacheRequest.class)))
                .thenThrow(FeignException.FeignClientException.class);

        boolean result = customersTransactionService.clearDepositCache(correlationId, crmId);

        assertFalse(result);
        verify(customersTransactionFeignClient).clearCache(eq(correlationId), any(TriggerCacheRequest.class));
    }
}