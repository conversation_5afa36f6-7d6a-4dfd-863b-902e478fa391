package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.request.notification.EmailChannel;
import com.tmb.common.model.request.notification.NotificationRecord;
import com.tmb.common.model.request.notification.NotificationRequest;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.BillerInfoResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.CacheMapper;
import com.tmb.oneapp.commonpaymentexp.model.cache.DepositAccountInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.MasterBillerResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.cache.ReferenceResponseInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ExternalConfirmRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerKYCResponse;
import com.tmb.oneapp.commonpaymentexp.model.notification.ENotificationSettingResponse;
import com.tmb.oneapp.commonpaymentexp.model.notification.NotificationOCP;
import com.tmb.oneapp.commonpaymentexp.service.client.CustomerService;
import com.tmb.oneapp.commonpaymentexp.service.client.NotificationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NotificationCommonPaymentServiceTest {

    @Mock
    private CustomerService customerService;

    @Mock
    private NotificationService notificationService;

    @InjectMocks
    private NotificationCommonPaymentService notificationCommonPaymentService;
    
    @Mock
    private CacheMapper cacheMapper;

    private NotificationOCP notificationRequest;
    private CustomerKYCResponse customerKYCResponse;
    private ENotificationSettingResponse eNotificationSettingResponse;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(notificationCommonPaymentService, "defaultChannelNameEN", "Mobile Banking");
        ReflectionTestUtils.setField(notificationCommonPaymentService, "defaultChannelNameTH", "โมบายแบงก์กิ้ง");

        notificationRequest = new NotificationOCP("template-name", "crm-id", "correlation-id");
        customerKYCResponse = new CustomerKYCResponse();
        customerKYCResponse.setCustomerFirstNameEn("John");
        customerKYCResponse.setCustomerLastNameEn("Doe");
        customerKYCResponse.setCustomerFirstNameTh("จอห์น");
        customerKYCResponse.setCustomerLastNameTh("โด");
        customerKYCResponse.setEmail("<EMAIL>");

        eNotificationSettingResponse = new ENotificationSettingResponse();
        eNotificationSettingResponse.setTransactionNotification(true);
    }

    @Test
    void testSendENotificationWhenNotificationEnabledThenSendSuccess() throws TMBCommonException {
        when(customerService.getCustomerKYC(any(), any())).thenReturn(customerKYCResponse);
        when(customerService.getENotificationSetting(any(), any())).thenReturn(eNotificationSettingResponse);

        notificationCommonPaymentService.sendENotification(notificationRequest);

        verify(notificationService, times(1)).sendMessage(eq(notificationRequest.getXCorrelationId()), any(NotificationRequest.class));
        verify(customerService, times(1)).getCustomerKYC(any(), any());
        verify(customerService, times(1)).getENotificationSetting(any(), any());
    }

    @Test
    void testSendENotificationWhenNotificationDisabledThenDoNotSend() throws TMBCommonException {
        eNotificationSettingResponse.setTransactionNotification(false);
        when(customerService.getENotificationSetting(any(), any())).thenReturn(eNotificationSettingResponse);

        notificationCommonPaymentService.sendENotification(notificationRequest);

        verify(notificationService, never()).sendMessage(any(), any());
        verify(customerService, never()).getCustomerKYC(any(), any());
    }

    @Test
    void testSendENotificationWhenSkipCheckNotificationSettingThenSendSuccess() throws TMBCommonException {
        notificationRequest = new NotificationOCP(CommonPaymentExpConstant.SCHEDULE_TRANSFER_TEMPLATE_VALUE, "crm-id", "correlation-id");
        when(customerService.getCustomerKYC(any(), any())).thenReturn(customerKYCResponse);

        notificationCommonPaymentService.sendENotification(notificationRequest);

        verify(notificationService, times(1)).sendMessage(eq(notificationRequest.getXCorrelationId()), any(NotificationRequest.class));
        verify(customerService, times(1)).getCustomerKYC(any(), any());
        verify(customerService, never()).getENotificationSetting(any(), any());
    }

    @Test
    void testSendENotificationWhenCustomerKYCFailsThenDoNotSend() throws TMBCommonException {
        when(customerService.getENotificationSetting(any(), any())).thenReturn(eNotificationSettingResponse);
        when(customerService.getCustomerKYC(any(), any())).thenThrow(new RuntimeException("Service unavailable"));

        notificationCommonPaymentService.sendENotification(notificationRequest);

        verify(notificationService, never()).sendMessage(any(), any());
    }

    @Test
    void testSendENotificationWhenNotificationServiceFailsThenHandleError() throws TMBCommonException {
        when(customerService.getCustomerKYC(any(), any())).thenReturn(customerKYCResponse);
        when(customerService.getENotificationSetting(any(), any())).thenReturn(eNotificationSettingResponse);
        doThrow(new RuntimeException("Notification service error")).when(notificationService).sendMessage(any(), any());

        assertDoesNotThrow(() -> notificationCommonPaymentService.sendENotification(notificationRequest));
    }

    @Test
    void testSendENotificationWhenValidRequestThenCorrectNotificationRequest() throws TMBCommonException {
        when(customerService.getCustomerKYC(any(), any())).thenReturn(customerKYCResponse);
        when(customerService.getENotificationSetting(any(), any())).thenReturn(eNotificationSettingResponse);
        ArgumentCaptor<NotificationRequest> notificationRequestCaptor = ArgumentCaptor.forClass(NotificationRequest.class);

        notificationCommonPaymentService.sendENotification(notificationRequest);

        verify(notificationService).sendMessage(eq(notificationRequest.getXCorrelationId()), notificationRequestCaptor.capture());
        NotificationRequest capturedRequest = notificationRequestCaptor.getValue();

        assertNotNull(capturedRequest);
        assertEquals(1, capturedRequest.getRecords().size());

        NotificationRecord record = capturedRequest.getRecords().get(0);
        assertEquals(notificationRequest.getCrmId(), record.getCrmId());
        assertEquals(CommonPaymentExpConstant.LOCALE_TH, record.getLanguage());

        EmailChannel emailChannel = record.getEmail();
        assertNotNull(emailChannel);
        assertEquals(customerKYCResponse.getEmail(), emailChannel.getEmailEndpoint());
    }

    @Test
    void testSendENotificationWhenTemplateScheduleTopupThenSkipNotificationCheck() throws TMBCommonException {
        notificationRequest = new NotificationOCP(CommonPaymentExpConstant.SCHEDULE_TOPUP_TEMPLATE_VALUE, "crm-id", "correlation-id");
        when(customerService.getCustomerKYC(any(), any())).thenReturn(customerKYCResponse);

        notificationCommonPaymentService.sendENotification(notificationRequest);

        verify(customerService, never()).getENotificationSetting(any(), any());
        verify(notificationService, times(1)).sendMessage(any(), any());
    }

    @Test
    void testSendENotificationWhenENotificationSettingNullThenDoNotSend() throws TMBCommonException {
        when(customerService.getENotificationSetting(any(), any())).thenReturn(null);

        notificationCommonPaymentService.sendENotification(notificationRequest);

        verify(notificationService, never()).sendMessage(any(), any());
        verify(customerService, never()).getCustomerKYC(any(), any());
    }

    CommonPaymentDraftCache mockCommonPaymentDraftCache() {
        ValidationCommonPaymentDraftCache validationCommonPaymentDraftCache = new ValidationCommonPaymentDraftCache();
        MasterBillerResponse masterBillerResponse = new MasterBillerResponse();
        BillerInfoResponse billerInfoResponse = new BillerInfoResponse();
        ReferenceResponse referenceResponse = new ReferenceResponse();
        DepositAccount depositAccount = new DepositAccount();
        PaymentInformation paymentInformation = new PaymentInformation();
        ProductDetail productDetail = new ProductDetail();
        productDetail.setProductRef1("ref1");
        productDetail.setProductRef2("ref2");
        OCPBillRequest ocpBillRequest = new OCPBillRequest();
        ocpBillRequest.setFee(new OCPFee());
        paymentInformation.setProductDetail(productDetail);

        masterBillerResponse.setBillerInfo(billerInfoResponse);
        masterBillerResponse.setRef1(referenceResponse);

        var masterBillerResponseInCache = MasterBillerResponseInCache.builder()
                .billerInfo(BillerInfoResponseInCache.builder()
                        .billerId(billerInfoResponse.getBillerId())
                        .nameTh(billerInfoResponse.getNameTh())
                        .nameEn(billerInfoResponse.getNameEn())
                        .billerCompCode(billerInfoResponse.getBillerCompCode())
                        .imageUrl(billerInfoResponse.getImageUrl())
                        .fee(billerInfoResponse.getFee())
                        .startTime(billerInfoResponse.getStartTime())
                        .endTime(billerInfoResponse.getEndTime())
                        .billerMethod(billerInfoResponse.getBillerMethod())
                        .billerGroupType(billerInfoResponse.getBillerGroupType())
                        .paymentMethod(billerInfoResponse.getPaymentMethod())
                        .billerCategoryCode(billerInfoResponse.getBillerCategoryCode())
                        .build())
                .ref1(new ReferenceResponseInCache()
                        .setLabelTh(referenceResponse.getLabelTh())
                        .setLabelEn(referenceResponse.getLabelEn())
                        .setIsRequirePay(referenceResponse.getIsRequirePay())
                        .setIsRequireAddFavorite(referenceResponse.getIsRequireAddFavorite())
                        .setIsMobile(referenceResponse.getIsMobile()))
                .build();
        
        var depositAccountInCache = DepositAccountInCache.builder()
                .productNickname(depositAccount.getProductNickname())
                .productNameTh(depositAccount.getProductNameTh())
                .productNameEn(depositAccount.getProductNameEn())
                .accountNumber(depositAccount.getAccountNumber())
                .accountName(depositAccount.getAccountName())
                .availableBalance(depositAccount.getAvailableBalance())
                .waiveFeeForBillpay(depositAccount.getWaiveFeeForBillpay())
                .accountType(depositAccount.getAccountType())
                .build();
                
        when(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse)).thenReturn(masterBillerResponseInCache);
        when(cacheMapper.toDepositAccountInCache(depositAccount)).thenReturn(depositAccountInCache);
        validationCommonPaymentDraftCache.setMasterBillerResponse(cacheMapper.toMasterBillerResponseInCache(masterBillerResponse));
        validationCommonPaymentDraftCache.setFromDepositAccount(cacheMapper.toDepositAccountInCache(depositAccount));
        validationCommonPaymentDraftCache.setExternalConfirmRequest(new ExternalConfirmRequest().setOcpBillPaymentConfirmRequest(ocpBillRequest));
        validationCommonPaymentDraftCache.setFeeCalculated(new BigDecimal("10.50"));

        ValidationCommonPaymentRequest validationCommonPaymentRequest = new ValidationCommonPaymentRequest();
        validationCommonPaymentRequest.setCreditCard(new CreditCardValidationCommonPaymentRequest());
        validationCommonPaymentRequest.setDeposit(new DepositValidationCommonPaymentRequest());

        CommonPaymentDraftCache commonPaymentDraftCache = new CommonPaymentDraftCache();
        commonPaymentDraftCache.setValidateDraftCache(validationCommonPaymentDraftCache);
        commonPaymentDraftCache.setValidateRequest(validationCommonPaymentRequest);
        commonPaymentDraftCache.setPaymentInformation(paymentInformation);

        return commonPaymentDraftCache;
    }
}