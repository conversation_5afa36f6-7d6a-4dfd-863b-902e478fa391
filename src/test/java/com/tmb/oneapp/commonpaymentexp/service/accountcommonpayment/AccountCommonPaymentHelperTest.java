package com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment;

import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.model.TmbOneServiceResponse;
import com.tmb.common.model.TmbServiceResponse;
import com.tmb.oneapp.commonpaymentexp.client.CustomerAccountBizClient;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.AccountSaving;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.LoanAccount;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.FeignClientHelper;
import feign.FeignException;
import org.apache.commons.lang3.RandomStringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.Supplier;

import static com.tmb.oneapp.commonpaymentexp.service.accountcommonpayment.AccountCommonPaymentHelper.ACCOUNT_STATUS_ACTIVE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class AccountCommonPaymentHelperTest {
    @InjectMocks
    AccountCommonPaymentHelper accountCommonPaymentHelper;

    @Mock
    CustomerAccountBizClient customerAccountBizClient;
    @Mock
    FeignClientHelper feignClientHelper;

    String crmId;
    String correlationId;
    HttpHeaders headers;
    List<DepositAccount> depositAccountList;
    List<DepositAccount> accountBalanceResponse;

    @BeforeEach
    void setUp() {
        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        depositAccountList = new ArrayList<>();
        accountBalanceResponse = new ArrayList<>();
    }

    @Test
    void testFetchDepositAccountList_ShouldReturnDepositAccountListSuccess() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();
        mockGetAccountListSuccess(depositAccountList);
        List<DepositAccount> actual = accountCommonPaymentHelper.fetchDepositAccountList(correlationId, crmId);

        assertEquals(depositAccountList.size(), actual.size());
    }

    @Test
    void testFetchDepositAccountList_WhenDepositAccountEmptyList_ShouldThrowTMBCommonException() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();
        mockGetAccountListReturnEmptyList();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCommonPaymentHelper.fetchDepositAccountList(correlationId, crmId));

        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testFetchDepositAccountList_WhenFetchDepositAccountTMBCommonException_ShouldThrowTMBCommonException() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();
        mockGetAccountListThrowFeignException();

        Assertions.assertThrows(TMBCommonException.class, () -> accountCommonPaymentHelper.fetchDepositAccountList(correlationId, crmId));
    }

    @Test
    void testSetAvailableBalance_WhenFirstAccountNotHaveAvailableBalance_ShouldReturnDepositAccountListSuccess() throws TMBCommonException {
        BigDecimal availableBalance = null;
        depositAccountList.addAll(factoryInitDepositAccountList());
        depositAccountList.get(0).setAvailableBalance(availableBalance);
        mockGetBalanceReturnAccountBalanceActive();

        List<DepositAccount> actual = accountCommonPaymentHelper.setAvailableBalance(depositAccountList, correlationId, crmId);

        assertEquals(4, actual.size());
        assertShouldCallGetBalanceOneTime();
    }

    @Test
    void testSetAvailableBalance_WhenFirstAccountHaveAvailableBalance_ShouldReturnDepositAccountListSuccess() throws TMBCommonException {
        BigDecimal availableBalance = new BigDecimal("0.00");
        depositAccountList.addAll(factoryInitDepositAccountList());
        depositAccountList.get(0).setAvailableBalance(availableBalance);

        List<DepositAccount> actual = accountCommonPaymentHelper.setAvailableBalance(depositAccountList, correlationId, crmId);

        assertEquals(4, actual.size());
        assertShouldNotCallGetBalance();
    }

    @Test
    void testSetAvailableBalance_WhenDepositAccountListEmpty_ShouldThrowTMBCommonException() {
        depositAccountList.addAll(Collections.emptyList());

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCommonPaymentHelper.setAvailableBalance(depositAccountList, correlationId, crmId));

        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testSetAvailableBalance_WhenFetchDepositAccountReturnCloseAccount_ShouldThrowTMBCommonException() {
        depositAccountList.addAll(factoryInitDepositAccountList());
        mockGetBalanceReturnCloseAccount(accountBalanceResponse);

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCommonPaymentHelper.setAvailableBalance(depositAccountList, correlationId, crmId));

        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testSetAvailableBalance_WhenFetchDepositAccountFeignException_ShouldThrowTMBCommonException() {
        depositAccountList.addAll(factoryInitDepositAccountList());
        mockGetBalanceThrowFeignException();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCommonPaymentHelper.setAvailableBalance(depositAccountList, correlationId, crmId));

        Assertions.assertEquals(ResponseCode.FAILED_V2.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getStatus());
    }

    @Test
    void testFetchHpAccountList_ShouldReturnHpAccountListSuccess() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();
        mockGetAccountListSuccess(depositAccountList);

        List<LoanAccount> actual = accountCommonPaymentHelper.fetchHpAccountList(correlationId, crmId);

        assertEquals(1, actual.size());
    }

    @Test
    void testFetchHpAccountList_WhenHpAccountEmptyList_ShouldThrowTMBCommonException() throws TMBCommonException {
        setUpFeignClientHelperExecuteRequest();
        mockGetAccountListReturnEmptyList();

        TMBCommonException exception = Assertions.assertThrows(TMBCommonException.class, () -> accountCommonPaymentHelper.fetchDepositAccountList(correlationId, crmId));

        Assertions.assertEquals(ResponseCode.NO_ELIGIBLE_DEPOSIT_ACCOUNT_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    private void setUpFeignClientHelperExecuteRequest() throws TMBCommonException {
        when(feignClientHelper.executeRequest(any())).thenAnswer(invocation -> {
            try {
                Supplier<ResponseEntity<TmbServiceResponse<?>>> supplier = invocation.getArgument(0);
                ResponseEntity<TmbServiceResponse<?>> feignResponseEntity = supplier.get();
                return feignResponseEntity.getBody().getData();
            } catch (Exception e) {
                throw new TMBCommonException("");
            }
        });
    }

    private void mockGetAccountListSuccess(List<DepositAccount> depositAccountList) {
        List<DepositAccount> dList = factoryInitDepositAccountList();

        depositAccountList.addAll(dList);
        TmbServiceResponse<AccountSaving> t = new TmbServiceResponse<>();
        t.setData(new AccountSaving().setDepositAccountLists(depositAccountList).setHpAccounts(List.of(new LoanAccount())));
        when(customerAccountBizClient.getAccountList(correlationId, crmId)).thenReturn(ResponseEntity.ok(t));
    }

    private void mockGetAccountListThrowFeignException() {
        when(customerAccountBizClient.getAccountList(correlationId, crmId)).thenThrow(FeignException.class);
    }

    private void mockGetAccountListReturnEmptyList() {
        TmbServiceResponse<AccountSaving> tmbServiceResponse = new TmbServiceResponse<>();
        tmbServiceResponse.setData(new AccountSaving());
        tmbServiceResponse.setStatus(CommonServiceUtils.getStatusSuccess());

        when(customerAccountBizClient.getAccountList(correlationId, crmId)).thenReturn(ResponseEntity.ok(tmbServiceResponse));
    }

    private List<DepositAccount> factoryInitDepositAccountList() {
        List<DepositAccount> dList = new ArrayList<>();

        dList.add(initDepositAccount("PRIIND", "ACTIVE"));
        dList.add(initDepositAccount("IGNORE", "ACTIVE"));
        dList.add(initDepositAccount("PRIIND", "INACTIVE"));
        dList.add(initDepositAccount("IGNORE", "INACTIVE"));

        //Should filter out
        dList.add(initDepositAccount("PRIIND", "DORMANT"));
        dList.add(initDepositAccount("IGNORE", "DORMANT"));
        dList.add(initDepositAccount("PRIIND", "CLOSE"));
        dList.add(initDepositAccount("IGNORE", "CLOSE"));
        return dList;
    }

    private DepositAccount initDepositAccount(String relationshipCode, String accountStatus) {
        return new DepositAccount()
                .setRelationshipCode(relationshipCode)
                .setAccountStatus(accountStatus)
                .setAccountNumber(RandomStringUtils.randomNumeric(10));
    }

    private void mockGetBalanceReturnAccountBalanceActive() {
        DepositAccount accountBalance = new DepositAccount()
                .setAvailableBalance(new BigDecimal("123.50"))
                .setAccountStatus(ACCOUNT_STATUS_ACTIVE)
                .setAccountName("New Name")
                .setLinkedAccount("**********");

        accountBalanceResponse.add(accountBalance);
        TmbOneServiceResponse<List<DepositAccount>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(accountBalanceResponse);
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), anyList())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private void mockGetBalanceReturnCloseAccount(List<DepositAccount> accountBalanceResponse) {
        accountBalanceResponse = Collections.emptyList();
        TmbOneServiceResponse<List<DepositAccount>> tmbOneServiceResponse = new TmbOneServiceResponse<>();
        tmbOneServiceResponse.setData(accountBalanceResponse);
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), anyList())).thenReturn(ResponseEntity.ok(tmbOneServiceResponse));
    }

    private void mockGetBalanceThrowFeignException() {
        when(customerAccountBizClient.getAccountBalance(eq(crmId), eq(correlationId), anyList())).thenThrow(FeignException.class);
    }

    private void assertShouldCallGetBalanceOneTime() {
        Mockito.verify(customerAccountBizClient, times(1)).getAccountBalance(any(), any(), anyList());
    }

    private void assertShouldNotCallGetBalance() {
        Mockito.verify(customerAccountBizClient, never()).getAccountBalance(any(), any(), anyList());
    }
}