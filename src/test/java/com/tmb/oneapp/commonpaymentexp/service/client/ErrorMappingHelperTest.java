package com.tmb.oneapp.commonpaymentexp.service.client;

import com.tmb.common.model.Description;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class ErrorMappingHelperTest {

    @Test
    void testErrorMappingIsNotCopiedNewObject() {
        Map<String, Description> beanErrorMapping = new HashMap<>();
        beanErrorMapping.put("errorFromBeanMapping", new Description("en", "th"));

        ErrorMappingHelper errorMappingHelper = new ErrorMappingHelper(beanErrorMapping);
        beanErrorMapping.put("newErrorSetByBeanObject", new Description("new-error-set-by-bean-th", "new-error-set-by-bean-th"));

        Description actualFromErrorMappingHelper = errorMappingHelper.get("newErrorSetByBeanObject");

        assertNotNull(actualFromErrorMappingHelper, "Prove ErrorMappingHelper use same as object Bean 'errorMapping' created at start service");
        assertEquals(beanErrorMapping.get("newErrorSetByBeanObject").getEn(), actualFromErrorMappingHelper.getEn());
        assertEquals(beanErrorMapping.get("newErrorSetByBeanObject").getTh(), actualFromErrorMappingHelper.getTh());
    }
}