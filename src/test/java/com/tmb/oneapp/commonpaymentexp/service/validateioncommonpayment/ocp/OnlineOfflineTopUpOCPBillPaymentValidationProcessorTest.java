package com.tmb.oneapp.commonpaymentexp.service.validateioncommonpayment.ocp;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.tmb.common.cache.Transaction;
import com.tmb.common.cache.service.TransactionServices;
import com.tmb.common.constants.TmbCommonUtilityConstants;
import com.tmb.common.exception.model.TMBCommonException;
import com.tmb.common.exception.model.TMBCommonExceptionWithResponse;
import com.tmb.common.exception.model.TMBCustomCommonExceptionWithResponse;
import com.tmb.common.model.BillerCreditcardMerchant;
import com.tmb.common.util.TMBUtils;
import com.tmb.oneapp.commonpaymentexp.constant.CommonPaymentExpConstant;
import com.tmb.oneapp.commonpaymentexp.constant.ResponseCode;
import com.tmb.oneapp.commonpaymentexp.model.ConversionRateDetail;
import com.tmb.oneapp.commonpaymentexp.model.DepositAccount;
import com.tmb.oneapp.commonpaymentexp.model.WowPoint;
import com.tmb.oneapp.commonpaymentexp.model.activitylog.commonpayment.validation.ActivityBillPayValidationEvent;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillPayConfiguration;
import com.tmb.oneapp.commonpaymentexp.model.biller.BillerInfoResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.MasterBillerResponse;
import com.tmb.oneapp.commonpaymentexp.model.biller.ReferenceResponse;
import com.tmb.oneapp.commonpaymentexp.model.cache.CommonPaymentRuleInCache;
import com.tmb.oneapp.commonpaymentexp.model.commonauthentication.CommonAuthenResult;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.AmountDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentConfig;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CompleteScreenDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PaymentInformation;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.PhraseDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.ProductDetail;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.AdditionalParam;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPAccountPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPayment;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPBillRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.OCPFee;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.external.WaiveOCP;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.WowPointValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.BillPayPrepareDataValidate;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CardBalancesAsync;
import com.tmb.oneapp.commonpaymentexp.model.creditcardaccount.CreditCardSupplementary;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.LogEventPublisherService;
import com.tmb.oneapp.commonpaymentexp.service.cache.CacheService;
import com.tmb.oneapp.commonpaymentexp.service.client.PaymentService;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.DailyLimitService;
import com.tmb.oneapp.commonpaymentexp.utils.CommonServiceUtils;
import com.tmb.oneapp.commonpaymentexp.utils.TestUtils;
import com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidator;
import com.tmb.oneapp.commonpaymentexp.validator.DailyLimitPinFreeValidator;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_BILL;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILLER_GROUP_TYPE_TOP_UP;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_EASY_PASS;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.BILL_COMP_CODE_FLEET_CARD;
import static com.tmb.oneapp.commonpaymentexp.constant.BillerConstant.SPECIAL_OFFLINE_BILLER_AIA_0002;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_BILL_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_AUTH_TOP_UP_FLOW_NAME;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_BILL_PAY_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.CommonAuthenticationConstant.COMMON_PAYMENT_TOP_UP_FEATURE_ID;
import static com.tmb.oneapp.commonpaymentexp.constant.ProcessorConstant.BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP;
import static com.tmb.oneapp.commonpaymentexp.model.financiallog.BaseFinancialActivityLog.BILLER_GROUP_TOP_UP;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY;
import static com.tmb.oneapp.commonpaymentexp.service.PaymentMethodCommonPaymentService.COMMON_PAYMENT_TRANSACTION_SUBTYPE_TOP_UP;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class OnlineOfflineTopUpOCPBillPaymentValidationProcessorTest {
    @InjectMocks
    private OnlineOfflineTopUpOCPBillPaymentValidationProcessor onlineOfflineTopUpBillPaymentValidationProcessor;

    @Mock
    private PaymentService paymentService;
    @Mock
    private LogEventPublisherService logEventPublisherService;
    @Mock
    private CacheService cacheService;
    @Mock
    private DailyLimitPinFreeValidator dailyLimitPinFreeValidator;
    @Mock
    private BaseBillPayValidator baseBillPayValidator;
    @Mock
    private OCPValidationHelper ocpValidationHelper;
    @Mock
    private DailyLimitService dailyLimitService;
    @Mock
    private TransactionServices transactionServices;

    private String crmId;
    private String correlationId;
    private HttpHeaders headers;
    private ValidationCommonPaymentRequest request;
    private CommonPaymentDraftCache cacheData;
    private CommonPaymentConfig commonPaymentConfig;
    private String compCode;
    private String depositAccountNumber;
    private MasterBillerResponse masterBillerResponse;
    private CreditCardSupplementary creditCardDetail;
    private CustomerCrmProfile customerCrmProfile;
    private DepositAccount depositAccount;
    private BillPayConfiguration billPayConfig;

    @BeforeEach
    void setUp() {
        Transaction.setTransactionServices(transactionServices);

        crmId = "crmId";
        correlationId = "correlationId";
        headers = new HttpHeaders();
        headers.add(CommonPaymentExpConstant.HEADER_CRM_ID, crmId);
        headers.add(CommonPaymentExpConstant.HEADER_CORRELATION_ID, correlationId);

        cacheData = new CommonPaymentDraftCache();
        compCode = "3058";
        depositAccountNumber = "**********";
        request = new ValidationCommonPaymentRequest();

        commonPaymentConfig = new CommonPaymentConfig();

        masterBillerResponse = initialMasterBiller();
        creditCardDetail = initialCreditCardDetails();
        customerCrmProfile = initialCustomerCrmProfile();
        depositAccount = initialDepositAccount();
        billPayConfig = initialBillPayConfig();

    }

    @Test
    void testExecuteBillPay_WhenBillPayTransactionAndPayWithDeposit_ShouldSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse {
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_BILL);

        mockBasePrepareDataReturnData();
        mockValidateDailyLimitExceededDoNothing();
        mockStaticTransactionGenerateId();
        mockValidateOCPBillPayment();
        mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();

        ValidationCommonPaymentResponse actual = onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData);

        Assertions.assertNotNull(actual.getFee());
        Assertions.assertNotNull(actual.getTransactionId());
        Assertions.assertNotNull(actual.getTotalAmount());
        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
        Assertions.assertEquals(COMMON_AUTH_BILL_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
        verify(logEventPublisherService, times(1)).saveActivityLog(any(ActivityBillPayValidationEvent.class));
    }

    @Test
    void testExecuteBillPay_WhenBillPayTransactionAndPayWithCreditCard_ShouldSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse {
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_TOP_UP);
        request = initialDefaultRequest();
        request.getDeposit().setPayWithDepositFlag(false).setAmount(null).setAccountNumber(null);
        request.setCreditCard(
                new CreditCardValidationCommonPaymentRequest()
                        .setPayWithCreditCardFlag(true)
                        .setAmount(BigDecimal.valueOf(9500.50))
                        .setAccountId("0000000050083520705000171")
        );
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_BILL);

        mockBasePrepareDataReturnData();
        mockStaticTransactionGenerateId();
        mockValidateOCPBillPayment();
        mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();

        ValidationCommonPaymentResponse actual = onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData);

        Assertions.assertNotNull(actual.getFee());
        Assertions.assertNotNull(actual.getTransactionId());
        Assertions.assertNotNull(actual.getTotalAmount());
        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
        Assertions.assertEquals(COMMON_AUTH_BILL_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
        assertShouldNotValidateDailyLimit();
        verify(logEventPublisherService, times(1)).saveActivityLog(any(ActivityBillPayValidationEvent.class));
    }

    @Test
    void testExecuteBillPay_WhenBasePrepareDataFailedTMBCommonException_ShouldThrowTMBCommonException() throws TMBCommonException {
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_BILL);
        when(ocpValidationHelper.basePrepareData(request, headers, cacheData)).thenThrow(TMBCommonException.class);

        assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));

        verify(logEventPublisherService, times(1)).saveActivityLog(any(ActivityBillPayValidationEvent.class));
    }

    @Test
    void testExecuteBillPayValidateInsufficientFund_WhenFailedValidateSpecialBillerOffline_ShouldThrowTMBCommonException() throws TMBCommonException {
        compCode = SPECIAL_OFFLINE_BILLER_AIA_0002;
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_TOP_UP);
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_TOP_UP);
        TMBCommonException expectedException = CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.INCORRECT_AIA_REF);

        mockBasePrepareDataReturnData();

        doThrow(expectedException).when(baseBillPayValidator).validateSpecialBillerOffline(eq(compCode), anyString(), anyString(), any(BigDecimal.class), any(MasterBillerResponse.class));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));

        Assertions.assertEquals(expectedException, exception);
    }

    @Test
    void testExecuteBillPayValidateInsufficientFund_WhenPayWithDepositAndAmountExceedAvailableCreditAllowance_ShouldSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
        BigDecimal amountMoreThanAvailableCreditAllowance = BigDecimal.valueOf(***********.50);
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_TOP_UP);
        request = initialDefaultRequest();
        request.getDeposit().setPayWithDepositFlag(false).setAmount(null).setAccountNumber(null);
        request.setCreditCard(
                new CreditCardValidationCommonPaymentRequest()
                        .setPayWithCreditCardFlag(true)
                        .setAmount(amountMoreThanAvailableCreditAllowance)
                        .setAccountId("0000000050083520705000171")
        );
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_TOP_UP);
        mockBasePrepareDataReturnData();
        mockStaticTransactionGenerateId();
        mockValidateOCPBillPayment();
        doThrow(new TMBCommonException(ResponseCode.CREDIT_CARD_INSUFFICIENT_FUNDS_ERROR.getCode(), ResponseCode.CREDIT_CARD_INSUFFICIENT_FUNDS_ERROR.getMessage(), ResponseCode.CREDIT_CARD_INSUFFICIENT_FUNDS_ERROR.getService(), HttpStatus.OK, null)).when(baseBillPayValidator).validateInsufficientFund(any(), any(), any(), any());

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));

        Assertions.assertEquals(ResponseCode.CREDIT_CARD_INSUFFICIENT_FUNDS_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testExecuteBillPayValidateInsufficientFund_WhenFailedCheckCompCodeInExcludeBillerConfig_ShouldThrowTMBCommonException() throws TMBCommonException {
        BigDecimal amountMoreThanAvailableBalance = BigDecimal.valueOf(***********.99);
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_TOP_UP);
        request = initialDefaultRequest();
        request.getDeposit().setAmount(amountMoreThanAvailableBalance);
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_TOP_UP);

        mockBasePrepareDataReturnData();

        doThrow(new TMBCommonException(ResponseCode.EXCLUDE_BILLER_ERROR.getCode(), ResponseCode.EXCLUDE_BILLER_ERROR.getMessage(), ResponseCode.EXCLUDE_BILLER_ERROR.getService(), HttpStatus.OK, null)).when(baseBillPayValidator).validateCompCodeExclusion(any(), any());

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));

        Assertions.assertEquals(ResponseCode.EXCLUDE_BILLER_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testExecuteBillPayValidateInsufficientFund_WhenFailedCheckBillerExpired_ShouldThrowTMBCommonException() throws TMBCommonException {
        BigDecimal amountMoreThanAvailableBalance = BigDecimal.valueOf(***********.99);
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_TOP_UP);
        request = initialDefaultRequest();
        request.getDeposit().setAmount(amountMoreThanAvailableBalance);

        mockBasePrepareDataReturnData();

        doThrow(CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.BILLER_EXPIRED)).when(baseBillPayValidator).validateBillerExpiration(any());

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));

        Assertions.assertEquals(ResponseCode.BILLER_EXPIRED.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testExecuteBillPayValidateInsufficientFund_WhenPayWithDepositAndAmountExceedAvailableBalance_ShouldThrowTMBCommonException() throws TMBCommonException, TMBCommonExceptionWithResponse {
        BigDecimal amountMoreThanAvailableBalance = BigDecimal.valueOf(***********.99);
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_TOP_UP);
        request = initialDefaultRequest();
        request.getDeposit().setAmount(amountMoreThanAvailableBalance);
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_TOP_UP);

        mockBasePrepareDataReturnData();
        mockValidateDailyLimitExceededDoNothing();
        mockStaticTransactionGenerateId();
        mockValidateOCPBillPayment();
        doThrow(new TMBCommonException(ResponseCode.DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR.getCode(), ResponseCode.DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR.getMessage(), ResponseCode.DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR.getService(), HttpStatus.OK, null)).when(baseBillPayValidator).validateInsufficientFund(any(), any(), any(), any());

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));

        Assertions.assertEquals(ResponseCode.DEPOSIT_ACCOUNT_INSUFFICIENT_FUNDS_ERROR.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @Test
    void testExecuteBillPay_WhenFailedCheckServiceHours_ShouldThrowTMBCommonException() throws TMBCommonException {
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_BILL);
        mockBasePrepareDataReturnData();

        doThrow(CommonServiceUtils.getBusinessTmbCommonException(ResponseCode.UNAVAILABLE_SERVICE_HOUR)).when(baseBillPayValidator).validateServiceHours(any(MasterBillerResponse.class));
        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));

        assertEquals(ResponseCode.UNAVAILABLE_SERVICE_HOUR.getCode(), exception.getErrorCode());
        assertEquals(HttpStatus.OK, exception.getStatus());

    }

    @ParameterizedTest
    @CsvSource({"3069", "3070", "3071"})
    void testExecuteBillPayValidateDuplicateRef_WhenFoundRefsInCache_ShouldThrowTMBCommonException(String inputCompCode) throws JsonProcessingException, TMBCommonException {
        compCode = inputCompCode;
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
        cacheData.getPaymentInformation().setCompCode(compCode);
        cacheData.getPaymentInformation().setProductDetail(new ProductDetail().setProductRef1("111").setProductRef2("222"));
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_BILL);
        mockBasePrepareDataReturnData();
        mockValidateDailyLimitExceededDoNothing();
        mockStaticTransactionGenerateId();

        Map<String, String> mapRef = new HashMap<>();
        mapRef.put("ref1", "111");
        mapRef.put("ref2", "222");
        Mockito.when(cacheService.get(any())).thenReturn(TMBUtils.convertJavaObjectToString(mapRef));

        TMBCommonException exception = assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));

        Assertions.assertEquals(ResponseCode.BILL_PAY_TOP_UP_DUPLICATE_REF.getCode(), exception.getErrorCode());
        Assertions.assertEquals(HttpStatus.OK, exception.getStatus());
    }

    @ParameterizedTest
    @CsvSource({"1234, 000", "000, 4321", "000, 000"})
    void testExecuteBillPayValidateDuplicateRef_WhenPartiallyMatched_ShouldDoesNotThrow(String ref1, String ref2) throws JsonProcessingException, TMBCommonException, TMBCommonExceptionWithResponse {
        compCode = "3069";
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
        cacheData.getPaymentInformation().setCompCode(compCode);
        cacheData.getPaymentInformation().setProductDetail(new ProductDetail().setProductRef1(ref1).setProductRef2(ref2));
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_BILL);
        mockBasePrepareDataReturnData();
        mockValidateDailyLimitExceededDoNothing();
        mockStaticTransactionGenerateId();
        mockValidateOCPBillPayment();
        mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();

        Map<String, String> mapRef = new HashMap<>();
        mapRef.put("ref1", "1234");
        mapRef.put("ref2", "4321");
        Mockito.when(cacheService.get(any())).thenReturn(TMBUtils.convertJavaObjectToString(mapRef));

        Assertions.assertDoesNotThrow(() -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));
    }

    @Test
    void testExecuteBillPayValidateDuplicateRef_WhenFailedToGetCache_ShouldDoesNotThrow() throws TMBCommonException, TMBCommonExceptionWithResponse {
        compCode = "3070";
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
        cacheData.getPaymentInformation().setCompCode(compCode);
        cacheData.getPaymentInformation().setProductDetail(new ProductDetail().setProductRef1("987").setProductRef2("789"));
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_BILL);
        mockBasePrepareDataReturnData();
        mockValidateDailyLimitExceededDoNothing();
        mockStaticTransactionGenerateId();
        mockValidateOCPBillPayment();
        mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();

        Mockito.when(cacheService.get(any())).thenThrow(RuntimeException.class);

        Assertions.assertDoesNotThrow(() -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));
    }

    @Test
    void testExecuteBillPay_WhenTopUpFleetCardTransactionWithDeposit_ShouldSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse {
        compCode = BILL_COMP_CODE_FLEET_CARD;
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_TOP_UP);
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TOP_UP);
        mockBasePrepareDataReturnData();
        mockValidateDailyLimitExceededDoNothing();
        mockStaticTransactionGenerateId();
        mockValidateOCPBillPayment();
        mockValidateIsRequireCommonAuthForTopUpReturnRequireCommonAuth();

        ValidationCommonPaymentResponse actual = onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData);

        Assertions.assertNotNull(actual.getFee());
        Assertions.assertNotNull(actual.getTransactionId());
        Assertions.assertNotNull(actual.getTotalAmount());
        Assertions.assertTrue(actual.getIsRequireCommonAuthen());
        Assertions.assertEquals(COMMON_PAYMENT_TOP_UP_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
        Assertions.assertEquals(COMMON_AUTH_TOP_UP_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
        Assertions.assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
    }

    @Test
    void testExecuteBillPay_WhenTopUpEasyPassTransactionWithDeposit_ShouldSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse {
        compCode = BILL_COMP_CODE_EASY_PASS;
        cacheData = initialCacheDataForBillPayTransaction();
        cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_TOP_UP);
        request = initialDefaultRequest();
        masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TOP_UP);
        mockBasePrepareDataReturnData();
        mockValidateDailyLimitExceededDoNothing();
        mockStaticTransactionGenerateId();
        mockValidateOCPBillPayment();
        mockValidateIsRequireCommonAuthForTopUpReturnRequireCommonAuth();

        ValidationCommonPaymentResponse actual = onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData);

        Assertions.assertEquals(COMMON_PAYMENT_TOP_UP_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
        Assertions.assertEquals(COMMON_AUTH_TOP_UP_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
        Assertions.assertNotNull(actual.getEasyPass().getEasyPassTopUpRef());
        Assertions.assertNotNull(actual.getEasyPass().getEasyPassAccountName());
    }

    @Test
    void testGetProcessorType_ShouldReturnBillPay() {
        String actual = onlineOfflineTopUpBillPaymentValidationProcessor.getProcessorType();

        Assertions.assertEquals(BILLER_PAYMENT_ONLINE_OFFLINE_TOPUP, actual);
    }

    @Nested
    class CalculateFeeTest {
        @Test
        void testCalculateFeeWhenWaiveFlagIsYThenReturnZeroFee() {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee("25.00");
            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag("Y");
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType("SDA");
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment);

            assertEquals(new BigDecimal("0.00"), result);
        }

        @Test
        void testCalculateFeeWhenPayWithCreditCardThenReturnZeroFee() {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee("25.00");
            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag("N");
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType(TmbCommonUtilityConstants.ACCOUNT_TYPE_CCA);
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment);

            assertEquals(new BigDecimal("0.00"), result);
        }

        @Test
        void testCalculateFeeWhenHasBillPmtFeeThenReturnBillPmtFee() {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee("25.00");
            fee.setPaymentFee("15.00");
            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag("N");
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType("SDA");
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment);

            assertEquals(new BigDecimal("25.00"), result);
        }

        @Test
        void testCalculateFeeWhenHasPaymentFeeButNoBillPmtFeeThenReturnPaymentFee() {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee(null);
            fee.setPaymentFee("15.00");
            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag("N");
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType("SDA");
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment);

            assertEquals(new BigDecimal("15.00"), result);
        }

        @Test
        void testCalculateFeeWhenNoFeesAvailableThenReturnZeroFee() {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee(null);
            fee.setPaymentFee(null);
            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag("N");
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType("SDA");
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment);

            assertEquals(new BigDecimal("0.00"), result);
        }

        @Test
        void testCalculateFeeWhenWaiveIsNullThenShouldNotThrowException() {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();
            fee.setBillPmtFee("25.00");
            ocpBillPayment.setFee(fee);

            ocpBillPayment.setWaive(null);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType("SDA");
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment);

            assertEquals(new BigDecimal("25.00"), result);
        }

        @ParameterizedTest
        @CsvSource({
                "25.00, 15.00, N, SDA, 25.00",
                "null, 15.00, N, SDA, 15.00",
                "null, null, N, SDA, 0.00",
                "25.00, 15.00, Y, SDA, 0.00",
                "25.00, 15.00, N, CCA, 0.00"
        })
        void testCalculateFeeWithVariousParameters(String billPmtFee, String paymentFee, String waiveFlag, String accountType, String expectedFee) {
            OCPBillPayment ocpBillPayment = new OCPBillPayment();
            OCPFee fee = new OCPFee();

            if (!"null".equals(billPmtFee)) {
                fee.setBillPmtFee(billPmtFee);
            }

            if (!"null".equals(paymentFee)) {
                fee.setPaymentFee(paymentFee);
            }

            ocpBillPayment.setFee(fee);

            WaiveOCP waive = new WaiveOCP();
            waive.setFlag(waiveFlag);
            ocpBillPayment.setWaive(waive);

            OCPAccountPayment accountPayment = new OCPAccountPayment();
            accountPayment.setAccountType(accountType);
            ocpBillPayment.setFromAccount(accountPayment);

            BigDecimal result = invokeCalculateFee(ocpBillPayment);

            assertEquals(new BigDecimal(expectedFee), result);
        }

        private BigDecimal invokeCalculateFee(OCPBillPayment ocpBillPayment) {
            return TestUtils.invokeMethod(onlineOfflineTopUpBillPaymentValidationProcessor, "calculateFee", ocpBillPayment);
        }
    }

    @Test
    void testTransformRequest_ShouldTransformRequest() {
        ProductDetail productDetail = new ProductDetail()
                .setProductRef1("lowercase1")
                .setProductRef2("lowercase2");

        assertDoesNotThrow(() -> TestUtils.invokeMethod(onlineOfflineTopUpBillPaymentValidationProcessor, "transformRequestBody", productDetail));

        assertEquals("LOWERCASE1", productDetail.getProductRef1());
        assertEquals("LOWERCASE2", productDetail.getProductRef2());
    }

    @Nested
    class BillPayWithWowPointTransactionTest {
        @Test
        void testExecuteBillPay_WhenBillPayTransactionAndPayWithDepositAndWowPoint_ShouldSuccess() throws TMBCommonException, TMBCommonExceptionWithResponse {
            cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(new WowPoint()
                    .setMinPaymentAmount(new BigDecimal(100))
                    .setMin(new BigDecimal(10))
                    .setMax(new BigDecimal(10000))
                    .setConversionRate(new ConversionRateDetail(new BigDecimal(2), new BigDecimal(15))));
            cacheData.setCommonPaymentRule(commonPaymentRule);

            request = initialDefaultRequest();
            request.setWowPoint(new WowPointValidationCommonPaymentRequest()
                    .setWowPointAmount(new BigDecimal(1500))
                    .setDiscountAmount(new BigDecimal(200)));

            masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_BILL);
            mockBasePrepareDataReturnData();
            mockValidateDailyLimitExceededDoNothing();
            mockStaticTransactionGenerateId();
            mockValidateOCPBillPayment();
            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();

            Assertions.assertDoesNotThrow(() -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));
            verify(baseBillPayValidator, times(1)).validateWowPoint(request, cacheData);
        }

        @Test
        void testExecuteBillPay_WhenValidateNormallyBillPayTransactionWithNullWowPoint_ShouldSuccess() throws TMBCommonException, TMBCustomCommonExceptionWithResponse, TMBCommonExceptionWithResponse {
            cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            request = initialDefaultRequest();
            masterBillerResponse.getBillerInfo().setBillerGroupType(BILLER_GROUP_TYPE_BILL);
            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);
            cacheData.setCommonPaymentRule(commonPaymentRule);

            mockBasePrepareDataReturnData();
            mockValidateDailyLimitExceededDoNothing();
            mockStaticTransactionGenerateId();
            mockValidateOCPBillPayment();
            mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth();

            request.setWowPoint(null);

            ValidationCommonPaymentResponse actual = onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData);

            Assertions.assertNotNull(actual.getFee());
            Assertions.assertNotNull(actual.getTransactionId());
            Assertions.assertNotNull(actual.getTotalAmount());
            Assertions.assertTrue(actual.getIsRequireCommonAuthen());
            Assertions.assertEquals(COMMON_PAYMENT_BILL_PAY_FEATURE_ID, actual.getCommonAuthenticationInformation().getFeatureId());
            Assertions.assertEquals(COMMON_AUTH_BILL_FLOW_NAME, actual.getCommonAuthenticationInformation().getFlowName());
            Assertions.assertNotNull(actual.getCommonAuthenticationInformation().getTotalPaymentAccumulateUsage());
        }

        @Test
        void testExecuteBillPay_WhenFailedValidateWowPoint_ShouldThrowTMBCommonException() throws TMBCommonException {
            WowPointValidationCommonPaymentRequest invalidWowPoint = new WowPointValidationCommonPaymentRequest()
                    .setWowPointAmount(new BigDecimal(1000))
                    .setDiscountAmount(new BigDecimal(100));

            CommonPaymentRuleInCache commonPaymentRule = new CommonPaymentRuleInCache();
            commonPaymentRule.setWowPointFlag(true);
            commonPaymentRule.setWowPoint(new WowPoint()
                    .setMinPaymentAmount(new BigDecimal(10))
                    .setMin(new BigDecimal(10))
                    .setMax(new BigDecimal(100))
                    .setConversionRate(new ConversionRateDetail(new BigDecimal(1), new BigDecimal(10))));

            cacheData = initialCacheDataForBillPayTransaction();
            cacheData.getPaymentInformation().setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY);
            cacheData.setCommonPaymentRule(commonPaymentRule);

            request = initialDefaultRequest();
            request.getDeposit().setAmount(BigDecimal.valueOf(101));
            request.setWowPoint(invalidWowPoint);

            mockBasePrepareDataReturnData();
            mockValidateDailyLimitExceededDoNothing();

            doThrow(TMBCommonException.class).when(baseBillPayValidator).validateWowPoint(request, cacheData);

            assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentValidationProcessor.executeValidate(request, headers, cacheData));
        }
    }

    @Nested
    class HandleExceptionTest {
        @Test
        void testHandleException_WhenFailedTMBCommonException_ShouldThrowTMBCommonException() {
            assertThrows(TMBCommonException.class, () -> onlineOfflineTopUpBillPaymentValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonException("ignore message")));
        }

        @Test
        void testHandleException_WhenFailedTMBCommonExceptionWithResponse_ShouldThrowTMBCommonExceptionWithResponse() {
            assertThrows(TMBCommonExceptionWithResponse.class, () -> onlineOfflineTopUpBillPaymentValidationProcessor.handleException(null, null, null, null, null, null, new TMBCommonExceptionWithResponse("", "", "", HttpStatus.OK, null, null)));
        }
    }

    private void assertShouldNotValidateDailyLimit() throws TMBCommonException {
        Mockito.verify(dailyLimitService, never()).validateDailyLimitExceeded(any(), any(), any());
    }

    private void mockBasePrepareDataReturnData() throws TMBCommonException {
        var prepareData = BillPayPrepareDataValidate.builder()
                .billPayConfiguration(billPayConfig)
                .masterBillerResponse(masterBillerResponse)
                .fromDepositAccount(depositAccount)
                .fromCreditCardDetail(creditCardDetail)
                .customerCrmProfile(customerCrmProfile)
                .build();

        when(ocpValidationHelper.basePrepareData(request, headers, cacheData)).thenReturn(prepareData);
    }

    private void mockValidateOCPBillPayment() throws TMBCommonException, TMBCommonExceptionWithResponse {
        OCPBillPaymentResponse ocpBillPaymentResponse = new OCPBillPaymentResponse();
        OCPBillPayment ocpResponse = new OCPBillPayment();
        ocpResponse.setToAccount(new OCPAccountPayment()
                .setAccountId("**********")
                .setAccountType("CDA")
                .setFiId("")
                .setTitle(null)
        );
        ocpResponse.setFromAccount(new OCPAccountPayment()
                .setAccountId("**********")
                .setAccountType("SDA")
                .setFiId("")
                .setTitle(null)
        );
        ocpResponse.setAdditionalParams(List.of(new AdditionalParam().setName("Msg").setValue("|AdditionalParamValue")));
        ocpResponse.setAmount("9500.50");
        ocpResponse.setRef1("Ref1");
        ocpResponse.setRef2("Ref2");
        ocpResponse.setRef3("Ref3");
        ocpResponse.setRef4("Ref4");
        ocpResponse.setBankRefId("BankRefId");
        ocpResponse.setBankRefId("BankRefId");
        ocpResponse.setFee(new OCPFee().setPaymentFee("10.50"));

        ocpBillPaymentResponse.setData(ocpResponse);
        when(paymentService.validateOCPBillPayment(anyString(), anyString(), any(OCPBillRequest.class))).thenReturn(ocpBillPaymentResponse);
    }

    private void mockStaticTransactionGenerateId() {
        when(Transaction.getTransactionId(anyString(), anyInt())).thenReturn("transaction-after-generate");
    }

    private void mockValidateIsRequireCommonAuthForBillReturnRequireCommonAuth() throws TMBCommonException {
        when(dailyLimitPinFreeValidator.validateIsRequireCommonAuthForBill(any(HttpHeaders.class), any(BigDecimal.class), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(new CommonAuthenResult().setRequireCommonAuthen(true));
    }

    private void mockValidateIsRequireCommonAuthForTopUpReturnRequireCommonAuth() throws TMBCommonException {
        when(dailyLimitPinFreeValidator.validateIsRequireCommonAuthForTopUp(any(HttpHeaders.class), any(BigDecimal.class), anyBoolean(), any(CustomerCrmProfile.class))).thenReturn(new CommonAuthenResult().setRequireCommonAuthen(true));
    }

    private void mockValidateDailyLimitExceededDoNothing() throws TMBCommonException {
        doNothing().when(dailyLimitService).validateDailyLimitExceeded(anyString(), any(CustomerCrmProfile.class), any(BigDecimal.class));
    }

    private CommonPaymentDraftCache initialCacheDataForBillPayTransaction() {
        PaymentInformation p = new PaymentInformation()
                .setEntryId(COMMON_PAYMENT_TRANSACTION_SUBTYPE_BILL_PAY)
                .setTransactionType("bill_pay")
                .setCompCode(compCode)
                .setFundCode("ignore")
                .setRequireAddressFlag(true)
                .setProductDetail(new ProductDetail()
                        .setProductNameEn("Product-name-en-value")
                        .setProductNameTh("Product-name-th-value")
                        .setProductAttributeList(List.of(
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("1000000006")
                                        .setValueTh("1000000006"),
                                new PhraseDetail()
                                        .setLabelEn("label-en-1")
                                        .setLabelTh("label-th-1")
                                        .setValueEn("0987834783")
                                        .setValueTh("0987834783")
                        ))
                        .setProductRef1("reference1")
                        .setProductRef2("reference2")
                )
                .setAmountDetail(new AmountDetail()
                        .setAmountLabelEn("Amount-lable-en-value")
                        .setAmountLabelTh("Amount-lable-th-value")
                        .setAmountUnitEn("Amount-unit-en-value")
                        .setAmountUnitTh("Amount-unit-th-value")
                        .setAmountValue(BigDecimal.valueOf(9855.00))
                )
                .setCompleteScreenDetail(new CompleteScreenDetail()
                        .setRemarkEn("Remark-en-value")
                        .setRemarkTh("Remark-th-value")
                        .setFooterEn("Footer-en-value")
                        .setFooterTh("Footer-th-value")
                        .setBackBtnKeyEn("Back-btn-key-en-value")
                        .setBackBtnKeyTh("Back-btn-key-th-value")
                        .setBackBtnUrl("www.google.co.th")
                );

        return new CommonPaymentDraftCache()
                .setPaymentInformation(p)
                .setCrmId(crmId)
                .setCommonPaymentConfig(commonPaymentConfig);
    }

    private BillPayConfiguration initialBillPayConfig() {
        BillerCreditcardMerchant billerCreditcardMerchant = new BillerCreditcardMerchant();
        billerCreditcardMerchant.setMerchantId("merchantId");
        return new BillPayConfiguration().setBillerCreditcardMerchant(List.of(billerCreditcardMerchant));
    }

    private MasterBillerResponse initialMasterBiller() {
        return new MasterBillerResponse().setBillerInfo(
                        new BillerInfoResponse()
                                .setStartTime(null)
                                .setEndTime(null)
                                .setExpiredDate("9999-12-31T00:00:00.000000+07:00")
                                .setPaymentMethod("5")
                                .setBillerMethod("0")
                                .setNameEn("PB_PRUDENTIAL LIFE ASSURANCE (THAILAND) PCL")
                                .setBillerCategoryCode("03")
                )
                .setRef1(new ReferenceResponse().setIsMobile(false));
    }

    private DepositAccount initialDepositAccount() {
        return new DepositAccount()
                .setAccountNumber(depositAccountNumber)
                .setAccountType("SDA")
                .setAvailableBalance(BigDecimal.valueOf(999999.99));
    }

    private CustomerCrmProfile initialCustomerCrmProfile() {
        return new CustomerCrmProfile()
                .setPaymentAccuUsgAmt(BigDecimal.valueOf(100.50))
                .setBillpayAccuUsgAmt(BigDecimal.valueOf(2000.50))
                .setEbAccuUsgAmtDaily(2000.50);
    }

    private CreditCardSupplementary initialCreditCardDetails() {
        creditCardDetail = new CreditCardSupplementary();
        creditCardDetail.setCardNo("CardNo");
        creditCardDetail.setAccountId("0000000050083520705000171");
        creditCardDetail.setExpiredBy("ExpiredBy");
        creditCardDetail.setCardEmbossingName1("cardEmbossingName1");
        creditCardDetail.setProductCode("ProductId");
        creditCardDetail.setCardBalances(new CardBalancesAsync());
        creditCardDetail.getCardBalances().setAvailableCreditAllowance(BigDecimal.valueOf(999999.99));
        return creditCardDetail;
    }

    private ValidationCommonPaymentRequest initialDefaultRequest() {
        request = new ValidationCommonPaymentRequest();
        request.setDeposit(new DepositValidationCommonPaymentRequest().setPayWithDepositFlag(true).setAccountNumber(depositAccountNumber).setAmount(BigDecimal.valueOf(9500.50)));
        request.setCreditCard(new CreditCardValidationCommonPaymentRequest().setPayWithCreditCardFlag(false));
        request.setFlow("flow");
        request.setTransactionId("transaction_id");
        return request;
    }
}