package com.tmb.oneapp.commonpaymentexp.service;

import com.tmb.oneapp.commonpaymentexp.model.commonpayment.CommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CommonAuthenticationValidationCommonPaymentResponse;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.CreditCardValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.DepositValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.ValidationCommonPaymentRequest;
import com.tmb.oneapp.commonpaymentexp.model.commonpayment.validation.billpay.ValidationCommonPaymentDraftCache;
import com.tmb.oneapp.commonpaymentexp.model.customer.AccumulateUsageRequest;
import com.tmb.oneapp.commonpaymentexp.model.customer.CustomerCrmProfile;
import com.tmb.oneapp.commonpaymentexp.service.dailylimit.TopUpAccumulateUsageStrategy;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
class TopUpAccumulateUsageStrategyTest {
    private TopUpAccumulateUsageStrategy strategy;

    private CommonPaymentDraftCache draftCache;
    private CustomerCrmProfile customerProfile;


    private BigDecimal amount;

    @BeforeEach
    void setUp() {
        draftCache = new CommonPaymentDraftCache();
        draftCache.setValidateRequest(new ValidationCommonPaymentRequest()
                        .setDeposit(new DepositValidationCommonPaymentRequest())
                        .setCreditCard(new CreditCardValidationCommonPaymentRequest()))
                .setValidateDraftCache(new ValidationCommonPaymentDraftCache());

        customerProfile = new CustomerCrmProfile();
        customerProfile.setEbAccuUsgAmtDaily(500.00);
        customerProfile.setPaymentAccuUsgAmt(new BigDecimal("2000.00"));

        BigDecimal frPaymentAccumulateAmountLimit = BigDecimal.valueOf(200000);
        strategy = new TopUpAccumulateUsageStrategy(frPaymentAccumulateAmountLimit);
    }

    @Test
    void createRequest_WithValidInputs_ShouldCalculateCorrectAccumulation() {
        amount = new BigDecimal("1000.00");
        CommonAuthenticationValidationCommonPaymentResponse commonAuthenticationValidationCommonPaymentResponse = new CommonAuthenticationValidationCommonPaymentResponse();
        commonAuthenticationValidationCommonPaymentResponse.setTotalPaymentAccumulateUsage(new BigDecimal("3000.00"));

        draftCache.getValidateRequest().getDeposit().setAmount(amount);
        draftCache.getValidateDraftCache().setCommonAuthentication(commonAuthenticationValidationCommonPaymentResponse);

        AccumulateUsageRequest result = strategy.createRequest(draftCache, customerProfile);

        assertNotNull(result);
        assertEquals(new BigDecimal("1500.00"), result.getDailyAccumulateUsageAmount());
        assertEquals(new BigDecimal("3000.00"), result.getPaymentAccumulateUsageAmount());
    }

    @Test
    void createRequest_WithZeroCurrentAccumulation_ShouldAddOnlyNewAmount() {
        amount = new BigDecimal("1000.00");

        CommonAuthenticationValidationCommonPaymentResponse commonAuthenticationValidationCommonPaymentResponse = new CommonAuthenticationValidationCommonPaymentResponse();
        commonAuthenticationValidationCommonPaymentResponse.setTotalPaymentAccumulateUsage(new BigDecimal("1000.00"));

        draftCache.getValidateRequest().getDeposit().setAmount(amount);
        draftCache.getValidateDraftCache().setCommonAuthentication(commonAuthenticationValidationCommonPaymentResponse);

        customerProfile.setEbAccuUsgAmtDaily(0.00);
        customerProfile.setPaymentAccuUsgAmt(BigDecimal.ZERO);

        AccumulateUsageRequest result = strategy.createRequest(draftCache, customerProfile);

        assertNotNull(result);
        assertEquals(new BigDecimal("1000.00"), result.getDailyAccumulateUsageAmount());
        assertEquals(new BigDecimal("1000.00"), result.getPaymentAccumulateUsageAmount());
    }

    @Test
    void createRequest_WithNullCurrentPaymentAccumulation_ShouldHandleGracefully() {
        amount = new BigDecimal("1000.00");
        CommonAuthenticationValidationCommonPaymentResponse commonAuthenticationValidationCommonPaymentResponse = new CommonAuthenticationValidationCommonPaymentResponse();
        commonAuthenticationValidationCommonPaymentResponse.setTotalPaymentAccumulateUsage(new BigDecimal("1000.00"));

        draftCache.getValidateRequest().getDeposit().setAmount(amount);
        draftCache.getValidateDraftCache().setCommonAuthentication(commonAuthenticationValidationCommonPaymentResponse);
        customerProfile.setPaymentAccuUsgAmt(null);

        AccumulateUsageRequest result = strategy.createRequest(draftCache, customerProfile);

        assertNotNull(result);
        assertEquals(new BigDecimal("1500.00"), result.getDailyAccumulateUsageAmount());
        assertEquals(new BigDecimal("1000.00"), result.getPaymentAccumulateUsageAmount());
    }

    @Test
    void createRequest_WithDecimalAmount_ShouldCalculateCorrectly() {
        CommonAuthenticationValidationCommonPaymentResponse commonAuthenticationValidationCommonPaymentResponse = new CommonAuthenticationValidationCommonPaymentResponse();
        commonAuthenticationValidationCommonPaymentResponse.setTotalPaymentAccumulateUsage(BigDecimal.valueOf(3001.25));

        draftCache.getValidateDraftCache().setCommonAuthentication(commonAuthenticationValidationCommonPaymentResponse);
        draftCache.getValidateRequest().getDeposit().setAmount(new BigDecimal("1000.50"));


        customerProfile.setEbAccuUsgAmtDaily(500.25);
        customerProfile.setPaymentAccuUsgAmt(new BigDecimal("2000.75"));

        AccumulateUsageRequest result = strategy.createRequest(draftCache, customerProfile);

        assertNotNull(result);
        assertEquals(new BigDecimal("1500.75"), result.getDailyAccumulateUsageAmount());
        assertEquals(new BigDecimal("3001.25"), result.getPaymentAccumulateUsageAmount());
    }

    @Test
    void createRequest_WithLargeNumbers_ShouldCalculateAccurately() {
        amount = new BigDecimal("99999.99");

        CommonAuthenticationValidationCommonPaymentResponse commonAuthenticationValidationCommonPaymentResponse = new CommonAuthenticationValidationCommonPaymentResponse();
        commonAuthenticationValidationCommonPaymentResponse.setTotalPaymentAccumulateUsage(BigDecimal.valueOf(1999999.98));

        draftCache.getValidateDraftCache().setCommonAuthentication(commonAuthenticationValidationCommonPaymentResponse);
        draftCache.getValidateRequest().getDeposit().setAmount(amount);

        customerProfile.setEbAccuUsgAmtDaily(99999.99);
        customerProfile.setPaymentAccuUsgAmt(new BigDecimal("99999.99"));

        AccumulateUsageRequest result = strategy.createRequest(draftCache, customerProfile);

        assertNotNull(result);
        assertEquals(new BigDecimal("199999.98"), result.getDailyAccumulateUsageAmount());
        assertEquals(new BigDecimal("199999.98"), result.getPaymentAccumulateUsageAmount());
    }

    @Test
    void testCreateRequestWhenExceedLimitShouldReturnZeroPaymentAccumulate() {
        amount = new BigDecimal("200001");
        customerProfile.setEbAccuUsgAmtDaily(1000.0);

        CommonAuthenticationValidationCommonPaymentResponse commonAuthenticationValidationCommonPaymentResponse = new CommonAuthenticationValidationCommonPaymentResponse();


        draftCache.getValidateRequest().getDeposit().setAmount(amount);
        draftCache.getValidateDraftCache().setCommonAuthentication(commonAuthenticationValidationCommonPaymentResponse);

        AccumulateUsageRequest result = strategy.createRequest(draftCache, customerProfile);

        assertEquals(BigDecimal.ZERO, result.getPaymentAccumulateUsageAmount());
        BigDecimal expectedDailyAccumulate = BigDecimal.valueOf(customerProfile.getEbAccuUsgAmtDaily()).add(amount);
        assertEquals(expectedDailyAccumulate, result.getDailyAccumulateUsageAmount());
    }

    @Test
    void testCreateRequestWhenNotExceedLimitShouldReturnNormalPaymentAccumulate() {
        customerProfile.setEbAccuUsgAmtDaily(1000.0);

        CommonAuthenticationValidationCommonPaymentResponse commonAuthenticationValidationCommonPaymentResponse = new CommonAuthenticationValidationCommonPaymentResponse();

        amount = new BigDecimal("150000.00");
        draftCache.getValidateRequest().getDeposit().setAmount(amount);
        draftCache.getValidateDraftCache().setCommonAuthentication(commonAuthenticationValidationCommonPaymentResponse);

        AccumulateUsageRequest result = strategy.createRequest(draftCache, customerProfile);

        BigDecimal expectedPaymentAccumulate = customerProfile.getPaymentAccuUsgAmt().add(amount);
        assertEquals(expectedPaymentAccumulate, result.getPaymentAccumulateUsageAmount());
        BigDecimal expectedDailyAccumulate = BigDecimal.valueOf(customerProfile.getEbAccuUsgAmtDaily()).add(amount);
        assertEquals(expectedDailyAccumulate, result.getDailyAccumulateUsageAmount());
    }
}