# Technical Design Document (TDD)
## Common Payment Experience Service

**Version:** 1.0.0  
**Last Updated:** August 2, 2025  
**Document Owner:** Common Payment Team  
**Status:** Final

---

## Table of Contents

1. [Executive Summary](#executive-summary)
2. [System Architecture](#system-architecture)
3. [API Design](#api-design)
4. [Data Models](#data-models)
5. [Service Layer](#service-layer)
6. [External Integrations](#external-integrations)
7. [Configuration](#configuration)
8. [Security](#security)
9. [Caching Strategy](#caching-strategy)
10. [Error Handling](#error-handling)
11. [Deployment](#deployment)
12. [Monitoring & Logging](#monitoring--logging)

---

## Executive Summary

The Common Payment Experience Service is a microservice designed to provide a unified payment processing interface for various payment types including bill payments, credit card payments, auto loan payments, and e-wallet transactions. The service implements a four-step payment flow: initialization, payment method selection, validation, and confirmation.

### Key Features
- Unified payment interface for multiple payment types
- Support for bill payments, credit card payments, auto loans, and e-wallets
- Deep link integration for seamless mobile app experience
- Comprehensive validation and confirmation workflows
- Real-time payment processing with external service integration
- Configurable payment rules and limits

---

## System Architecture

### Technology Stack
- **Language:** Java 17
- **Framework:** Spring Boot 3.x
- **API Documentation:** OpenAPI 3.0 (Swagger)
- **HTTP Client:** OpenFeign with OkHttp
- **Cache:** Redis
- **Message Queue:** Kafka
- **Security:** JWT with JWK Set

### Architecture Pattern
The service follows a **microservices architecture** with:
- **Controller Layer:** RESTful API endpoints
- **Service Layer:** Business logic orchestration
- **Client Layer:** External service integration
- **Cache Layer:** Redis for temporary data storage
- **Message Layer:** Kafka for event streaming

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway                              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│              Common Payment Service                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Controller    │  │     Service     │  │     Client      │ │
│  │      Layer      │  │     Layer       │  │     Layer       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   Validation    │  │   Confirmation  │  │   Cache         │ │
│  │   Service       │  │   Service       │  │   Service       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│              External Services                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────┐ │
│  │   Payment   │  │   Customer  │  │   Account   │  │   Bank    │ │
│  │   Service   │  │   Service   │  │   Service   │  │   Service │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └───────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## API Design

### Base URL
```
https://api.oneapp.tmbbank.local/v1/common-payment
```

### Core Endpoints

#### 1. Payment Initialization
**POST** `/common-payment/initial`

**Purpose:** Initialize a new payment transaction and generate deep link

**Request:**
```json
{
  "payment_information": {
    "entry_id": "BILL_PAYMENT_ENTRY",
    "transaction_type": "BILL_PAY",
    "comp_code": "0001",
    "fund_code": "FUND001",
    "require_address_flag": true,
    "product_detail": {
      "product_code": "BILL_PAYMENT",
      "product_name": "Bill Payment Service"
    },
    "amount_detail": {
      "amount": 1000.00,
      "currency": "THB"
    }
  }
}
```

**Response:**
```json
{
  "status": {
    "code": "0000",
    "message": "Success"
  },
  "data": {
    "deeplink_url": "oneappvit://linkaccess/commonpayment?transaction_id=uuid-here"
  }
}
```

#### 2. Payment Method Selection
**GET** `/common-payment/payment-method/{transaction-id}`

**Purpose:** Retrieve available payment methods for the transaction

#### 3. Payment Validation
**POST** `/common-payment/validate`

**Purpose:** Validate payment details before confirmation

#### 4. Payment Confirmation
**POST** `/common-payment/confirm`

**Purpose:** Finalize and process the payment

### Controller Classes

- [`CommonPaymentController`](src/main/java/com/tmb/oneapp/commonpaymentexp/controller/v1/CommonPaymentController.java:45)
- [`AccountCommonPaymentController`](src/main/java/com/tmb/oneapp/commonpaymentexp/controller/v1/AccountCommonPaymentController.java:35)
- [`CreditCardAccountCommonPaymentController`](src/main/java/com/tmb/oneapp/commonpaymentexp/controller/v1/CreditCardAccountCommonPaymentController.java:35)

---

## Data Models

### Core Data Structures

#### PaymentInformation
```java
public class PaymentInformation {
    private String entryId;           // Required: Entry point identifier
    private String transactionType;   // Required: Type of transaction
    private String compCode;        // Required: Company/biller code
    private String fundCode;        // Optional: Fund identifier
    private boolean requireAddressFlag; // Required: Address requirement flag
    private ProductDetail productDetail; // Required: Product information
    private AmountDetail amountDetail; // Required: Payment amount details
    private Schedule schedule;       // Optional: Scheduled payment
    private CallbackInitialRequest callback; // Optional: Partner callback
}
```

#### Transaction Flow Models

1. **InitializationCommonPaymentRequest** - Initial payment setup
2. **PaymentMethodCommonPaymentResponse** - Available payment methods
3. **ValidationCommonPaymentRequest** - Validation request
4. **ConfirmationCommonPaymentRequest** - Final confirmation

### Model Packages
- [`model.commonpayment`](src/main/java/com/tmb/oneapp/commonpaymentexp/model/commonpayment/)
- [`model.biller`](src/main/java/com/tmb/oneapp/commonpaymentexp/model/biller/)
- [`model.creditcardaccount`](src/main/java/com/tmb/oneapp/commonpaymentexp/model/creditcardaccount/)
- [`model.cache`](src/main/java/com/tmb/oneapp/commonpaymentexp/model/cache/)

---

## Service Layer

### Service Architecture

#### 1. Initialization Service
**Class:** [`InitializationCommonPaymentService`](src/main/java/com/tmb/oneapp/commonpaymentexp/service/initializationcommonpayment/InitializationCommonPaymentService.java:34)

**Responsibilities:**
- Generate unique transaction ID
- Validate payment information
- Fetch master biller details
- Cache payment data
- Generate deep link URL

**Key Methods:**
- `initialCommonPayment()` - Main initialization flow
- `prepareData()` - Prepare validation data
- `validateData()` - Validate input parameters
- `getProcessorType()` - Determine payment processor

#### 2. Validation Service
**Package:** [`service.validateioncommonpayment`](src/main/java/com/tmb/oneapp/commonpaymentexp/service/validateioncommonpayment/)

**Validation Types:**
- Auto loan payment validation
- Credit card payment validation
- E-wallet validation
- OCP bill payment validation
- PromptPay validation

#### 3. Confirmation Service
**Package:** [`service.confirmationcommonpayment`](src/main/java/com/tmb/oneapp/commonpaymentexp/service/confirmationcommonpayment/)

**Confirmation Types:**
- Auto loan confirmation
- Credit card confirmation
- OCP bill confirmation
- Custom bill payment confirmation

#### 4. Cache Service
**Class:** [`CacheService`](src/main/java/com/tmb/oneapp/commonpaymentexp/service/cache/CacheService.java)

**Responsibilities:**
- Redis cache operations
- Payment data temporary storage
- Transaction state management

---

## External Integrations

### Feign Client Configuration

#### Payment Service Client
**Interface:** [`PaymentServiceClient`](src/main/java/com/tmb/oneapp/commonpaymentexp/client/PaymentServiceClient.java:38)

**Services:**
- Master biller details
- Common payment rules
- Payment configuration
- ETE (End-to-End) payment processing

#### Integration Points

| Service | Purpose | Configuration |
|---------|---------|---------------|
| **Payment Service** | Core payment processing | `feign.payment.service.url` |
| **Customer Service** | Customer data retrieval | `feign.customers.service.url` |
| **Account Service** | Account balance inquiry | `feign.account.service.url` |
| **Credit Card Service** | Credit card operations | `feign.credit.card.service.url` |
| **Transfer Service** | Fund transfer operations | `feign.transfer.service.url` |
| **Notification Service** | Push notifications | `feign.notification.service.endpoint` |
| **Customer Transaction Service** | Transaction history | `feign.customers.transaction.service.endpoint` |

### Configuration Properties

```properties
# Payment Service
feign.payment.service.name=payment-service
feign.payment.service.url=http://payment-service.${oneapp.ocp.domain}

# Customer Services
feign.customers.service.name=customer-service
feign.customers.service.url=http://customers-service.${oneapp.ocp.domain}

# Account Services
feign.account.service.name=account-service
feign.account.service.url=http://accounts-service.${oneapp.ocp.domain}
```

---

## Configuration

### Application Configuration

#### Core Settings
```properties
# Server Configuration
server.port=8080
spring.application.name=common-payment-exp

# Redis Configuration
spring.redis.host=***********
spring.redis.port=6379
spring.redis.mode=standalone

# Cache Settings
common.payment.initial.cache.expire.second=600
redis.cache.enabled=true
```

#### Feature Flags
```properties
# Feature Toggles
common.payment.initial.enable.check.toggle.allow.common.payment=true
qr.payment.iso20022.flag=false

# Payment Limits
fr.payment.accumulate.usage.limit=200000
amlo.amount.validate.for.get.tax.id=700000
```

#### Deep Link Configuration
```properties
common.payment.initial.deeplink.url=oneappvit://linkaccess/commonpayment
```

### Environment Profiles
- **Local:** `application-local.properties`
- **Development:** `application-dev.properties`
- **Staging:** `application-stg.properties`
- **Production:** `application-prod.properties`

---

## Security

### Authentication & Authorization

#### JWT Configuration
- **Algorithm:** RS256 (RSA Signature with SHA-256)
- **Key Rotation:** JWK Set with multiple keys
- **Key Management:** Separate signing and encryption keys

#### JWK Set Configuration
**Location:** [`application.properties:148-151`](src/main/resources/application.properties:148)

**Supported Clients:**
- **Shopee:** RSA-OAEP-256 encryption
- **Lazada:** RSA-OAEP-256 encryption

#### Security Headers
- `X-Correlation-ID` - Request tracking
- `X-crmId` - Customer identification
- `X-Partner-Name` - Partner identification
- `X-Forward-For` - IP address tracking

### API Security
- **Rate Limiting:** Configurable per endpoint
- **Input Validation:** Bean validation with custom validators
- **Output Encoding:** JSON sanitization
- **CORS Configuration:** Configurable origins

---

## Caching Strategy

### Redis Cache Implementation

#### Cache Structure
```
Key Format: common_payment:{transaction_id}
Hash Key: common_payment_hash
TTL: 600 seconds (configurable)
```

#### Cache Operations
1. **SET Operation:** Store payment data during initialization
2. **GET Operation:** Retrieve cached payment data
3. **DELETE Operation:** Clean up after completion/failure

#### Cache Service Methods
- `set()` - Store with TTL
- `get()` - Retrieve by key
- `delete()` - Remove cached data

### Cache Configuration
```properties
spring.redis.pool.max-total=8
spring.redis.pool.max-idle=8
spring.redis.pool.min-idle=0
spring.redis.read-from=replicaPreferred
```

---

## Error Handling

### Exception Hierarchy

#### Custom Exceptions
- **TMBCommonException** - Generic business exception
- **TMBCommonExceptionWithResponse** - Exception with response payload
- **ValidationException** - Input validation failure
- **NullPointerCustomException** - Null pointer scenarios

### Error Response Format
```json
{
  "status": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "messageTH": "Thai error message"
  },
  "data": null
}
```

### Error Codes
- `0000` - Success
- `1000` - Validation error
- `2000` - Business rule violation
- `3000` - System error
- `4000` - External service error
- `5000` - Authentication/Authorization error

### Logging Strategy
- **Request/Response Logging:** Inbound/outbound API calls
- **Error Logging:** Structured error tracking
- **Performance Logging:** Execution time monitoring
- **Audit Logging:** Payment transaction trails

---

## Deployment

### Container Configuration
- **Base Image:** OpenJDK 17 Alpine
- **Memory:** 1GB heap, 2GB container
- **CPU:** 1 core minimum, 2 cores recommended
- **Health Check:** `/actuator/health`

### Environment Variables
```bash
# Required
SPRING_PROFILES_ACTIVE=prod
KAFKA_BOOTSTRAP_SERVERS=kafka:9092
REDIS_HOST=redis-cluster

# Optional
LOGGING_LEVEL_ROOT=INFO
LOGGING_LEVEL_COM_TMB=DEBUG
```

### Deployment Checklist
- [ ] Environment configuration validated
- [ ] Database connectivity tested
- [ ] External service endpoints verified
- [ ] SSL certificates configured
- [ ] Monitoring alerts configured
- [ ] Log aggregation enabled

---

## Monitoring & Logging

### Health Check Endpoints
- **Health:** `/actuator/health`
- **Metrics:** `/actuator/metrics`
- **Info:** `/actuator/info`

### Key Metrics
- **Request Rate:** Per endpoint
- **Response Time:** P50, P95, P99
- **Error Rate:** Per error type
- **Cache Hit Rate:** Redis cache efficiency
- **External Service Latency:** Feign client metrics

### Logging Configuration
**Location:** [`logback.xml`](src/main/resources/logback.xml)

**Log Levels:**
- **ERROR:** Critical failures
- **WARN:** Business rule violations
- **INFO:** Transaction milestones
- **DEBUG:** Detailed flow tracking

### Alerting Rules
- **High Error Rate:** > 5% for 5 minutes
- **High Response Time:** P95 > 2 seconds
- **Service Unavailable:** Health check fails
- **Cache Miss Rate:** > 20% sustained

---

## Development Guidelines

### Code Style
- **Java 17** features preferred
- **Lombok** for boilerplate reduction
- **Builder pattern** for complex objects
- **Functional programming** where appropriate

### Testing Strategy
- **Unit Tests:** Service layer with Mockito
- **Integration Tests:** Controller layer with TestRestTemplate
- **Contract Tests:** Feign client validation
- **Performance Tests:** JMeter for load testing

### API Versioning
- **URL Versioning:** `/v1/` prefix
- **Backward Compatibility:** Maintain for 6 months
- **Deprecation Notice:** 3 months advance notice

---

## Appendix

### Related Documentation
- [PRD.md](PRD.md) - Product Requirements Document
- [API Specification](http://localhost:8080/swagger-ui.html)
- [Postman Collection](docs/postman/common-payment.postman_collection.json)

### Support Contacts
- **Technical Lead:** <EMAIL>
- **Product Owner:** <EMAIL>
- **DevOps Team:** <EMAIL>

### Change Log
| Version | Date | Changes |
|---------|------|---------|
| 1.0.0 | 2025-08-02 | Initial TDD document |