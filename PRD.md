# Common Payment Experience - Product Requirements Document

## 1. Executive Summary
The Common Payment Experience project is a comprehensive payment processing system for TMB OneApp that provides unified payment capabilities across multiple channels. This system supports various payment types including bill payments, credit card transactions, fund transfers, and loan payments with a consistent user experience.

### 1.1 Project Scope
- **Payment Types**: Bill payments, credit card payments, PromptPay, e-wallets, auto loans, loan payments
- **Channels**: Mobile banking, internet banking, partner integrations
- **Features**: Validation, confirmation, account management, daily limits, notifications
- **Security**: Common authentication, encryption, fraud detection

### 1.2 Key Objectives
- Provide consistent payment experience across all channels
- Support multiple payment methods with extensible architecture
- Ensure secure transaction processing with compliance standards
- Enable real-time validation and confirmation
- Support partner integrations through APIs

## 2. System Architecture

### 2.1 High-Level Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway                              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┴───────────────────────────────────────┐
│              Common Payment Experience                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Controller    │  │     Service     │  │   Client    │ │
│  │     Layer       │  │     Layer       │  │    Layer    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Validation    │  │   Confirmation  │  │   Account   │ │
│  │   Processors    │  │   Processors    │  │  Management │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Core Components

#### 2.2.1 Controller Layer
- **AccountCommonPaymentController**: Handles account retrieval requests
- **CommonPaymentController**: Manages the end-to-end payment flow.
- **CommonPaymentPartnerIntegrationController**: Handles partner integrations

#### 2.2.2 Service Layer
- **InitializationCommonPaymentService**: Handles the first step of payment initialization.
- **PaymentMethodCommonPaymentService**: Retrieves available payment methods for a transaction.
- **ValidationCommonPaymentService**: Orchestrates payment validation.
- **ConfirmationCommonPaymentService**: Manages payment confirmation.

#### 2.2.3 Processor Layer
Uses Strategy pattern with processor selectors:
- **ValidationCommonPaymentProcessorSelector**: Routes to appropriate validation processor.
- **ConfirmationCommonPaymentProcessorSelector**: Routes to appropriate confirmation processor.
- **AccountCommonPaymentProcessorSelector**: Routes to appropriate account processor.

#### 2.2.4 Client Layer
- **PaymentServiceClient**: Integrates with payment service
- **AccountServiceClient**: Integrates with account service
- **CustomerServiceClient**: Integrates with customer service
- **CreditCardServiceClient**: Integrates with credit card service

## 3. End-to-End Payment Flow
The common payment process is designed as a four-step flow, orchestrated by the `CommonPaymentController`. Each step is a separate API call that builds upon the previous one, using a `transactionId` to maintain state, which is stored in a Redis cache.

### 3.1 Step 1: Initialization (`POST /common-payment/initial`)
- **Purpose**: To begin a new payment transaction, generate a unique transaction ID, and create a cached draft of the transaction details.
- **Service**: `InitializationCommonPaymentService`
- **Process**:
  1. A unique `transactionId` (UUID) is generated for the new transaction.
  2. The service fetches essential configuration data, such as `MasterBillerResponse` (for bill payments) and `CommonPaymentConfig`.
  3. If the request originates from a deep link, it retrieves the associated deep link data from the cache.
  4. The initial request data is validated by `InitializationValidator`.
  5. The system determines the appropriate `processorType` (e.g., `bill_prompt_pay`, `credit_card`) based on the transaction details.
  6. A `CommonPaymentDraftCache` object is created, containing all the initial information.
  7. This cache object is stored in Redis, keyed by the `transactionId`, with a configured TTL.
  8. The API returns a deep link URL that includes the `transactionId`, which the client uses to proceed to the next step.

### 3.2 Step 2: Get Payment Methods (`GET /common-payment/payment-method/{transaction-id}`)
- **Purpose**: To retrieve the list of available payment methods (e.g., deposit accounts, credit cards) for the given transaction.
- **Service**: `PaymentMethodCommonPaymentService`
- **Process**:
  1. The `CommonPaymentDraftCache` is retrieved from Redis using the `transactionId`.
  2. The service asynchronously fetches `CommonPaymentRule`, `MasterBillerResponse`, and `CustomerDetailResponse`.
  3. Based on the `transactionType` in the cache, it calls the relevant service to get the list of source accounts:
     - For `bill_pay`, it uses `BillPayPaymentMethodCommonPaymentHelper`.
     - For `d_statement`, it uses `DStatementAccountCommonPaymentService`.
     - For `issue_debit_card`, it uses `IssueDebitCardAccountCommonPaymentService`.
  4. It evaluates eligibility for Wow Points and Credit Card Points and updates the cache if applicable.
  5. It determines a default payment method.
  6. The API returns a `PaymentMethodCommonPaymentResponse` containing the list of available deposit accounts and/or credit cards, along with UI flags (e.g., `creditCardFlag`, `qrCodeFlag`).

### 3.3 Step 3: Validate Payment (`POST /common-payment/validate`)
- **Purpose**: To validate the selected payment method and transaction details before confirming the payment.
- **Service**: `ValidationCommonPaymentService`
- **Process**:
  1. The service retrieves the `CommonPaymentDraftCache` from Redis.
  2. It uses the `ValidationCommonPaymentProcessorSelector` to route the request to the correct processor based on the `processorType` stored in the cache.
  3. The specific validation processor (e.g., `BillPromptPayValidationProcessor`, `CreditCardValidationProcessor`) executes its validation logic. This typically includes:
     - Fetching necessary data (e.g., account balances, customer profiles).
     - Validating business rules (e.g., daily limits, service hours, biller status).
     - Calling external services for validation (if required).
     - Calculating fees.
     - Determining if additional authentication (Common Auth) is needed.
  4. The cache is updated with the validation results.
  5. The API returns a `ValidationCommonPaymentResponse` with details like the calculated fee, total amount, and whether common authentication is required.

### 3.4 Step 4: Confirm Payment (`POST /common-payment/confirm`)
- **Purpose**: To finalize and execute the payment transaction.
- **Service**: `ConfirmationCommonPaymentService`
- **Process**:
  1. The service retrieves the `CommonPaymentDraftCache` from Redis.
  2. It uses the `ConfirmationCommonPaymentProcessorSelector` to route the request to the correct processor.
  3. The specific confirmation processor (e.g., `BillPromptPayConfirmServiceProcessor`, `CreditCardPaymentConfirmServiceProcessor`) executes the confirmation logic. This includes:
     - Performing final validations (e.g., checking transaction status, re-validating daily limits).
     - Calling external services to execute the financial transaction.
     - Handling the response from the external service.
  4. Upon successful confirmation, the service performs post-transaction tasks:
     - Clears the transaction data from the cache.
     - Updates the customer's daily limit usage.
     - Publishes events to Kafka for activity, financial, and transaction logs.
     - Triggers an e-notification to be sent to the customer.
  5. The API returns a `ConfirmationCommonPaymentResponse` with the final transaction details, such as the reference number and remaining balance.

## 4. Detailed Processor Flows
This section details the specific logic within the validation and confirmation processors for major payment types.

### 4.1 Bill PromptPay Payment
**Processor Type**: `bill_prompt_pay`

#### 4.1.1 Validation Flow
1. **Prepare Data**:
   - Fetch master biller information, bill pay configuration, customer CRM profile, and deposit account details.
   - Fetch customer KYC if the amount exceeds the AML threshold (e.g., 700,000 THB).
2. **Validate Data**:
   - Check if the company code is in an exclusion list.
   - Validate biller expiration and service hours.
   - Validate daily limits.
   - Validate QR channel compatibility (channels 57/97).
3. **External Validation**:
   - Call the ETE service for validation, supporting ISO20022 format for QR payments.
4. **Post-Validation**:
   - Calculate fees (waived for certain accounts).
   - Determine if common authentication is required.
   - Calculate the total amount (amount + fee).

#### 4.1.2 Confirmation Flow
1. **Validate Data**:
   - Verify authentication status and transaction ID.
   - Re-check daily limits.
2. **External Confirmation**:
   - Call the ETE service to confirm the payment.
3. **Post-Confirmation**:
   - Clear cache, update daily limits, send notifications, and generate a QR code for the slip.

### 4.2 Credit Card Payment
**Processor Type**: `credit_card`

#### 4.2.1 Validation Flow
1. **Prepare Data**:
   - Fetch customer KYC, master biller info, customer CRM profile, deposit account details, and target credit card details.
2. **Validate Data**:
   - Validate biller expiration and service hours.
   - Validate daily limits if the payment is not for the user's own card.
   - Check for insufficient funds.
3. **Fee Calculation**:
   - Waive fees for credit card payments and for accounts with a waive flag.
4. **Authentication**:
   - Determine if common authentication is required and calculate accumulated usage.

#### 4.2.2 Confirmation Flow
1. **Validate Data**:
   - Verify authentication and transaction ID.
   - Re-check daily limits if not paying for one's own card.
2. **External Confirmation**:
   - Call the credit card service to confirm the payment.
3. **Post-Confirmation**:
   - Clear transaction cache, update daily limits, send notifications, and clear the credit card-specific cache.

---
*(The rest of the document continues from section 5, Data Models, with appropriate renumbering if needed)*
---

## 5. Data Models

### 5.1 Request Models

#### 5.1.1 Initialization Request
```json
{
  "paymentInformation": {
    "transactionType": "string",
    "compCode": "string",
    "entryId": "string",
    "deepLinkTransactionId": "string",
    "schedule": {
      // Schedule details
    }
  }
}
```

#### 5.1.2 Validation Request
```json
{
  "transactionId": "string",
  "deposit": {
    "accountNumber": "string",
    "amount": "decimal"
  },
  "creditCard": {
    "isPayWithCreditCardFlag": "boolean",
    "amount": "decimal"
  }
}
```

#### 5.1.3 Confirmation Request
```json
{
  "transactionId": "string",
  "customSlip": {
    "backgroundImage": "string",
    "textColor": "string"
  }
}
```

### 5.2 Response Models

#### 5.2.1 Initialization Response
```json
{
  "deeplinkUrl": "string"
}
```

#### 5.2.2 Payment Method Response
```json
{
  "depositAccountList": [],
  "creditCardList": [],
  "creditCardFlag": "boolean",
  "wowPointFlag": "boolean",
  "defaultPaymentOverride": "string",
  "paymentInformation": {}
}
```

#### 5.2.3 Validation Response
```json
{
  "transactionId": "string",
  "amount": "decimal",
  "totalAmount": "decimal",
  "fee": "decimal",
  "isRequireCommonAuthen": "boolean",
  "commonAuthenticationInformation": {
    "totalPaymentAccumulateUsage": "decimal",
    "flowName": "string",
    "featureId": "string",
    "billerCompCode": "string"
  }
}
```

#### 5.2.4 Confirmation Response
```json
{
  "referenceNo": "string",
  "transactionCreatedDatetime": "string",
  "remainingBalance": "decimal",
  "completeScreenDetail": {
    "title": "string",
    "subtitle": "string",
    "description": "string"
  },
  "autoSaveSlip": "boolean",
  "qr": "string"
}
```

## 6. Business Rules

### 6.1 Daily Limits
- **Validation**: Check daily limit exceeded based on biller group type
- **Accumulate Usage**: Update accumulate usage after successful transaction
- **Reset**: Daily limits reset at midnight

### 6.2 Fee Calculation
- **Bill PromptPay**: Fee from ETE service, waived for certain accounts
- **Credit Card**: Fee waived for credit card payments
- **Auto Loan**: Fee from configuration

### 6.3 Authentication Rules
- **Common Authentication**: Required based on amount and customer profile
- **Pin Free**: Check pin free count and update after transaction
- **Threshold**: 700,000 THB for AML/KYC requirements

### 6.4 Service Hours
- **Biller Validation**: Check if biller is within service hours
- **Expiration**: Check if biller is expired
- **Channel Validation**: Validate QR channel availability

## 7. Configuration

### 7.1 Application Properties
```properties
# AML/KYC Threshold
amlo.amount.validate.for.get.tax.id=700000

# QR Payment ISO20022 Flag
qr.payment.iso20022.flag=false

# Cache Configuration
cache.ttl=3600
cache.prefix=common_payment

# Daily Limit Configuration
daily.limit.enabled=true
daily.limit.reset.time=00:00
```

### 7.2 Constants
- **TTB Bank Code**: 011 (3-digit), 811 (ISO20022)
- **Transaction Code Prefix**: 88
- **Reference Sequence Digits**: 8
- **Default Fee**: 0.00

## 8. External Service Integrations

### 8.1 Payment Service
- **Base URL**: Configured via environment
- **Endpoints**:
  - `/validate/bill-prompt-pay`
  - `/confirm/bill-prompt-pay`
  - `/validate/credit-card`
  - `/confirm/credit-card`
  - `/validate/auto-loan`
  - `/confirm/auto-loan`

### 8.2 Account Service
- **Base URL**: Configured via environment
- **Endpoints**:
  - `/accounts/deposit`
  - `/accounts/credit-card`

### 8.3 Customer Service
- **Base URL**: Configured via environment
- **Endpoints**:
  - `/customer/profile`
  - `/customer/kyc`
  - `/customer/crm-profile`

### 8.4 Credit Card Service
- **Base URL**: Configured via environment
- **Endpoints**:
  - `/credit-card/detail`
  - `/credit-card/confirm`

## 9. Security Requirements

### 9.1 Authentication
- **Common Authentication**: Integrated with TMB Common Authentication service
- **Token Validation**: Validate tokens via Common Authentication service
- **Session Management**: Handle session timeouts and refresh

### 9.2 Authorization
- **Role-Based Access**: Check user roles and permissions
- **Feature Access**: Validate feature access via Common Authentication
- **Biller Access**: Check if user has access to specific biller

### 9.3 Data Protection
- **Encryption**: All sensitive data encrypted in transit and at rest
- **Masking**: Mask sensitive information in logs (citizen ID, account numbers)
- **PCI Compliance**: Follow PCI DSS standards for credit card data

### 9.4 Audit Logging
- **Activity Logs**: Track all user activities
- **Financial Logs**: Track all financial transactions
- **Transaction Logs**: Track all transaction details
- **Security Logs**: Track authentication and authorization events

## 10. Error Handling

### 10.1 Error Codes
- **MISSING_REQUIRED_FIELD_ERROR**: 400 Bad Request
- **BILLER_EXPIRED**: 400 Bad Request
- **SERVICE_HOURS_NOT_AVAILABLE**: 400 Bad Request
- **DAILY_LIMIT_EXCEEDED**: 400 Bad Request
- **INSUFFICIENT_FUNDS**: 400 Bad Request
- **TRANSACTION_NOT_FOUND**: 404 Not Found
- **FAILED_V2**: 500 Internal Server Error

### 10.2 Error Response Format
```json
{
  "status": {
    "code": "string",
    "message": "string",
    "service": "string"
  },
  "data": null
}
```

### 10.3 Retry Logic
- **Idempotent Operations**: All operations are idempotent
- **Retry Count**: Configurable retry count
- **Backoff Strategy**: Exponential backoff with jitter

## 11. Testing Strategy

### 11.1 Unit Testing
- **Processor Testing**: Test each processor independently
- **Service Testing**: Test service layer with mocks
- **Controller Testing**: Test controllers with mocked services

### 11.2 Integration Testing
- **End-to-End Testing**: Test complete payment flows
- **External Service Testing**: Test with external service mocks
- **Database Testing**: Test with in-memory database

### 11.3 Performance Testing
- **Load Testing**: Test system under load
- **Stress Testing**: Test system limits
- **Concurrency Testing**: Test concurrent transactions

## 12. Deployment

### 12.1 Container Configuration
```dockerfile
FROM openjdk:17-jdk-slim
COPY target/common-payment-exp.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 12.2 Environment Variables
```bash
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=common_payment
DB_USER=payment_user
DB_PASSWORD=secure_password

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=secure_password

# External Services
PAYMENT_SERVICE_URL=https://payment-service.tmbbank.com
ACCOUNT_SERVICE_URL=https://account-service.tmbbank.com
CUSTOMER_SERVICE_URL=https://customer-service.tmbbank.com
```

### 12.3 Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: common-payment-exp
spec:
  replicas: 3
  selector:
    matchLabels:
      app: common-payment-exp
  template:
    metadata:
      labels:
        app: common-payment-exp
    spec:
      containers:
      - name: common-payment-exp
        image: tmbbank/common-payment-exp:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: common-payment-secrets
              key: db-host
```

## 13. Monitoring and Observability

### 13.1 Metrics
- **Transaction Count**: Number of transactions processed
- **Success Rate**: Percentage of successful transactions
- **Response Time**: Average response time
- **Error Rate**: Percentage of failed transactions

### 13.2 Logging
- **Structured Logging**: Use JSON format for logs
- **Correlation ID**: Track requests across services
- **Log Levels**: DEBUG, INFO, WARN, ERROR

### 13.3 Health Checks
- **Liveness Probe**: Check if application is running
- **Readiness Probe**: Check if application is ready to serve requests
- **Startup Probe**: Check if application has started successfully

## 14. API Documentation

### 14.1 OpenAPI Specification
Available at: `/swagger-ui.html`

### 14.2 Postman Collection
Available at: `/postman-collection.json`

### 14.3 API Versioning
- **Version**: v1
- **Base Path**: `/api/v1`
- **Deprecation Policy**: 6 months notice

## 15. Development Guidelines

### 15.1 Code Standards
- **Java 17**: Use latest Java features
- **Spring Boot 3.x**: Use latest Spring Boot
- **Lombok**: Use for boilerplate code
- **MapStruct**: Use for object mapping

### 15.2 Testing Standards
- **JUnit 5**: Use for unit testing
- **Mockito**: Use for mocking
- **Testcontainers**: Use for integration testing
- **Coverage**: Minimum 80% code coverage

### 15.3 Documentation Standards
- **Javadoc**: Document all public methods
- **README**: Update README for new features
- **CHANGELOG**: Maintain changelog
- **ADR**: Document architectural decisions

## 16. Future Enhancements

### 16.1 Payment Types
- **Digital Wallet**: Support for digital wallets
- **Cryptocurrency**: Support for cryptocurrency payments
- **Buy Now Pay Later**: Support for BNPL services

### 16.2 Features
- **Recurring Payments**: Support for recurring payments
- **Bulk Payments**: Support for bulk payments
- **Payment Scheduling**: Support for payment scheduling

### 16.3 Integrations
- **Third-party Wallets**: Integrate with third-party wallets
- **Payment Gateways**: Integrate with payment gateways
- **Blockchain**: Integrate with blockchain networks

## 17. Kafka Logging and Notifications

### 17.1 Kafka Integration
The system uses Kafka for asynchronous logging and notifications. The `LogEventPublisherService` is responsible for publishing events to the following topics:
- **Activity Log Topic**: `com.tmb.oneapp.common.payment.exp.activity`
- **Financial Log Topic**: `com.tmb.oneapp.common.payment.exp.financial`
- **Transaction Log Topic**: `com.tmb.oneapp.common.payment.exp.transaction`

### 17.2 Activity Logs
- **Purpose**: To track user interactions and key events within the payment flow.
- **Key Data Points**:
  - `activityTypeId`: Unique identifier for the activity.
  - `fromAccount`: The account number from which the payment is made.
  - `paymentMethod`: The method of payment (e.g., deposit).
  - `step`: The stage in the payment process (e.g., Confirm).
  - `reference1`, `reference2`: Biller references.
  - `amount`, `fee`, `totalAmount`: Transaction amounts.
  - `billerName`: The name of the biller.
  - Masked references for sensitive information.

### 17.3 Financial Logs
- **Purpose**: To create an audit trail of all financial transactions for reconciliation and reporting.
- **Key Data Points**:
  - `referenceID`: The unique transaction reference number.
  - `fromAccNo`, `fromAccType`, `fromAccName`: Details of the source account.
  - `toAccNo`, `toAccName`: Details of the destination account.
  - `bankCode`: The bank code of the destination account.
  - `txnAmount`, `txnFee`: The transaction amount and fee.
  - `billerRef1`, `billerRef2`: Biller references.
  - `compCode`: The company code of the biller.

### 17.4 Transaction Logs
- **Purpose**: To provide a summary of the transaction for quick lookups and activity feeds.
- **Key Data Points**:
  - `financialTransferRefId`: The reference ID from the financial log.
  - `fromAccountNickname`, `fromAccountNo`: Nickname and number of the source account.
  - `toAccountNo`: The destination account number.
  - `financialTransferAmount`: The amount of the transaction.
  - `billerNameEn`, `billerNameTh`: Biller names in English and Thai.
  - `billerRef1`, `billerRef2`: Biller references.

### 17.5 E-Notifications
- **Purpose**: To send email notifications to customers upon completion of a transaction.
- **Process**:
  1. The `NotificationCommonPaymentService` is called after a successful transaction.
  2. It checks the customer's notification settings to see if transaction notifications are enabled. Certain templates (e.g., for scheduled payments) bypass this check.
  3. It fetches the customer's KYC information to get their name and email address.
  4. It constructs a `NotificationRequest` containing the transaction details.
  5. The request is sent to the central Notification Service to be delivered to the customer.
- **Key Data Points**:
  - `templateName`: The template to use for the notification (e.g., `oneapp-scheduled-transfer-complete`).
  - `customerName`: The customer's name.
  - `channelName`: The name of the channel (e.g., OneApp).
  - Transaction-specific details like amount, date, and reference number.