# Project Overview: Common Payment Experience (common-payment-exp)

## Project Purpose
This is a Spring Boot-based microservice that provides a common payment experience functionality for the TMB Bank's OneApp platform. The service acts as a central payment orchestration layer that handles various payment workflows including initialization, payment method selection, validation, and confirmation.

## Technology Stack
- **Framework**: Spring Boot 3.3.8 with Spring Cloud
- **Language**: Java 17
- **Build Tool**: Gradle
- **Containerization**: Docker
- **Architecture**: Microservice with Feign clients for service-to-service communication
- **Messaging**: Apache Kafka for event publishing
- **Caching**: Redis
- **API Documentation**: SpringDoc OpenAPI (Swagger)
- **Security**: JWK/JWT for authentication, OAuth2 integration
- **Monitoring**: Micrometer with Prometheus, Actuator endpoints
- **Testing**: JUnit 5, Mockito

## Project Structure
```
src/main/java/com/tmb/oneapp/commonpaymentexp/
├── client/                 # Feign clients for external service integration
├── config/                 # Configuration classes (<PERSON><PERSON>, <PERSON>, <PERSON>wagger, etc.)
├── constant/               # Application constants
├── controller/             # REST API controllers (v1 APIs)
├── exception/              # Custom exception handling
├── logger/                 # Custom logging utilities
├── model/                  # Data transfer objects and domain models
├── service/                # Business logic implementation
├── utils/                  # Utility classes
└── validator/              # Validation logic
```

## Key Dependencies
- Spring Boot Web, Actuator, AOP, Validation
- Spring Cloud OpenFeign for service communication
- Spring Kafka for event streaming
- Spring Data Redis for caching
- TMB Common Libraries for utilities, Redis client, Kafka integration
- Resilience4j for circuit breaking
- Nimbus JOSE JWT for security
- Lombok and MapStruct for code generation
- Google ZXing for QR code generation

## API Endpoints
The service exposes several REST APIs under `/common-payment`:
1. **POST /initial** - Initialize a common payment workflow
2. **GET /payment-method/{transaction-id}** - Get available payment methods
3. **POST /validate** - Validate payment details
4. **POST /confirm** - Confirm and execute payment
5. **GET /toggle** - Get payment toggle configurations

## Building and Running

### Prerequisites
- Java 17 JDK
- Docker (for containerization)
- Access to TMB Nexus repository

### Local Development
```bash
# Run tests
./gradlew test

# Build the application
./gradlew build

# Run the application locally
./gradlew bootRun
```

### Docker Build
```bash
# Build Docker image
./gradlew docker

# Run with Docker
docker run -p 8080:8080 com.tmb.oneapp/common-payment-exp:VERSION
```

### Environment Configuration
The application is configured through:
- `application.properties` - Main configuration file
- `bootstrap.properties` - Bootstrap configuration
- Environment variables for sensitive data

Key configurations include:
- Server port (8080)
- Kafka connection settings
- Redis connection settings
- Feign client service URLs
- JWK sets for authentication

## Development Conventions
- **Package Structure**: Follows domain-driven design with clear separation of concerns
- **Logging**: Uses custom TMBLogger with LogAround and LogExecutionTime annotations
- **Error Handling**: Centralized exception handling with TMBCommonException
- **API Documentation**: OpenAPI 3.0 documentation with detailed examples
- **Security**: Request header validation with correlation ID and CRM ID requirements
- **Testing**: Unit tests with JUnit 5 and Mockito, integration tests where appropriate
- **Code Quality**: SonarQube integration, Jacoco code coverage

## Service Integration
The service integrates with multiple downstream services via Feign clients:
- Customer Account Biz
- Customer Experience Service
- Payment Service
- Retail Lending Biz
- Common Service
- Credit Card Service
- OAuth Service
- Notification Service
- HP Experience Service
- Transfer Service
- Bank Service
- Account Service
- Loyalty Biz

## Event Streaming
The service publishes events to Kafka topics:
- Activity logs
- Financial logs
- Transaction logs

## Caching
Redis is used for:
- Caching payment configurations
- Storing temporary transaction data
- Rate limiting and daily limits

## Security Features
- JWT validation with JWK sets
- Request header validation
- OAuth2 integration
- Data encryption/decryption utilities