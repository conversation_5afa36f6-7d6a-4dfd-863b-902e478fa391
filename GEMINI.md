# GEMINI.md

This document provides an overview of the `common-payment-exp` project, its architecture, and development conventions to guide AI-driven development.

## Project Overview

This project, `common-payment-exp`, is a Spring Boot microservice designed to provide a **Common Payment Experience** for the TMB OneApp. It acts as a backend-for-frontend (BFF) that orchestrates various downstream services to handle different payment types through a unified, multi-step process.

**Key Technologies:**
- **Language:** Java 17
- **Framework:** Spring Boot 3
- **Build Tool:** Gradle
- **Caching:** Redis
- **Messaging:** Kafka
- **API Documentation:** OpenAPI (Swagger)
- **Containerization:** Docker

**Core Architecture:**
The service implements a four-step payment flow, managed by the `CommonPaymentController`:
1.  **Initialization (`/common-payment/initial`):** Starts a payment flow, generates a unique `transactionId`, and returns a deep link.
2.  **Get Payment Methods (`/payment-method/{transaction-id}`):** Fetches available payment methods for the given transaction.
3.  **Validation (`/validate`):** Validates the payment details before execution.
4.  **Confirmation (`/confirm`):** Executes the payment and finalizes the transaction.

The application uses a **Strategy Pattern** to handle different payment types (e.g., `bill_prompt_pay`, `credit_card`, `auto_loan`). A `ProcessorSelector` routes requests to the appropriate implementation based on the payment type stored in a Redis cache (`CommonPaymentDraftCache`), which maintains the state of the transaction.

The service integrates with numerous other internal microservices via **OpenFeign** clients, such as `PaymentService`, `CustomerService`, and `AccountService`.

## Building and Running

### Build
To build the project and run all tests:
```bash
./gradlew build
```

### Testing
To run all unit tests:
```bash
./gradlew test
```

To run a specific test:
```bash
./gradlew test --tests "com.tmb.oneapp.commonpaymentexp.validator.BaseBillPayValidatorTest"
```

### Running the Application
To run the application locally for development:
```bash
./gradlew bootRun
```
The application will start on port `8080` by default, as configured in `application.properties`.

### Docker
To build the Docker image for the service:
```bash
./gradlew docker
```
The `Dockerfile` uses a multi-stage build to compile the application, run SonarQube analysis, and create a final production image.

## Development Conventions

### Code Structure
The project follows a standard Spring Boot structure:
- `src/main/java/com/tmb/oneapp/commonpaymentexp/`
  - `controller/`: API endpoints.
  - `service/`: Business logic, organized by payment step (e.g., `initializationcommonpayment`, `validateioncommonpayment`).
  - `client/`: Feign client interfaces for external service communication.
  - `model/`: Data Transfer Objects (DTOs) and domain models.
  - `validator/`: Business logic validators.
  - `config/`: Spring configuration classes.
  - `constant/`: Application-wide constants.

### Key Libraries and Practices
- **Lombok:** Used extensively to reduce boilerplate code (e.g., `@RequiredArgsConstructor`, `@Data`).
- **MapStruct:** Used for mapping between different DTOs and entity models.
- **TMB Common Utility:** A shared internal library (`com.tmb.common:tmb_common_utility`) provides standardized logging, exception handling, and response models (`TmbServiceResponse`).
- **Testing:** Unit tests are written with **JUnit 5** and **Mockito**. Controller tests use `@WebMvcTest` and `MockMvc`.
- **API Documentation:** Endpoints are documented using OpenAPI 3 annotations (`@Operation`, `@Tag`). The Swagger UI is available at `/swagger-ui.html`.
- **Configuration:** Application configuration is managed in `application.properties` and `bootstrap.properties`, with environment-specific values for service URLs and credentials.
- **Asynchronous Operations:** Kafka is used for publishing events asynchronously (e.g., activity logs, financial logs, payment status updates).
