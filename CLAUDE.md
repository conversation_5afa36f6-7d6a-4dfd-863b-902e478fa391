# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands

### Build and Testing
- **Build project**: `./gradlew build` or `make commit` (runs tests first)
- **Run tests**: `./gradlew test`
- **Run specific test**: `./gradlew test --tests "ClassName.methodName"`
- **Generate coverage report**: `./gradlew jacocoTestReport` (generates reports in `build/reports/jacoco/test/html/`)
- **Run SonarQube analysis**: `./gradlew sonarqube`
- **Create Docker image**: `./gradlew docker`

### Development
- **Run application**: `./gradlew bootRun`
- **Clean build artifacts**: `./gradlew clean`
- **Check dependencies**: `./gradlew dependencies`

## High-Level Architecture

This is a Spring Boot 3.x microservice implementing a **Common Payment Experience** system for TMB OneApp. The service provides unified payment processing across multiple payment types using a processor-based strategy pattern.

### Core Payment Flow
The system implements a **4-step payment process**:
1. **Initialization** (`/common-payment/initial`) - Generate transaction ID and deep link
2. **Payment Methods** (`/payment-method/{transaction-id}`) - Get available payment options  
3. **Validation** (`/validate`) - Validate payment before confirmation
4. **Confirmation** (`/confirm`) - Execute the actual payment

Each step uses a `transactionId` to maintain state in Redis cache (`CommonPaymentDraftCache`).

### Architecture Patterns

#### Strategy Pattern with Processor Selection
- **ValidationCommonPaymentProcessorSelector** - Routes validation to appropriate processor
- **ConfirmationCommonPaymentProcessorSelector** - Routes confirmation to appropriate processor
- **AccountCommonPaymentProcessorSelector** - Routes account operations to appropriate processor

Supported payment types: `bill_prompt_pay`, `credit_card`, `auto_loan`, `ewallet`, `ocp`, `d_statement`, `issue_debit_card`

#### Service Layer Organization
```
service/
├── initializationcommonpayment/     # Step 1: Payment initialization
├── PaymentMethodCommonPaymentService # Step 2: Payment method retrieval
├── validateioncommonpayment/        # Step 3: Payment validation
├── confirmationcommonpayment/       # Step 4: Payment confirmation
├── client/                          # External service integration
├── cache/                           # Redis cache management
├── dailylimit/                      # Daily limit validation and tracking
└── accountcommonpayment/            # Account-specific operations
```

### External Service Integration
Uses **OpenFeign** clients for integration with:
- **PaymentService** - Core payment processing (ETE, master biller, config)
- **CustomerService** - Customer profiles and KYC data  
- **AccountService** - Account balance and details
- **CreditCardService** - Credit card operations
- **TransferService** - Fund transfer operations
- **NotificationService** - E-notifications

Configuration uses environment-based URLs with pattern: `http://{service-name}.${oneapp.ocp.domain}`

### Data Flow & Caching
- **Redis** for transaction state management with 10-minute TTL
- **Kafka** for async logging (activity, financial, transaction logs)
- **JWK Sets** for partner authentication (Shopee, Lazada, Lineman)
- **MapStruct** for object mapping between layers

### Key Business Logic
- **Daily Limits**: Accumulate usage tracking with different strategies per payment type
- **Common Authentication**: Determines when additional auth is required based on amount/profile
- **Service Hours**: Validates biller availability and operating hours
- **Fee Calculation**: Different fee rules per payment type (some waived)
- **QR Code Generation**: For payment slips using ZXing library

### Testing Structure
- Controllers tested with `@WebMvcTest` and MockMvc
- Services tested with `@ExtendWith(MockitoExtension.class)` and mocked dependencies  
- Integration tests use TestContainers for Redis
- Extensive use of test utilities in `TestUtils.java`

### Configuration Management
- Feature flags for payment types and validations
- Environment-specific properties files
- JWK sets embedded in `application.properties`
- Comprehensive Feign client configuration

This architecture supports high scalability through stateless processing, effective caching, and clear separation of concerns between payment types while maintaining a unified API interface.

## TMB Common Utility Framework (`com.tmb.common:tmb_common_utility`)

This project heavily relies on the TMB Common Utility framework which provides standardized patterns and utilities across TMB's microservices ecosystem.

### Core Response Models
- **`TmbServiceResponse<T>`** - Standard API response wrapper with `Status` and generic data payload
- **`TmbOneServiceResponse`** - Alternative response format for specific services
- **`Status`** - Response status containing code, message, service, and description fields
- **`Description`** - Detailed error/info descriptions

### Exception Handling Framework
- **`TMBCommonException`** - Base exception with error codes and HTTP status support
- **`TMBCommonExceptionWithResponse`** - Exception that carries response payload
- **`TMBCustomCommonExceptionWithResponse`** - Custom exception variant
- **`TMBBusinessException`**, **`CacheException`** - Domain-specific exceptions

### Logging Framework (`TMBLogger`)
- **Enhanced logging** with structured event, audit, and payload logging
- **Correlation tracking** with `getCurrentCorrelationId()` and `getCurrentSessionId()`
- **Data masking** capabilities for sensitive information
- **AOP logging** with `@LogAround` annotation
- **Multiple log types**: EVENT, AUDIT, PAYLOAD, ERROR with TTB-specific formatting

### Utility Functions (`TMBUtils`)
#### JSON & Object Conversion
- `convertJavaObjectToString(obj)` - Object to JSON serialization
- `convertStringToJavaObj(response, className)` - JSON to object deserialization
- `convertStringToJavaObjWithTypeRef()` - Type-safe deserialization

#### Date & Time Operations
- **Bangkok timezone as default** (`Asia/Bangkok`)
- `getZonedCurrentInstant()`, `getZonedCurrentLocalDate()` - Timezone-aware current time
- `getFormattedDateByEpoch()`, `getFormattedDateByMillie()` - Date formatting
- `getBankDateByMilli()` - Bank-specific date formatting

#### Security & Encryption
- `aesEncrypt(plainText, privateKey)` - AES 256 encryption
- `aesDecrypt(encryptedData, privateKey)` - AES 256 decryption
- `maskAccountId(accountId)` - Account number masking (shows last 4 digits)

#### Account Processing
- `getAccountType(accountNo)` - Determine account type from account number
- `generateTransactionCode(accountType)` - Generate transaction codes
- Supports: DDA, SDA, CDA, LOC, CCA account types

#### Redis Sequence Generation
- `generateSequenceKey(key, digits)` - Generate sequential IDs with Redis backing
- `generateSequence(key, digits)` - Core sequence generation logic
- 24-hour TTL for sequence counters

### Caching & Transaction Management
- **`Transaction`** - Transaction cache model for Redis storage
- **`TransactionServices`** - Transaction state management utilities
- **`CachePopulator`** - Cache initialization and management

### Security Framework
- **Field-level encryption** with AOP aspects (`@TmbEncryptField`, `@TmbDecryptField`)
- **Request/Response encryption** (`@TmbEncryptResponse`, `@TmbDecryptRequest`)
- **Digital signature validation** (`TmbSignatureValidator`)
- **MD5 hashing utilities** (`Md5Hashing`)

### HTTP & Feign Integration
- **`FeignCommonConfig`** - Standardized Feign client configuration
- **`FeignRequestInterceptor`** - Automatic request header injection
- **`ApiLoggingFilter`** - HTTP request/response logging
- **`CorrelationFilter`** - Correlation ID propagation across service calls

### Domain Models Available
#### Credit Card Models
- `CreditCardDetail`, `CardStatus`, `CardBalances`, `CardInstallment`
- `ActivateCardResponse`, `BlockCardResponse`, `SetPinResponse`

#### Customer Models  
- `CustomerMasterData`, `SourceOfIncome`, `CustomerFavoriteListResponse`
- `Province`, `District`, `Nationality`, `Occupation`

#### Notification Models
- `NotificationRequest`, `NotificationResponse`
- `EmailChannel`, `SmsChannel`, `LineChannel`

#### Common Configuration Models
- `CommonData`, `CommonTime`, `BillerCategory`
- `ServiceHour` validation utilities

### Constants & Configuration
- **`TmbCommonUtilityConstants`** - Centralized constants for:
  - Account types and codes
  - Date formats and timezones  
  - Standard response codes
  - Service configuration
- **`ErrorConstants`** - Standardized error codes

### Best Practices When Using TMB Common Utility

1. **Response Handling**: Always use `TmbServiceResponse<T>` for consistent API responses
2. **Exception Management**: Catch and wrap exceptions with appropriate TMB exception types
3. **Logging**: Use `TMBLogger` instead of standard logging for correlation tracking
4. **Date/Time**: Always use timezone-aware utilities for date operations
5. **Security**: Use provided encryption utilities for sensitive data
6. **Caching**: Leverage `Transaction` model for Redis-based state management
7. **Service Integration**: Use `FeignCommonConfig` for external service calls