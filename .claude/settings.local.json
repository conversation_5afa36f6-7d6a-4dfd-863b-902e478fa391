{"permissions": {"allow": ["Bash(./gradlew test:*)", "<PERSON><PERSON>(make test:*)", "<PERSON><PERSON>(gradle test:*)", "<PERSON><PERSON>(gradle:*)", "<PERSON><PERSON>(./gradlew:*)", "<PERSON><PERSON>(mv:*)", "mcp__ide__getDiagnostics", "Read(//Users/<USER>/.config/**)", "Read(//Users/<USER>/**)", "<PERSON><PERSON>(env)", "Read(//Users/<USER>/**)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(find:*)", "Bash(for file in )", "Bash(\"/Users/<USER>/source/one_common-payment-exp/src/test/java/com/tmb/oneapp/commonpaymentexp/service/confirmationcommonpayment/EWalletConfirmServiceProcessorTest.java\" )", "Bash(\"/Users/<USER>/source/one_common-payment-exp/src/test/java/com/tmb/oneapp/commonpaymentexp/service/confirmationcommonpayment/BillPromptPayConfirmServiceProcessorTest.java\" )", "Bash(\"/Users/<USER>/source/one_common-payment-exp/src/test/java/com/tmb/oneapp/commonpaymentexp/service/confirmationcommonpayment/ocp/custombillpayment/PEABillPaymentConfirmServiceProcessorTest.java\")", "Bash(do)", "<PERSON><PERSON>(echo:*)", "Bash(if grep -q \"private LoyaltyBizService loyaltyBizService;\" \"$file\")", "<PERSON><PERSON>(then)", "Bash(fi)", "Bash(done)", "<PERSON><PERSON>(javac:*)"], "deny": [], "defaultMode": "acceptEdits", "additionalDirectories": ["/Users/<USER>/.gradle/caches", "/Users/<USER>/.claude"]}}